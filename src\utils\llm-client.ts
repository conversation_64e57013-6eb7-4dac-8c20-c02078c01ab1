/**
 * LLM Client for interacting with AI models
 */

import OpenAI from 'openai';
import { logger } from './logger';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface LLMOptions {
  temperature?: number;
  maxTokens?: number;
  model?: string;
  requestId?: string;
}

export async function invokeLLM(
  prompt: string,
  options: LLMOptions = {}
): Promise<string> {
  const {
    temperature = 0.7,
    maxTokens = 4000,
    model = 'gpt-4-turbo-preview',
    requestId = 'unknown'
  } = options;

  try {
    logger.info('Invoking LLM', { 
      requestId, 
      model, 
      temperature, 
      maxTokens,
      promptLength: prompt.length 
    });

    const response = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature,
      max_tokens: maxTokens,
      response_format: { type: 'text' }
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from LLM');
    }

    logger.info('LLM response received', { 
      requestId, 
      responseLength: content.length,
      tokensUsed: response.usage?.total_tokens 
    });

    return content;

  } catch (error) {
    logger.error('LLM invocation failed', { requestId, error });
    throw new Error(`LLM invocation failed: ${error.message}`);
  }
}

export async function invokeLLMWithRetry(
  prompt: string,
  options: LLMOptions = {},
  maxRetries: number = 3
): Promise<string> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await invokeLLM(prompt, {
        ...options,
        requestId: `${options.requestId}-attempt-${attempt}`
      });
    } catch (error) {
      lastError = error;
      logger.warn('LLM invocation attempt failed', { 
        attempt, 
        maxRetries, 
        requestId: options.requestId,
        error: error.message 
      });

      if (attempt < maxRetries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

export async function invokeLLMStreaming(
  prompt: string,
  options: LLMOptions = {},
  onChunk?: (chunk: string) => void
): Promise<string> {
  const {
    temperature = 0.7,
    maxTokens = 4000,
    model = 'gpt-4-turbo-preview',
    requestId = 'unknown'
  } = options;

  try {
    logger.info('Invoking LLM with streaming', { 
      requestId, 
      model, 
      temperature, 
      maxTokens 
    });

    const stream = await openai.chat.completions.create({
      model,
      messages: [
        {
          role: 'system',
          content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature,
      max_tokens: maxTokens,
      stream: true,
    });

    let fullContent = '';

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || '';
      if (content) {
        fullContent += content;
        if (onChunk) {
          onChunk(content);
        }
      }
    }

    logger.info('LLM streaming response completed', { 
      requestId, 
      responseLength: fullContent.length 
    });

    return fullContent;

  } catch (error) {
    logger.error('LLM streaming invocation failed', { requestId, error });
    throw new Error(`LLM streaming invocation failed: ${error.message}`);
  }
}

export function validateLLMResponse(response: string, expectedFormat?: 'json' | 'yaml' | 'markdown'): boolean {
  if (!response || response.trim().length === 0) {
    return false;
  }

  if (expectedFormat === 'json') {
    try {
      JSON.parse(response);
      return true;
    } catch {
      return false;
    }
  }

  if (expectedFormat === 'yaml') {
    // Basic YAML validation
    return response.includes(':') && !response.includes('{');
  }

  if (expectedFormat === 'markdown') {
    // Basic markdown validation
    return response.includes('#') || response.includes('*') || response.includes('-');
  }

  return true;
}

export function extractJSONFromResponse(response: string): any {
  try {
    // Try to parse the entire response as JSON
    return JSON.parse(response);
  } catch {
    // Try to extract JSON from code blocks
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1]);
      } catch {
        // Fall through to next attempt
      }
    }

    // Try to extract JSON from the response
    const jsonStart = response.indexOf('{');
    const jsonEnd = response.lastIndexOf('}');
    
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        return JSON.parse(response.substring(jsonStart, jsonEnd + 1));
      } catch {
        // Fall through to error
      }
    }

    throw new Error('No valid JSON found in response');
  }
}

export function sanitizePrompt(prompt: string): string {
  // Remove potentially harmful content
  return prompt
    .replace(/\b(eval|exec|system|shell)\s*\(/gi, '[SANITIZED]')
    .replace(/\b(rm|del|delete)\s+/gi, '[SANITIZED]')
    .replace(/\b(DROP|DELETE|TRUNCATE)\s+/gi, '[SANITIZED]')
    .trim();
}

export function buildPromptWithContext(
  basePrompt: string,
  context: Record<string, any>,
  maxContextLength: number = 8000
): string {
  let contextString = JSON.stringify(context, null, 2);
  
  // Truncate context if too long
  if (contextString.length > maxContextLength) {
    contextString = contextString.substring(0, maxContextLength) + '\n... [TRUNCATED]';
  }

  return `${basePrompt}\n\nContext:\n${contextString}`;
}

export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

export function splitLargePrompt(
  prompt: string,
  maxTokens: number = 3000
): string[] {
  const maxChars = maxTokens * 4;
  const chunks: string[] = [];
  
  if (prompt.length <= maxChars) {
    return [prompt];
  }

  // Split by paragraphs first
  const paragraphs = prompt.split('\n\n');
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    if ((currentChunk + paragraph).length <= maxChars) {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
        currentChunk = paragraph;
      } else {
        // Paragraph is too long, split by sentences
        const sentences = paragraph.split('. ');
        for (const sentence of sentences) {
          if ((currentChunk + sentence).length <= maxChars) {
            currentChunk += (currentChunk ? '. ' : '') + sentence;
          } else {
            if (currentChunk) {
              chunks.push(currentChunk);
            }
            currentChunk = sentence;
          }
        }
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk);
  }

  return chunks;
}
