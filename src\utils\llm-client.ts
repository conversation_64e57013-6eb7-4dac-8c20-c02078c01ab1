/**
 * LLM Client for interacting with AI models (Gemini and DeepSeek R1)
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from './logger';

// Initialize Gemini client
const gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// DeepSeek R1 client configuration
const DEEPSEEK_API_BASE = 'https://api.deepseek.com/v1';
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '';

export interface LLMOptions {
  temperature?: number;
  maxTokens?: number;
  model?: 'gemini' | 'deepseek' | 'auto';
  requestId?: string;
  provider?: 'gemini' | 'deepseek';
}

export async function invokeLLM(
  prompt: string,
  options: LLMOptions = {}
): Promise<string> {
  const {
    temperature = 0.7,
    maxTokens = 4000,
    model = 'auto',
    requestId = 'unknown'
  } = options;

  // Determine which provider to use
  const provider = determineProvider(model, prompt);

  try {
    logger.info('Invoking LLM', {
      requestId,
      provider,
      model,
      temperature,
      maxTokens,
      promptLength: prompt.length
    });

    let content: string;

    if (provider === 'gemini') {
      content = await invokeGemini(prompt, { temperature, maxTokens, requestId });
    } else {
      content = await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
    }

    logger.info('LLM response received', {
      requestId,
      provider,
      responseLength: content.length
    });

    return content;

  } catch (error) {
    logger.error('LLM invocation failed', { requestId, provider, error });

    // Fallback to alternative provider if primary fails
    if (provider === 'gemini' && DEEPSEEK_API_KEY) {
      logger.info('Falling back to DeepSeek', { requestId });
      return await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
    } else if (provider === 'deepseek' && process.env.GEMINI_API_KEY) {
      logger.info('Falling back to Gemini', { requestId });
      return await invokeGemini(prompt, { temperature, maxTokens, requestId });
    }

    throw new Error(`LLM invocation failed: ${error.message}`);
  }
}

async function invokeGemini(
  prompt: string,
  options: { temperature?: number; maxTokens?: number; requestId?: string }
): Promise<string> {
  const model = gemini.getGenerativeModel({
    model: 'gemini-1.5-pro',
    generationConfig: {
      temperature: options.temperature || 0.7,
      maxOutputTokens: options.maxTokens || 4000,
    }
  });

  const systemPrompt = 'You are an expert software architect and developer. Provide detailed, production-ready solutions in JSON format when requested.';
  const fullPrompt = `${systemPrompt}\n\n${prompt}`;

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;
  const content = response.text();

  if (!content) {
    throw new Error('No content received from Gemini');
  }

  return content;
}

async function invokeDeepSeek(
  prompt: string,
  options: { temperature?: number; maxTokens?: number; requestId?: string }
): Promise<string> {
  const response = await fetch(`${DEEPSEEK_API_BASE}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
    },
    body: JSON.stringify({
      model: 'deepseek-reasoner',
      messages: [
        {
          role: 'system',
          content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 4000,
    }),
  });

  if (!response.ok) {
    throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  const content = data.choices?.[0]?.message?.content;

  if (!content) {
    throw new Error('No content received from DeepSeek');
  }

  return content;
}

function determineProvider(model: string, prompt: string): 'gemini' | 'deepseek' {
  // If specific model requested
  if (model === 'gemini') return 'gemini';
  if (model === 'deepseek') return 'deepseek';

  // Auto-selection based on prompt characteristics and availability
  if (!process.env.GEMINI_API_KEY && DEEPSEEK_API_KEY) return 'deepseek';
  if (process.env.GEMINI_API_KEY && !DEEPSEEK_API_KEY) return 'gemini';

  // Both available - choose based on task type
  if (prompt.includes('reasoning') || prompt.includes('analysis') || prompt.includes('review')) {
    return 'deepseek'; // DeepSeek R1 is better for reasoning tasks
  }

  return 'gemini'; // Gemini for general tasks
}

export async function invokeLLMWithRetry(
  prompt: string,
  options: LLMOptions = {},
  maxRetries: number = 3
): Promise<string> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await invokeLLM(prompt, {
        ...options,
        requestId: `${options.requestId}-attempt-${attempt}`
      });
    } catch (error) {
      lastError = error;
      logger.warn('LLM invocation attempt failed', {
        attempt,
        maxRetries,
        requestId: options.requestId,
        error: error.message
      });

      if (attempt < maxRetries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

export async function invokeLLMStreaming(
  prompt: string,
  options: LLMOptions = {},
  onChunk?: (chunk: string) => void
): Promise<string> {
  const {
    temperature = 0.7,
    maxTokens = 4000,
    model = 'auto',
    requestId = 'unknown'
  } = options;

  const provider = determineProvider(model, prompt);

  try {
    logger.info('Invoking LLM with streaming', {
      requestId,
      provider,
      temperature,
      maxTokens
    });

    if (provider === 'gemini') {
      // Gemini doesn't support streaming in the same way, so we'll simulate it
      const content = await invokeGemini(prompt, { temperature, maxTokens, requestId });

      // Simulate streaming by chunking the response
      const chunkSize = 50;
      let fullContent = '';

      for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.slice(i, i + chunkSize);
        fullContent += chunk;
        if (onChunk) {
          onChunk(chunk);
        }
        // Small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      return fullContent;
    } else {
      // DeepSeek streaming implementation
      return await invokeDeepSeekStreaming(prompt, { temperature, maxTokens, requestId }, onChunk);
    }

  } catch (error) {
    logger.error('LLM streaming invocation failed', { requestId, provider, error });
    throw new Error(`LLM streaming invocation failed: ${error.message}`);
  }
}

async function invokeDeepSeekStreaming(
  prompt: string,
  options: { temperature?: number; maxTokens?: number; requestId?: string },
  onChunk?: (chunk: string) => void
): Promise<string> {
  const response = await fetch(`${DEEPSEEK_API_BASE}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
    },
    body: JSON.stringify({
      model: 'deepseek-reasoner',
      messages: [
        {
          role: 'system',
          content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 4000,
      stream: true,
    }),
  });

  if (!response.ok) {
    throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
  }

  let fullContent = '';
  const reader = response.body?.getReader();

  if (!reader) {
    throw new Error('No response body reader available');
  }

  try {
    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      const chunk = new TextDecoder().decode(value);
      const lines = chunk.split('\n').filter(line => line.trim() !== '');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') continue;

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content || '';
            if (content) {
              fullContent += content;
              if (onChunk) {
                onChunk(content);
              }
            }
          } catch (e) {
            // Ignore parsing errors for malformed chunks
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }

  return fullContent;
}

export function validateLLMResponse(response: string, expectedFormat?: 'json' | 'yaml' | 'markdown'): boolean {
  if (!response || response.trim().length === 0) {
    return false;
  }

  if (expectedFormat === 'json') {
    try {
      JSON.parse(response);
      return true;
    } catch {
      return false;
    }
  }

  if (expectedFormat === 'yaml') {
    // Basic YAML validation
    return response.includes(':') && !response.includes('{');
  }

  if (expectedFormat === 'markdown') {
    // Basic markdown validation
    return response.includes('#') || response.includes('*') || response.includes('-');
  }

  return true;
}

export function extractJSONFromResponse(response: string): any {
  try {
    // Try to parse the entire response as JSON
    return JSON.parse(response);
  } catch {
    // Try to extract JSON from code blocks
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1]);
      } catch {
        // Fall through to next attempt
      }
    }

    // Try to extract JSON from the response
    const jsonStart = response.indexOf('{');
    const jsonEnd = response.lastIndexOf('}');
    
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        return JSON.parse(response.substring(jsonStart, jsonEnd + 1));
      } catch {
        // Fall through to error
      }
    }

    throw new Error('No valid JSON found in response');
  }
}

export function sanitizePrompt(prompt: string): string {
  // Remove potentially harmful content
  return prompt
    .replace(/\b(eval|exec|system|shell)\s*\(/gi, '[SANITIZED]')
    .replace(/\b(rm|del|delete)\s+/gi, '[SANITIZED]')
    .replace(/\b(DROP|DELETE|TRUNCATE)\s+/gi, '[SANITIZED]')
    .trim();
}

export function buildPromptWithContext(
  basePrompt: string,
  context: Record<string, any>,
  maxContextLength: number = 8000
): string {
  let contextString = JSON.stringify(context, null, 2);
  
  // Truncate context if too long
  if (contextString.length > maxContextLength) {
    contextString = contextString.substring(0, maxContextLength) + '\n... [TRUNCATED]';
  }

  return `${basePrompt}\n\nContext:\n${contextString}`;
}

export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

export function splitLargePrompt(
  prompt: string,
  maxTokens: number = 3000
): string[] {
  const maxChars = maxTokens * 4;
  const chunks: string[] = [];
  
  if (prompt.length <= maxChars) {
    return [prompt];
  }

  // Split by paragraphs first
  const paragraphs = prompt.split('\n\n');
  let currentChunk = '';

  for (const paragraph of paragraphs) {
    if ((currentChunk + paragraph).length <= maxChars) {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
        currentChunk = paragraph;
      } else {
        // Paragraph is too long, split by sentences
        const sentences = paragraph.split('. ');
        for (const sentence of sentences) {
          if ((currentChunk + sentence).length <= maxChars) {
            currentChunk += (currentChunk ? '. ' : '') + sentence;
          } else {
            if (currentChunk) {
              chunks.push(currentChunk);
            }
            currentChunk = sentence;
          }
        }
      }
    }
  }

  if (currentChunk) {
    chunks.push(currentChunk);
  }

  return chunks;
}
