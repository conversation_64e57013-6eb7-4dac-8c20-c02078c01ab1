/**
 * Static analysis utilities
 */

import { GeneratedFile, CodeError, Suggestion } from '../types';

export interface StaticAnalysisResult {
  errors: CodeError[];
  suggestions: Suggestion[];
  metrics: {
    complexity: number;
    maintainability: number;
    testCoverage: number;
  };
}

export async function runStaticAnalysis(files: GeneratedFile[]): Promise<StaticAnalysisResult> {
  const errors: CodeError[] = [];
  const suggestions: Suggestion[] = [];
  let totalComplexity = 0;
  let totalMaintainability = 0;

  for (const file of files) {
    const fileAnalysis = analyzeFile(file);
    errors.push(...fileAnalysis.errors);
    suggestions.push(...fileAnalysis.suggestions);
    totalComplexity += fileAnalysis.complexity;
    totalMaintainability += fileAnalysis.maintainability;
  }

  const avgMaintainability = files.length > 0 ? totalMaintainability / files.length : 0;
  const testCoverage = calculateTestCoverage(files);

  return {
    errors,
    suggestions,
    metrics: {
      complexity: totalComplexity,
      maintainability: avgMaintainability,
      testCoverage,
    },
  };
}

function analyzeFile(file: GeneratedFile): {
  errors: CodeError[];
  suggestions: Suggestion[];
  complexity: number;
  maintainability: number;
} {
  const errors: CodeError[] = [];
  const suggestions: Suggestion[] = [];

  // Calculate complexity
  const complexity = calculateCyclomaticComplexity(file.content);
  const maintainability = calculateMaintainabilityScore(file);

  // Analyze based on file type
  switch (file.language.toLowerCase()) {
    case 'typescript':
    case 'javascript':
      analyzeJavaScriptFile(file, errors, suggestions);
      break;
    case 'python':
      analyzePythonFile(file, errors, suggestions);
      break;
    case 'html':
      analyzeHTMLFile(file, errors, suggestions);
      break;
    case 'css':
      analyzeCSSFile(file, errors, suggestions);
      break;
  }

  // General code quality checks
  analyzeCodeQuality(file, errors, suggestions);

  return { errors, suggestions, complexity, maintainability };
}

function calculateCyclomaticComplexity(content: string): number {
  let complexity = 1; // Base complexity

  const complexityPatterns = [
    /\bif\b/g,
    /\belse\s+if\b/g,
    /\bwhile\b/g,
    /\bfor\b/g,
    /\bdo\b/g,
    /\bswitch\b/g,
    /\bcase\b/g,
    /\bcatch\b/g,
    /\b&&\b/g,
    /\b\|\|\b/g,
    /\?\s*:/g, // Ternary operator
    /\bbreak\b/g,
    /\bcontinue\b/g,
  ];

  for (const pattern of complexityPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      complexity += matches.length;
    }
  }

  return complexity;
}

function calculateMaintainabilityScore(file: GeneratedFile): number {
  const lines = file.content.split('\n');
  const linesOfCode = lines.filter(line => {
    const trimmed = line.trim();
    return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('/*');
  }).length;

  const complexity = calculateCyclomaticComplexity(file.content);
  const commentRatio = calculateCommentRatio(file.content);
  
  // Simplified maintainability calculation
  let score = 100;
  
  // Penalize high complexity
  if (complexity > 10) score -= (complexity - 10) * 2;
  
  // Penalize long files
  if (linesOfCode > 200) score -= (linesOfCode - 200) * 0.1;
  
  // Reward good commenting
  score += commentRatio * 10;
  
  return Math.max(0, Math.min(100, score));
}

function calculateCommentRatio(content: string): number {
  const lines = content.split('\n');
  const commentLines = lines.filter(line => {
    const trimmed = line.trim();
    return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*');
  }).length;
  
  return lines.length > 0 ? commentLines / lines.length : 0;
}

function calculateTestCoverage(files: GeneratedFile[]): number {
  const sourceFiles = files.filter(f => f.type === 'source');
  const testFiles = files.filter(f => f.type === 'test' || f.path.includes('.test.') || f.path.includes('.spec.'));
  
  if (sourceFiles.length === 0) return 0;
  
  // Simple heuristic: test coverage based on ratio of test files to source files
  const coverage = Math.min(100, (testFiles.length / sourceFiles.length) * 100);
  return coverage;
}

function analyzeJavaScriptFile(file: GeneratedFile, errors: CodeError[], suggestions: Suggestion[]): void {
  const content = file.content;
  const lines = content.split('\n');

  // Check for function length
  const functions = content.match(/function\s+\w+\s*\([^)]*\)\s*\{/g) || [];
  functions.forEach(() => {
    // This is a simplified check - in reality, you'd parse the AST
    const avgFunctionLength = content.length / Math.max(functions.length, 1);
    if (avgFunctionLength > 500) {
      suggestions.push({
        type: 'refactor',
        description: 'Consider breaking down large functions into smaller ones',
        impact: 'medium',
        effort: 'medium',
      });
    }
  });

  // Check for missing JSDoc
  const exportedFunctions = content.match(/export\s+(function|const\s+\w+\s*=)/g) || [];
  const jsdocComments = content.match(/\/\*\*[\s\S]*?\*\//g) || [];
  
  if (exportedFunctions.length > jsdocComments.length) {
    suggestions.push({
      type: 'improvement',
      description: 'Add JSDoc comments to exported functions',
      impact: 'low',
      effort: 'low',
    });
  }

  // Check for error handling
  const asyncFunctions = content.match(/async\s+function|async\s+\(/g) || [];
  const tryBlocks = content.match(/try\s*\{/g) || [];
  
  if (asyncFunctions.length > tryBlocks.length) {
    errors.push({
      severity: 'major',
      message: 'Async functions should include error handling',
      file: file.path,
      rule: 'missing_error_handling',
    });
  }

  // Check for security issues
  if (content.includes('eval(')) {
    errors.push({
      severity: 'critical',
      message: 'Use of eval() is dangerous and should be avoided',
      file: file.path,
      rule: 'security_eval',
    });
  }

  if (content.includes('innerHTML') && !content.includes('sanitize')) {
    errors.push({
      severity: 'major',
      message: 'innerHTML usage without sanitization can lead to XSS',
      file: file.path,
      rule: 'security_xss',
    });
  }
}

function analyzePythonFile(file: GeneratedFile, errors: CodeError[], suggestions: Suggestion[]): void {
  const content = file.content;

  // Check for proper imports
  if (content.includes('import *')) {
    suggestions.push({
      type: 'improvement',
      description: 'Avoid wildcard imports, import specific modules instead',
      impact: 'low',
      effort: 'low',
    });
  }

  // Check for proper exception handling
  if (content.includes('except:') && !content.includes('except Exception:')) {
    errors.push({
      severity: 'major',
      message: 'Bare except clauses should be avoided',
      file: file.path,
      rule: 'bare_except',
    });
  }

  // Check for docstrings
  const functionDefs = content.match(/def\s+\w+\s*\(/g) || [];
  const docstrings = content.match(/""".+?"""/gs) || [];
  
  if (functionDefs.length > docstrings.length) {
    suggestions.push({
      type: 'improvement',
      description: 'Add docstrings to functions for better documentation',
      impact: 'low',
      effort: 'low',
    });
  }
}

function analyzeHTMLFile(file: GeneratedFile, errors: CodeError[], suggestions: Suggestion[]): void {
  const content = file.content;

  // Check for accessibility
  const imgTags = content.match(/<img[^>]*>/g) || [];
  const imgsWithoutAlt = imgTags.filter(tag => !tag.includes('alt='));
  
  if (imgsWithoutAlt.length > 0) {
    errors.push({
      severity: 'minor',
      message: 'Images should have alt attributes for accessibility',
      file: file.path,
      rule: 'accessibility_alt',
    });
  }

  // Check for semantic HTML
  if (!content.includes('<main>') && !content.includes('<section>') && !content.includes('<article>')) {
    suggestions.push({
      type: 'improvement',
      description: 'Use semantic HTML elements for better structure',
      impact: 'medium',
      effort: 'low',
    });
  }

  // Check for meta tags
  if (content.includes('<html>') && !content.includes('<meta name="viewport"')) {
    suggestions.push({
      type: 'improvement',
      description: 'Add viewport meta tag for responsive design',
      impact: 'medium',
      effort: 'low',
    });
  }
}

function analyzeCSSFile(file: GeneratedFile, errors: CodeError[], suggestions: Suggestion[]): void {
  const content = file.content;

  // Check for vendor prefixes
  const vendorPrefixes = ['-webkit-', '-moz-', '-ms-', '-o-'];
  let hasVendorPrefixes = false;
  
  vendorPrefixes.forEach(prefix => {
    if (content.includes(prefix)) {
      hasVendorPrefixes = true;
    }
  });

  if (hasVendorPrefixes) {
    suggestions.push({
      type: 'optimization',
      description: 'Consider using autoprefixer instead of manual vendor prefixes',
      impact: 'low',
      effort: 'low',
    });
  }

  // Check for magic numbers
  const magicNumbers = content.match(/:\s*\d+px/g) || [];
  if (magicNumbers.length > 10) {
    suggestions.push({
      type: 'refactor',
      description: 'Consider using CSS variables for repeated values',
      impact: 'medium',
      effort: 'medium',
    });
  }

  // Check for !important overuse
  const importantCount = (content.match(/!important/g) || []).length;
  if (importantCount > 5) {
    suggestions.push({
      type: 'refactor',
      description: 'Reduce use of !important by improving CSS specificity',
      impact: 'medium',
      effort: 'high',
    });
  }
}

function analyzeCodeQuality(file: GeneratedFile, errors: CodeError[], suggestions: Suggestion[]): void {
  const lines = file.content.split('\n');
  
  // Check for very long lines
  lines.forEach((line, index) => {
    if (line.length > 120) {
      suggestions.push({
        type: 'improvement',
        description: `Line ${index + 1} is too long (${line.length} characters)`,
        impact: 'low',
        effort: 'low',
      });
    }
  });

  // Check for TODO/FIXME comments
  const todoComments = file.content.match(/\/\/\s*(TODO|FIXME|HACK)/gi) || [];
  if (todoComments.length > 0) {
    suggestions.push({
      type: 'improvement',
      description: `Found ${todoComments.length} TODO/FIXME comments that should be addressed`,
      impact: 'low',
      effort: 'medium',
    });
  }

  // Check file size
  if (file.content.length > 10000) {
    suggestions.push({
      type: 'refactor',
      description: 'File is quite large, consider splitting into smaller modules',
      impact: 'medium',
      effort: 'high',
    });
  }
}
