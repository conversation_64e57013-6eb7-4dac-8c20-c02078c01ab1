# AI Agent Workflow System - Deployment Guide

## Quick Start Deployment

### Prerequisites Checklist

- [ ] Node.js 18+ installed
- [ ] AWS CLI configured with appropriate permissions
- [ ] AWS CDK CLI installed (`npm install -g aws-cdk`)
- [ ] Google Gemini API key OR DeepSeek API key (at least one required)
- [ ] Git repository access

### Step-by-Step Deployment

#### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <your-repository-url>
cd ai-agent-workflow-serverless

# Install dependencies
npm install

# Copy environment template
cp .env.example .env
```

#### 2. Configure Environment Variables

Edit `.env` file with your configuration:

```bash
# Required: At least one AI provider API key
GEMINI_API_KEY=your-gemini-api-key-here
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# Required: AWS Configuration
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1

# Optional: Notification Configuration
NOTIFICATION_EMAIL=<EMAIL>

# Optional: Environment
ENVIRONMENT=development
```

#### 3. Deploy Infrastructure

```bash
# Make deployment script executable (Linux/Mac)
chmod +x scripts/deploy.sh

# Run deployment script
./scripts/deploy.sh

# Or deploy manually
npm run bootstrap  # First time only
npm run build
npm test
npm run deploy
```

#### 4. Test Deployment

```bash
# Set environment variables from deployment output
export API_ENDPOINT="https://your-api-id.execute-api.region.amazonaws.com/prod"
export API_KEY="your-api-key-from-output"

# Make test script executable
chmod +x scripts/test-api.sh

# Run API tests
./scripts/test-api.sh
```

## API Keys Setup

### Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key and set it as `GEMINI_API_KEY`

### DeepSeek API Key

1. Go to [DeepSeek Platform](https://platform.deepseek.com/)
2. Sign up and navigate to API Keys
3. Create a new API key
4. Copy the key and set it as `DEEPSEEK_API_KEY`

## AWS Permissions

### Required IAM Permissions

Your AWS user/role needs the following permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "cloudformation:*",
        "lambda:*",
        "dynamodb:*",
        "s3:*",
        "apigateway:*",
        "states:*",
        "iam:*",
        "logs:*",
        "cloudwatch:*",
        "xray:*",
        "kms:*",
        "sns:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### CDK Bootstrap

First-time CDK setup in your AWS account:

```bash
# Bootstrap CDK (one-time setup per account/region)
npx cdk bootstrap aws://ACCOUNT-NUMBER/REGION

# Example
npx cdk bootstrap aws://************/us-east-1
```

## Environment-Specific Deployment

### Development Environment

```bash
export ENVIRONMENT=development
npm run deploy
```

### Staging Environment

```bash
export ENVIRONMENT=staging
npm run deploy
```

### Production Environment

```bash
export ENVIRONMENT=production
npm run deploy
```

## API Usage Examples

### 1. Health Check

```bash
curl -X GET $API_ENDPOINT/status
```

Expected Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0"
}
```

### 2. Description Enhancement

```bash
curl -X POST $API_ENDPOINT/enhance \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -d '{
    "userDescription": "Create a modern e-commerce website with user authentication, product catalog, shopping cart, and payment integration using Stripe",
    "preferences": {
      "techStack": ["react", "next.js", "typescript"],
      "deploymentTarget": "aws",
      "includeTests": true,
      "includeCICD": true
    }
  }'
```

### 3. Complete Workflow

```bash
curl -X POST $API_ENDPOINT/workflow \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -d '{
    "userDescription": "Create a blog platform with user authentication, post creation, comments, and admin dashboard",
    "preferences": {
      "techStack": ["react", "express", "postgresql"],
      "deploymentTarget": "aws",
      "includeTests": true,
      "includeCICD": true
    }
  }'
```

## Monitoring and Troubleshooting

### CloudWatch Dashboard

Access your CloudWatch dashboard:
1. Go to AWS Console → CloudWatch → Dashboards
2. Find "AI-Agent-Workflow-System" dashboard
3. Monitor key metrics:
   - API Gateway requests/errors
   - Lambda function performance
   - Step Functions execution status
   - DynamoDB metrics

### Common Issues and Solutions

#### 1. Deployment Fails

**Issue**: CDK deployment fails with permission errors
**Solution**: 
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify CDK bootstrap
aws cloudformation describe-stacks --stack-name CDKToolkit
```

#### 2. API Returns 403 Forbidden

**Issue**: API key authentication fails
**Solution**:
```bash
# Get API key from CloudFormation outputs
aws cloudformation describe-stacks \
  --stack-name AIAgentWorkflowStack \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiKey`].OutputValue' \
  --output text
```

#### 3. Lambda Timeout Errors

**Issue**: Lambda functions timing out
**Solution**:
- Check CloudWatch logs for specific errors
- Increase timeout in CDK configuration
- Optimize AI prompts for faster responses

#### 4. DynamoDB Throttling

**Issue**: DynamoDB read/write capacity exceeded
**Solution**:
- Monitor DynamoDB metrics in CloudWatch
- Consider switching to on-demand billing
- Optimize query patterns

### Log Analysis

```bash
# View Lambda logs
aws logs tail /aws/lambda/AIAgentWorkflowStack-DescriptionEnhancerFunction --follow

# View Step Functions execution logs
aws stepfunctions describe-execution --execution-arn <execution-arn>

# View API Gateway logs
aws logs tail API-Gateway-Execution-Logs_<api-id>/prod --follow
```

## Performance Optimization

### Cost Optimization

1. **Lambda Memory Allocation**
   - Monitor memory usage in CloudWatch
   - Adjust memory settings in CDK configuration
   - Balance cost vs. performance

2. **DynamoDB Optimization**
   - Use on-demand billing for variable workloads
   - Implement TTL for automatic cleanup
   - Optimize query patterns

3. **S3 Storage Management**
   - Configure lifecycle policies
   - Use appropriate storage classes
   - Enable compression for artifacts

### Performance Tuning

1. **AI Provider Selection**
   - Monitor response times for each provider
   - Adjust provider selection logic
   - Implement caching for repeated requests

2. **Parallel Processing**
   - Optimize Step Functions for parallel execution
   - Use Map states for concurrent task processing
   - Balance concurrency limits

## Security Best Practices

### API Security

1. **API Key Management**
   - Rotate API keys regularly
   - Use different keys for different environments
   - Monitor API key usage

2. **Rate Limiting**
   - Configure appropriate throttling limits
   - Monitor for abuse patterns
   - Implement circuit breakers

### Data Security

1. **Encryption**
   - Verify KMS encryption is enabled
   - Use TLS for all communications
   - Encrypt sensitive environment variables

2. **Access Control**
   - Follow least privilege principle
   - Regular IAM role audits
   - Enable CloudTrail for audit logging

## Backup and Recovery

### Automated Backups

1. **DynamoDB**
   - Point-in-time recovery enabled
   - Regular backup verification
   - Cross-region replication for critical data

2. **S3 Artifacts**
   - Versioning enabled
   - Cross-region replication
   - Lifecycle policies for cost optimization

### Disaster Recovery

1. **Infrastructure Recovery**
   - Infrastructure as Code (CDK)
   - Version-controlled configurations
   - Automated deployment pipelines

2. **Data Recovery**
   - Regular backup testing
   - Recovery time objectives (RTO)
   - Recovery point objectives (RPO)

## Scaling Considerations

### Horizontal Scaling

1. **Lambda Concurrency**
   - Monitor concurrent executions
   - Set appropriate reserved concurrency
   - Handle throttling gracefully

2. **DynamoDB Scaling**
   - Use auto-scaling or on-demand
   - Monitor hot partitions
   - Optimize partition key design

### Vertical Scaling

1. **Memory Allocation**
   - Profile memory usage
   - Optimize for cost/performance balance
   - Consider provisioned concurrency for consistent performance

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review CloudWatch alarms
   - Check error rates and performance metrics
   - Verify backup integrity

2. **Monthly**
   - Update dependencies
   - Review and rotate API keys
   - Analyze cost optimization opportunities

3. **Quarterly**
   - Security audit and penetration testing
   - Disaster recovery testing
   - Performance benchmarking

### Updates and Upgrades

1. **Dependency Updates**
   ```bash
   npm audit
   npm update
   npm run test
   npm run deploy
   ```

2. **CDK Updates**
   ```bash
   npm install -g aws-cdk@latest
   npm install aws-cdk-lib@latest
   npm run deploy
   ```

## Support and Resources

### Documentation
- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS Step Functions Guide](https://docs.aws.amazon.com/step-functions/)
- [Google Gemini API Docs](https://ai.google.dev/docs)
- [DeepSeek API Documentation](https://platform.deepseek.com/api-docs)

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Stack Overflow for technical questions

### Professional Support
- AWS Support for infrastructure issues
- Custom development and consulting available
