/**
 * Jest test setup configuration
 */

import { config } from 'dotenv';

// Load environment variables for testing
config({ path: '.env.test' });

// Set default test environment variables
process.env.NODE_ENV = 'test';
process.env.AWS_REGION = process.env.AWS_REGION || 'us-east-1';
process.env.WORKFLOW_TABLE_NAME = process.env.WORKFLOW_TABLE_NAME || 'test-workflow-table';
process.env.ARTIFACTS_BUCKET_NAME = process.env.ARTIFACTS_BUCKET_NAME || 'test-artifacts-bucket';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Mock AWS SDK
jest.mock('@aws-sdk/client-dynamodb');
jest.mock('@aws-sdk/lib-dynamodb');
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/client-stepfunctions');

// Mock OpenAI
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [
              {
                message: {
                  content: JSON.stringify({
                    specification: {
                      projectName: 'test-project',
                      description: 'Test project description',
                      features: [],
                      techStack: {
                        frontend: { framework: 'React', language: 'TypeScript' },
                        backend: { framework: 'Express', language: 'Node.js' },
                        infrastructure: { cloud: 'AWS' },
                      },
                      architecture: { type: 'serverless', components: [], integrations: [] },
                      dataModels: [],
                      apiEndpoints: [],
                      uiComponents: [],
                      deploymentConfig: { environment: 'development', environmentVariables: {} },
                      metadata: {
                        createdAt: new Date().toISOString(),
                        version: '1.0.0',
                        estimatedComplexity: 'medium',
                      },
                    },
                    taskPlan: {
                      tasks: [
                        {
                          taskId: 'test-task-1',
                          taskName: 'Test Task',
                          description: 'Test task description',
                          agent: 'CodeCreator',
                          dependencies: [],
                          status: 'pending',
                          priority: 1,
                          metadata: { category: 'backend', complexity: 'medium', files: [] },
                        },
                      ],
                      totalEstimatedDuration: 30,
                      dependencies: {},
                    },
                  }),
                },
              },
            ],
            usage: { total_tokens: 100 },
          }),
        },
      },
    })),
  };
});

// Global test utilities
global.mockDynamoDBResponse = (data: any) => {
  const mockSend = jest.fn().mockResolvedValue(data);
  return { send: mockSend };
};

global.mockS3Response = (data: any) => {
  const mockSend = jest.fn().mockResolvedValue(data);
  return { send: mockSend };
};

// Increase timeout for integration tests
jest.setTimeout(30000);
