/**
 * Code validation utilities for static analysis
 */

import { GeneratedFile, CodeError, CodeWarning } from '../types';
import { validateCodeSyntax, validateGeneratedFile } from './validation';

export interface CodeValidationResult {
  errors: CodeError[];
  warnings: CodeWarning[];
  metrics: {
    linesOfCode: number;
    complexity: number;
    maintainabilityIndex: number;
  };
}

export async function validateGeneratedCode(files: GeneratedFile[]): Promise<CodeValidationResult> {
  const errors: CodeError[] = [];
  const warnings: CodeWarning[] = [];
  let totalLinesOfCode = 0;
  let totalComplexity = 0;

  for (const file of files) {
    // Basic file validation
    const fileValidation = validateGeneratedFile(file);
    
    // Convert validation errors to CodeError format
    fileValidation.errors.forEach(error => {
      errors.push({
        severity: 'major',
        message: error,
        file: file.path,
        rule: 'file_validation',
      });
    });

    fileValidation.warnings.forEach(warning => {
      warnings.push({
        message: warning,
        file: file.path,
        suggestion: 'Review file structure and naming conventions',
      });
    });

    // Syntax validation
    const syntaxValidation = validateCodeSyntax(file);
    
    syntaxValidation.errors.forEach(error => {
      errors.push({
        severity: 'critical',
        message: error,
        file: file.path,
        rule: 'syntax_error',
      });
    });

    syntaxValidation.warnings.forEach(warning => {
      warnings.push({
        message: warning,
        file: file.path,
        suggestion: 'Review code syntax and formatting',
      });
    });

    // Calculate metrics
    const fileMetrics = calculateFileMetrics(file);
    totalLinesOfCode += fileMetrics.linesOfCode;
    totalComplexity += fileMetrics.complexity;

    // Language-specific validation
    const languageValidation = validateLanguageSpecific(file);
    errors.push(...languageValidation.errors);
    warnings.push(...languageValidation.warnings);
  }

  const maintainabilityIndex = calculateMaintainabilityIndex(totalLinesOfCode, totalComplexity);

  return {
    errors,
    warnings,
    metrics: {
      linesOfCode: totalLinesOfCode,
      complexity: totalComplexity,
      maintainabilityIndex,
    },
  };
}

function calculateFileMetrics(file: GeneratedFile): { linesOfCode: number; complexity: number } {
  const lines = file.content.split('\n');
  const linesOfCode = lines.filter(line => {
    const trimmed = line.trim();
    return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('/*');
  }).length;

  let complexity = 1; // Base complexity

  // Calculate cyclomatic complexity
  const complexityPatterns = [
    /\bif\b/g,
    /\belse\b/g,
    /\bwhile\b/g,
    /\bfor\b/g,
    /\bswitch\b/g,
    /\bcase\b/g,
    /\bcatch\b/g,
    /\b&&\b/g,
    /\b\|\|\b/g,
    /\?\s*:/g, // Ternary operator
  ];

  for (const pattern of complexityPatterns) {
    const matches = file.content.match(pattern);
    if (matches) {
      complexity += matches.length;
    }
  }

  return { linesOfCode, complexity };
}

function calculateMaintainabilityIndex(linesOfCode: number, complexity: number): number {
  // Simplified maintainability index calculation
  // Real formula is more complex, but this gives a good approximation
  const halsteadVolume = Math.log2(linesOfCode) * 10; // Simplified
  const maintainabilityIndex = Math.max(0, 
    171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode)
  );
  
  return Math.round(maintainabilityIndex);
}

function validateLanguageSpecific(file: GeneratedFile): { errors: CodeError[]; warnings: CodeWarning[] } {
  const errors: CodeError[] = [];
  const warnings: CodeWarning[] = [];

  switch (file.language.toLowerCase()) {
    case 'typescript':
    case 'javascript':
      validateJavaScript(file, errors, warnings);
      break;
    case 'python':
      validatePython(file, errors, warnings);
      break;
    case 'html':
      validateHTML(file, errors, warnings);
      break;
    case 'css':
      validateCSS(file, errors, warnings);
      break;
  }

  return { errors, warnings };
}

function validateJavaScript(file: GeneratedFile, errors: CodeError[], warnings: CodeWarning[]): void {
  const content = file.content;
  const lines = content.split('\n');

  // Check for common issues
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const trimmed = line.trim();

    // Check for var usage (prefer let/const)
    if (trimmed.includes('var ')) {
      warnings.push({
        message: 'Use let or const instead of var',
        file: file.path,
        line: lineNumber,
        suggestion: 'Replace var with let or const for better scoping',
      });
    }

    // Check for == usage (prefer ===)
    if (trimmed.includes('==') && !trimmed.includes('===')) {
      warnings.push({
        message: 'Use strict equality (===) instead of loose equality (==)',
        file: file.path,
        line: lineNumber,
        suggestion: 'Replace == with === for type-safe comparisons',
      });
    }

    // Check for missing semicolons
    if (trimmed.length > 0 && 
        !trimmed.endsWith(';') && 
        !trimmed.endsWith('{') && 
        !trimmed.endsWith('}') &&
        !trimmed.startsWith('//') &&
        !trimmed.startsWith('/*')) {
      warnings.push({
        message: 'Missing semicolon',
        file: file.path,
        line: lineNumber,
        suggestion: 'Add semicolon at the end of the statement',
      });
    }

    // Check for console.log in production code
    if (trimmed.includes('console.log') && !content.includes('// debug')) {
      warnings.push({
        message: 'Console.log statement found',
        file: file.path,
        line: lineNumber,
        suggestion: 'Remove console.log statements in production code',
      });
    }
  });

  // Check for missing error handling
  if (content.includes('async ') && !content.includes('try') && !content.includes('catch')) {
    warnings.push({
      message: 'Async function without error handling',
      file: file.path,
      suggestion: 'Add try-catch blocks for async operations',
    });
  }

  // Check for hardcoded values
  const hardcodedPatterns = [
    /['"]http:\/\/localhost:\d+['"]/g,
    /['"]127\.0\.0\.1['"]/g,
    /['"]password['"]/g,
    /['"]secret['"]/g,
  ];

  hardcodedPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      warnings.push({
        message: 'Hardcoded value detected',
        file: file.path,
        suggestion: 'Use environment variables for configuration values',
      });
    }
  });
}

function validatePython(file: GeneratedFile, errors: CodeError[], warnings: CodeWarning[]): void {
  const content = file.content;
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    const trimmed = line.trim();

    // Check for print statements (prefer logging)
    if (trimmed.includes('print(') && !content.includes('# debug')) {
      warnings.push({
        message: 'Print statement found',
        file: file.path,
        line: lineNumber,
        suggestion: 'Use logging instead of print statements',
      });
    }

    // Check for bare except clauses
    if (trimmed === 'except:') {
      errors.push({
        severity: 'major',
        message: 'Bare except clause',
        file: file.path,
        line: lineNumber,
        rule: 'bare_except',
      });
    }
  });
}

function validateHTML(file: GeneratedFile, errors: CodeError[], warnings: CodeWarning[]): void {
  const content = file.content;

  // Check for missing alt attributes on images
  const imgTags = content.match(/<img[^>]*>/g) || [];
  imgTags.forEach(tag => {
    if (!tag.includes('alt=')) {
      warnings.push({
        message: 'Image missing alt attribute',
        file: file.path,
        suggestion: 'Add alt attribute for accessibility',
      });
    }
  });

  // Check for inline styles
  if (content.includes('style=')) {
    warnings.push({
      message: 'Inline styles detected',
      file: file.path,
      suggestion: 'Use external CSS files instead of inline styles',
    });
  }
}

function validateCSS(file: GeneratedFile, errors: CodeError[], warnings: CodeWarning[]): void {
  const content = file.content;

  // Check for !important usage
  const importantCount = (content.match(/!important/g) || []).length;
  if (importantCount > 3) {
    warnings.push({
      message: 'Excessive use of !important',
      file: file.path,
      suggestion: 'Refactor CSS to avoid overuse of !important',
    });
  }

  // Check for vendor prefixes without standard property
  const vendorPrefixes = ['-webkit-', '-moz-', '-ms-', '-o-'];
  vendorPrefixes.forEach(prefix => {
    const prefixedProperties = content.match(new RegExp(`${prefix}[a-z-]+:`, 'g')) || [];
    prefixedProperties.forEach(prop => {
      const standardProp = prop.replace(prefix, '');
      if (!content.includes(standardProp)) {
        warnings.push({
          message: `Missing standard property for ${prop}`,
          file: file.path,
          suggestion: `Add standard property ${standardProp} after vendor-prefixed version`,
        });
      }
    });
  });
}
