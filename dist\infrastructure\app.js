#!/usr/bin/env node
"use strict";
/**
 * CDK App entry point for AI Agent Workflow System
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
require("source-map-support/register");
const cdk = __importStar(require("aws-cdk-lib"));
const ai_agent_workflow_stack_1 = require("./ai-agent-workflow-stack");
const app = new cdk.App();
// Get environment configuration
const account = process.env.CDK_DEFAULT_ACCOUNT || process.env.AWS_ACCOUNT_ID;
const region = process.env.CDK_DEFAULT_REGION || process.env.AWS_REGION || 'us-east-1';
// Environment configuration
const env = {
    account,
    region,
};
// Stack configuration
const stackProps = {
    env,
    description: 'AI Agent Workflow System - Serverless multi-agent workflow for creating websites, backends, and chatbots',
    tags: {
        Project: 'AIAgentWorkflow',
        Environment: process.env.ENVIRONMENT || 'development',
        Owner: 'AIAgentWorkflowSystem',
        CostCenter: 'Development',
    },
};
// Create the main stack
new ai_agent_workflow_stack_1.AIAgentWorkflowStack(app, 'AIAgentWorkflowStack', stackProps);
// Add stack-level tags
cdk.Tags.of(app).add('Project', 'AIAgentWorkflow');
cdk.Tags.of(app).add('ManagedBy', 'CDK');
cdk.Tags.of(app).add('Repository', 'ai-agent-workflow-serverless');
app.synth();
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXBwLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL2luZnJhc3RydWN0dXJlL2FwcC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQUNBOztHQUVHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVILHVDQUFxQztBQUNyQyxpREFBbUM7QUFDbkMsdUVBQWlFO0FBRWpFLE1BQU0sR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDO0FBRTFCLGdDQUFnQztBQUNoQyxNQUFNLE9BQU8sR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFtQixJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsY0FBYyxDQUFDO0FBQzlFLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLElBQUksT0FBTyxDQUFDLEdBQUcsQ0FBQyxVQUFVLElBQUksV0FBVyxDQUFDO0FBRXZGLDRCQUE0QjtBQUM1QixNQUFNLEdBQUcsR0FBRztJQUNWLE9BQU87SUFDUCxNQUFNO0NBQ1AsQ0FBQztBQUVGLHNCQUFzQjtBQUN0QixNQUFNLFVBQVUsR0FBbUI7SUFDakMsR0FBRztJQUNILFdBQVcsRUFBRSwwR0FBMEc7SUFDdkgsSUFBSSxFQUFFO1FBQ0osT0FBTyxFQUFFLGlCQUFpQjtRQUMxQixXQUFXLEVBQUUsT0FBTyxDQUFDLEdBQUcsQ0FBQyxXQUFXLElBQUksYUFBYTtRQUNyRCxLQUFLLEVBQUUsdUJBQXVCO1FBQzlCLFVBQVUsRUFBRSxhQUFhO0tBQzFCO0NBQ0YsQ0FBQztBQUVGLHdCQUF3QjtBQUN4QixJQUFJLDhDQUFvQixDQUFDLEdBQUcsRUFBRSxzQkFBc0IsRUFBRSxVQUFVLENBQUMsQ0FBQztBQUVsRSx1QkFBdUI7QUFDdkIsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxpQkFBaUIsQ0FBQyxDQUFDO0FBQ25ELEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsS0FBSyxDQUFDLENBQUM7QUFDekMsR0FBRyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLFlBQVksRUFBRSw4QkFBOEIsQ0FBQyxDQUFDO0FBRW5FLEdBQUcsQ0FBQyxLQUFLLEVBQUUsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIiMhL3Vzci9iaW4vZW52IG5vZGVcbi8qKlxuICogQ0RLIEFwcCBlbnRyeSBwb2ludCBmb3IgQUkgQWdlbnQgV29ya2Zsb3cgU3lzdGVtXG4gKi9cblxuaW1wb3J0ICdzb3VyY2UtbWFwLXN1cHBvcnQvcmVnaXN0ZXInO1xuaW1wb3J0ICogYXMgY2RrIGZyb20gJ2F3cy1jZGstbGliJztcbmltcG9ydCB7IEFJQWdlbnRXb3JrZmxvd1N0YWNrIH0gZnJvbSAnLi9haS1hZ2VudC13b3JrZmxvdy1zdGFjayc7XG5cbmNvbnN0IGFwcCA9IG5ldyBjZGsuQXBwKCk7XG5cbi8vIEdldCBlbnZpcm9ubWVudCBjb25maWd1cmF0aW9uXG5jb25zdCBhY2NvdW50ID0gcHJvY2Vzcy5lbnYuQ0RLX0RFRkFVTFRfQUNDT1VOVCB8fCBwcm9jZXNzLmVudi5BV1NfQUNDT1VOVF9JRDtcbmNvbnN0IHJlZ2lvbiA9IHByb2Nlc3MuZW52LkNES19ERUZBVUxUX1JFR0lPTiB8fCBwcm9jZXNzLmVudi5BV1NfUkVHSU9OIHx8ICd1cy1lYXN0LTEnO1xuXG4vLyBFbnZpcm9ubWVudCBjb25maWd1cmF0aW9uXG5jb25zdCBlbnYgPSB7XG4gIGFjY291bnQsXG4gIHJlZ2lvbixcbn07XG5cbi8vIFN0YWNrIGNvbmZpZ3VyYXRpb25cbmNvbnN0IHN0YWNrUHJvcHM6IGNkay5TdGFja1Byb3BzID0ge1xuICBlbnYsXG4gIGRlc2NyaXB0aW9uOiAnQUkgQWdlbnQgV29ya2Zsb3cgU3lzdGVtIC0gU2VydmVybGVzcyBtdWx0aS1hZ2VudCB3b3JrZmxvdyBmb3IgY3JlYXRpbmcgd2Vic2l0ZXMsIGJhY2tlbmRzLCBhbmQgY2hhdGJvdHMnLFxuICB0YWdzOiB7XG4gICAgUHJvamVjdDogJ0FJQWdlbnRXb3JrZmxvdycsXG4gICAgRW52aXJvbm1lbnQ6IHByb2Nlc3MuZW52LkVOVklST05NRU5UIHx8ICdkZXZlbG9wbWVudCcsXG4gICAgT3duZXI6ICdBSUFnZW50V29ya2Zsb3dTeXN0ZW0nLFxuICAgIENvc3RDZW50ZXI6ICdEZXZlbG9wbWVudCcsXG4gIH0sXG59O1xuXG4vLyBDcmVhdGUgdGhlIG1haW4gc3RhY2tcbm5ldyBBSUFnZW50V29ya2Zsb3dTdGFjayhhcHAsICdBSUFnZW50V29ya2Zsb3dTdGFjaycsIHN0YWNrUHJvcHMpO1xuXG4vLyBBZGQgc3RhY2stbGV2ZWwgdGFnc1xuY2RrLlRhZ3Mub2YoYXBwKS5hZGQoJ1Byb2plY3QnLCAnQUlBZ2VudFdvcmtmbG93Jyk7XG5jZGsuVGFncy5vZihhcHApLmFkZCgnTWFuYWdlZEJ5JywgJ0NESycpO1xuY2RrLlRhZ3Mub2YoYXBwKS5hZGQoJ1JlcG9zaXRvcnknLCAnYWktYWdlbnQtd29ya2Zsb3ctc2VydmVybGVzcycpO1xuXG5hcHAuc3ludGgoKTtcbiJdfQ==