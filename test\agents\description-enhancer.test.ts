/**
 * Tests for Description Enhancer Agent
 */

import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { handler } from '../../src/agents/description-enhancer/handler';

// Mock the dependencies
jest.mock('@aws-sdk/lib-dynamodb');
jest.mock('../../src/utils/llm-client');
jest.mock('../../src/utils/validation');

describe('Description Enhancer Agent', () => {
  const mockContext: Context = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: 'test-function',
    functionVersion: '1',
    invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/test-function',
    logStreamName: '2023/01/01/[$LATEST]test-stream',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env.WORKFLOW_TABLE_NAME = 'test-table';
    process.env.OPENAI_API_KEY = 'test-key';
  });

  it('should successfully enhance a valid user description', async () => {
    const event: APIGatewayProxyEvent = {
      httpMethod: 'POST',
      path: '/enhance',
      headers: {},
      multiValueHeaders: {},
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      pathParameters: null,
      stageVariables: null,
      requestContext: {
        accountId: '************',
        apiId: 'test-api',
        protocol: 'HTTP/1.1',
        httpMethod: 'POST',
        path: '/enhance',
        stage: 'test',
        requestId: 'test-request',
        requestTime: '01/Jan/2023:00:00:00 +0000',
        requestTimeEpoch: **********,
        identity: {
          cognitoIdentityPoolId: null,
          accountId: null,
          cognitoIdentityId: null,
          caller: null,
          sourceIp: '127.0.0.1',
          principalOrgId: null,
          accessKey: null,
          cognitoAuthenticationType: null,
          cognitoAuthenticationProvider: null,
          userArn: null,
          userAgent: 'test-agent',
          user: null,
          apiKey: 'test-api-key',
          apiKeyId: 'test-key-id',
          clientCert: null,
        },
        resourceId: 'test-resource',
        resourcePath: '/enhance',
        authorizer: null,
      },
      body: JSON.stringify({
        userRequest: {
          userDescription: 'Create a modern e-commerce website with user authentication, product catalog, shopping cart, and payment integration',
          preferences: {
            techStack: ['react', 'next.js'],
            deploymentTarget: 'aws',
            includeTests: true,
            includeCICD: true,
          },
        },
        sessionId: 'test-session-id',
        userId: 'test-user-id',
      }),
      isBase64Encoded: false,
      resource: '/enhance',
    };

    // Mock validation to return valid
    const { validateUserRequest } = require('../../src/utils/validation');
    validateUserRequest.mockReturnValue({
      isValid: true,
      errors: [],
      warnings: [],
    });

    // Mock DynamoDB
    const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
    const mockSend = jest.fn().mockResolvedValue({});
    DynamoDBDocumentClient.from = jest.fn().mockReturnValue({ send: mockSend });

    const result = await handler(event, mockContext);

    expect(result.statusCode).toBe(200);
    
    const responseBody = JSON.parse(result.body);
    expect(responseBody).toHaveProperty('sessionId');
    expect(responseBody).toHaveProperty('specification');
    expect(responseBody).toHaveProperty('taskPlan');
    expect(responseBody.status).toBe('enhancement_completed');
  });

  it('should return 400 for invalid user request', async () => {
    const event: APIGatewayProxyEvent = {
      httpMethod: 'POST',
      path: '/enhance',
      headers: {},
      multiValueHeaders: {},
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      pathParameters: null,
      stageVariables: null,
      requestContext: {
        accountId: '************',
        apiId: 'test-api',
        protocol: 'HTTP/1.1',
        httpMethod: 'POST',
        path: '/enhance',
        stage: 'test',
        requestId: 'test-request',
        requestTime: '01/Jan/2023:00:00:00 +0000',
        requestTimeEpoch: **********,
        identity: {
          cognitoIdentityPoolId: null,
          accountId: null,
          cognitoIdentityId: null,
          caller: null,
          sourceIp: '127.0.0.1',
          principalOrgId: null,
          accessKey: null,
          cognitoAuthenticationType: null,
          cognitoAuthenticationProvider: null,
          userArn: null,
          userAgent: 'test-agent',
          user: null,
          apiKey: 'test-api-key',
          apiKeyId: 'test-key-id',
          clientCert: null,
        },
        resourceId: 'test-resource',
        resourcePath: '/enhance',
        authorizer: null,
      },
      body: JSON.stringify({
        userRequest: {
          userDescription: '', // Invalid: empty description
        },
      }),
      isBase64Encoded: false,
      resource: '/enhance',
    };

    // Mock validation to return invalid
    const { validateUserRequest } = require('../../src/utils/validation');
    validateUserRequest.mockReturnValue({
      isValid: false,
      errors: ['User description is required'],
      warnings: [],
    });

    const result = await handler(event, mockContext);

    expect(result.statusCode).toBe(400);
    
    const responseBody = JSON.parse(result.body);
    expect(responseBody.error).toBe('Invalid user request');
    expect(responseBody.details).toContain('User description is required');
  });

  it('should return 500 for internal server error', async () => {
    const event: APIGatewayProxyEvent = {
      httpMethod: 'POST',
      path: '/enhance',
      headers: {},
      multiValueHeaders: {},
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      pathParameters: null,
      stageVariables: null,
      requestContext: {
        accountId: '************',
        apiId: 'test-api',
        protocol: 'HTTP/1.1',
        httpMethod: 'POST',
        path: '/enhance',
        stage: 'test',
        requestId: 'test-request',
        requestTime: '01/Jan/2023:00:00:00 +0000',
        requestTimeEpoch: **********,
        identity: {
          cognitoIdentityPoolId: null,
          accountId: null,
          cognitoIdentityId: null,
          caller: null,
          sourceIp: '127.0.0.1',
          principalOrgId: null,
          accessKey: null,
          cognitoAuthenticationType: null,
          cognitoAuthenticationProvider: null,
          userArn: null,
          userAgent: 'test-agent',
          user: null,
          apiKey: 'test-api-key',
          apiKeyId: 'test-key-id',
          clientCert: null,
        },
        resourceId: 'test-resource',
        resourcePath: '/enhance',
        authorizer: null,
      },
      body: JSON.stringify({
        userRequest: {
          userDescription: 'Valid description',
        },
      }),
      isBase64Encoded: false,
      resource: '/enhance',
    };

    // Mock validation to return valid
    const { validateUserRequest } = require('../../src/utils/validation');
    validateUserRequest.mockReturnValue({
      isValid: true,
      errors: [],
      warnings: [],
    });

    // Mock DynamoDB to throw error
    const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
    const mockSend = jest.fn().mockRejectedValue(new Error('DynamoDB error'));
    DynamoDBDocumentClient.from = jest.fn().mockReturnValue({ send: mockSend });

    const result = await handler(event, mockContext);

    expect(result.statusCode).toBe(500);
    
    const responseBody = JSON.parse(result.body);
    expect(responseBody.error).toBe('Internal server error');
    expect(responseBody.message).toBe('Failed to enhance description');
  });

  it('should handle malformed JSON in request body', async () => {
    const event: APIGatewayProxyEvent = {
      httpMethod: 'POST',
      path: '/enhance',
      headers: {},
      multiValueHeaders: {},
      queryStringParameters: null,
      multiValueQueryStringParameters: null,
      pathParameters: null,
      stageVariables: null,
      requestContext: {
        accountId: '************',
        apiId: 'test-api',
        protocol: 'HTTP/1.1',
        httpMethod: 'POST',
        path: '/enhance',
        stage: 'test',
        requestId: 'test-request',
        requestTime: '01/Jan/2023:00:00:00 +0000',
        requestTimeEpoch: **********,
        identity: {
          cognitoIdentityPoolId: null,
          accountId: null,
          cognitoIdentityId: null,
          caller: null,
          sourceIp: '127.0.0.1',
          principalOrgId: null,
          accessKey: null,
          cognitoAuthenticationType: null,
          cognitoAuthenticationProvider: null,
          userArn: null,
          userAgent: 'test-agent',
          user: null,
          apiKey: 'test-api-key',
          apiKeyId: 'test-key-id',
          clientCert: null,
        },
        resourceId: 'test-resource',
        resourcePath: '/enhance',
        authorizer: null,
      },
      body: 'invalid json{',
      isBase64Encoded: false,
      resource: '/enhance',
    };

    const result = await handler(event, mockContext);

    expect(result.statusCode).toBe(500);
    
    const responseBody = JSON.parse(result.body);
    expect(responseBody.error).toBe('Internal server error');
  });
});
