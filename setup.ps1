# AI Agent Workflow System - Windows Setup Script
# Run this script in PowerShell to set up the project

Write-Host "🚀 Setting up AI Agent Workflow System..." -ForegroundColor Blue

# Check if Node.js is installed
Write-Host "📦 Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is available
Write-Host "📦 Checking npm installation..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found. Please install npm." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green

# Check if AWS CLI is installed
Write-Host "☁️ Checking AWS CLI installation..." -ForegroundColor Yellow
try {
    $awsVersion = aws --version
    Write-Host "✅ AWS CLI found: $awsVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️ AWS CLI not found. Please install from https://aws.amazon.com/cli/" -ForegroundColor Yellow
    Write-Host "You can continue setup and install AWS CLI later." -ForegroundColor Yellow
}

# Check if CDK is installed
Write-Host "🏗️ Checking AWS CDK installation..." -ForegroundColor Yellow
try {
    $cdkVersion = cdk --version
    Write-Host "✅ AWS CDK found: $cdkVersion" -ForegroundColor Green
} catch {
    Write-Host "📦 Installing AWS CDK globally..." -ForegroundColor Yellow
    npm install -g aws-cdk
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install AWS CDK" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ AWS CDK installed successfully" -ForegroundColor Green
}

# Build the project
Write-Host "🔨 Building the project..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Project built successfully" -ForegroundColor Green

# Check environment file
Write-Host "⚙️ Checking environment configuration..." -ForegroundColor Yellow
if (Test-Path ".env") {
    Write-Host "✅ .env file found" -ForegroundColor Green
} else {
    Write-Host "📝 Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ .env file created. Please edit it with your API keys." -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Blue
Write-Host "1. Edit .env file with your API keys:" -ForegroundColor White
Write-Host "   - GEMINI_API_KEY (get from https://makersuite.google.com/app/apikey)" -ForegroundColor Gray
Write-Host "   - OPENROUTER_API_KEY (get from https://openrouter.ai/)" -ForegroundColor Gray
Write-Host "   - AWS_ACCOUNT_ID (your AWS account ID)" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Configure AWS CLI:" -ForegroundColor White
Write-Host "   aws configure" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Bootstrap CDK (first time only):" -ForegroundColor White
Write-Host "   cdk bootstrap aws://YOUR-ACCOUNT-ID/us-east-1" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Deploy the system:" -ForegroundColor White
Write-Host "   npm run deploy" -ForegroundColor Gray
Write-Host ""
Write-Host "For detailed instructions, see DEPLOYMENT_GUIDE.md" -ForegroundColor Blue
