/**
 * Centralized logging utility
 */

export interface LogContext {
  requestId?: string;
  sessionId?: string;
  taskId?: string;
  agent?: string;
  [key: string]: any;
}

export class Logger {
  private serviceName: string;

  constructor(serviceName: string = 'ai-agent-workflow') {
    this.serviceName = serviceName;
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      service: this.serviceName,
      message,
      ...context,
    };

    return JSON.stringify(logEntry);
  }

  info(message: string, context?: LogContext): void {
    console.log(this.formatMessage('INFO', message, context));
  }

  warn(message: string, context?: LogContext): void {
    console.warn(this.formatMessage('WARN', message, context));
  }

  error(message: string, context?: LogContext): void {
    console.error(this.formatMessage('ERROR', message, context));
  }

  debug(message: string, context?: LogContext): void {
    if (process.env.LOG_LEVEL === 'debug') {
      console.debug(this.formatMessage('DEBUG', message, context));
    }
  }

  trace(message: string, context?: LogContext): void {
    if (process.env.LOG_LEVEL === 'trace') {
      console.trace(this.formatMessage('TRACE', message, context));
    }
  }
}

export const logger = new Logger();

export function createLogger(serviceName: string): Logger {
  return new Logger(serviceName);
}

export function logExecutionTime<T>(
  operation: string,
  fn: () => Promise<T>,
  context?: LogContext
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const startTime = Date.now();
    logger.info(`Starting ${operation}`, context);

    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      logger.info(`Completed ${operation}`, { ...context, duration });
      resolve(result);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed ${operation}`, { ...context, duration, error: errorMessage });
      reject(error);
    }
  });
}

export function logWithMetrics(
  message: string,
  metrics: Record<string, number>,
  context?: LogContext
): void {
  logger.info(message, { ...context, metrics });
}

export function createRequestLogger(requestId: string) {
  return {
    info: (message: string, context?: LogContext) => 
      logger.info(message, { ...context, requestId }),
    warn: (message: string, context?: LogContext) => 
      logger.warn(message, { ...context, requestId }),
    error: (message: string, context?: LogContext) => 
      logger.error(message, { ...context, requestId }),
    debug: (message: string, context?: LogContext) => 
      logger.debug(message, { ...context, requestId }),
  };
}
