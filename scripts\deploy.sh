#!/bin/bash

# AI Agent Workflow System Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install AWS CLI and try again."
        exit 1
    fi
    
    if ! command -v cdk &> /dev/null; then
        print_error "AWS CDK is not installed. Please install CDK with 'npm install -g aws-cdk' and try again."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Check environment variables
check_environment() {
    print_status "Checking environment variables..."

    if [ -z "$GEMINI_API_KEY" ] && [ -z "$DEEPSEEK_API_KEY" ]; then
        print_error "At least one of GEMINI_API_KEY or DEEPSEEK_API_KEY environment variables must be set"
        exit 1
    fi

    if [ -n "$GEMINI_API_KEY" ]; then
        print_success "Gemini API key found"
    fi

    if [ -n "$DEEPSEEK_API_KEY" ]; then
        print_success "DeepSeek API key found"
    fi
    
    if [ -z "$AWS_ACCOUNT_ID" ]; then
        print_warning "AWS_ACCOUNT_ID not set, using CDK default"
    fi
    
    if [ -z "$AWS_REGION" ]; then
        print_warning "AWS_REGION not set, using us-east-1"
        export AWS_REGION="us-east-1"
    fi
    
    print_success "Environment variables checked"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed"
}

# Build the project
build_project() {
    print_status "Building project..."
    npm run build
    print_success "Project built successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    npm test
    print_success "All tests passed"
}

# Run linting
run_lint() {
    print_status "Running linter..."
    npm run lint
    print_success "Linting passed"
}

# Bootstrap CDK (if needed)
bootstrap_cdk() {
    print_status "Checking CDK bootstrap status..."
    
    # Check if bootstrap is needed
    if ! aws cloudformation describe-stacks --stack-name CDKToolkit --region $AWS_REGION &> /dev/null; then
        print_status "Bootstrapping CDK..."
        npm run bootstrap
        print_success "CDK bootstrapped successfully"
    else
        print_success "CDK already bootstrapped"
    fi
}

# Deploy the stack
deploy_stack() {
    print_status "Deploying AI Agent Workflow Stack..."
    
    # Set deployment environment
    export ENVIRONMENT=${ENVIRONMENT:-development}
    
    print_status "Deploying to environment: $ENVIRONMENT"
    npm run deploy
    
    print_success "Stack deployed successfully"
}

# Get stack outputs
get_outputs() {
    print_status "Retrieving stack outputs..."
    
    # Get the stack outputs
    OUTPUTS=$(aws cloudformation describe-stacks \
        --stack-name AIAgentWorkflowStack \
        --region $AWS_REGION \
        --query 'Stacks[0].Outputs' \
        --output table 2>/dev/null || echo "[]")
    
    if [ "$OUTPUTS" != "[]" ]; then
        echo ""
        print_success "Stack Outputs:"
        echo "$OUTPUTS"
        echo ""
        
        # Extract API endpoint
        API_ENDPOINT=$(aws cloudformation describe-stacks \
            --stack-name AIAgentWorkflowStack \
            --region $AWS_REGION \
            --query 'Stacks[0].Outputs[?OutputKey==`ApiEndpoint`].OutputValue' \
            --output text 2>/dev/null || echo "")
        
        if [ -n "$API_ENDPOINT" ]; then
            print_success "API Endpoint: $API_ENDPOINT"
            echo ""
            print_status "You can test the API with:"
            echo "curl -X POST $API_ENDPOINT/enhance \\"
            echo "  -H \"Content-Type: application/json\" \\"
            echo "  -H \"x-api-key: YOUR_API_KEY\" \\"
            echo "  -d '{\"userDescription\": \"Create a simple website\"}'"
        fi
    else
        print_warning "No stack outputs found"
    fi
}

# Main deployment function
main() {
    echo ""
    print_status "Starting AI Agent Workflow System Deployment"
    echo ""
    
    # Parse command line arguments
    SKIP_TESTS=false
    SKIP_LINT=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-lint)
                SKIP_LINT=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --skip-tests    Skip running tests"
                echo "  --skip-lint     Skip running linter"
                echo "  --help          Show this help message"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Run deployment steps
    check_prerequisites
    check_environment
    install_dependencies
    build_project
    
    if [ "$SKIP_LINT" = false ]; then
        run_lint
    else
        print_warning "Skipping linter"
    fi
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    else
        print_warning "Skipping tests"
    fi
    
    bootstrap_cdk
    deploy_stack
    get_outputs
    
    echo ""
    print_success "Deployment completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Note down the API endpoint and API key from the outputs above"
    echo "2. Test the system using the provided curl command"
    echo "3. Check CloudWatch for logs and metrics"
    echo "4. Review the generated documentation"
    echo ""
}

# Run main function
main "$@"
