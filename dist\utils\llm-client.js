"use strict";
/**
 * LLM Client for interacting with AI models (Gemini and DeepSeek R1)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.invokeLLM = invokeLLM;
exports.invokeLLMWithRetry = invokeLLMWithRetry;
exports.invokeLLMStreaming = invokeLLMStreaming;
exports.validateLLMResponse = validateLLMResponse;
exports.extractJSONFromResponse = extractJSONFromResponse;
exports.sanitizePrompt = sanitizePrompt;
exports.buildPromptWithContext = buildPromptWithContext;
exports.estimateTokenCount = estimateTokenCount;
exports.splitLargePrompt = splitLargePrompt;
const generative_ai_1 = require("@google/generative-ai");
const logger_1 = require("./logger");
// Initialize Gemini client
const gemini = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
// OpenRouter configuration (preferred for DeepSeek)
const OPENROUTER_API_BASE = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '';
// Direct DeepSeek R1 client configuration (fallback)
const DEEPSEEK_API_BASE = 'https://api.deepseek.com/v1';
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '';
async function invokeLLM(prompt, options = {}) {
    const { temperature = 0.7, maxTokens = 4000, model = 'auto', requestId = 'unknown' } = options;
    // Determine which provider to use
    const provider = determineProvider(model, prompt);
    try {
        logger_1.logger.info('Invoking LLM', {
            requestId,
            provider,
            model,
            temperature,
            maxTokens,
            promptLength: prompt.length
        });
        let content;
        if (provider === 'gemini') {
            content = await invokeGemini(prompt, { temperature, maxTokens, requestId });
        }
        else {
            content = await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
        }
        logger_1.logger.info('LLM response received', {
            requestId,
            provider,
            responseLength: content.length
        });
        return content;
    }
    catch (error) {
        logger_1.logger.error('LLM invocation failed', { requestId, provider, error });
        // Fallback to alternative provider if primary fails
        if (provider === 'gemini' && DEEPSEEK_API_KEY) {
            logger_1.logger.info('Falling back to DeepSeek', { requestId });
            return await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
        }
        else if (provider === 'deepseek' && process.env.GEMINI_API_KEY) {
            logger_1.logger.info('Falling back to Gemini', { requestId });
            return await invokeGemini(prompt, { temperature, maxTokens, requestId });
        }
        throw new Error(`LLM invocation failed: ${error.message}`);
    }
}
async function invokeGemini(prompt, options) {
    const model = gemini.getGenerativeModel({
        model: 'gemini-1.5-pro',
        generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 4000,
        }
    });
    const systemPrompt = 'You are an expert software architect and developer. Provide detailed, production-ready solutions in JSON format when requested.';
    const fullPrompt = `${systemPrompt}\n\n${prompt}`;
    const result = await model.generateContent(fullPrompt);
    const response = await result.response;
    const content = response.text();
    if (!content) {
        throw new Error('No content received from Gemini');
    }
    return content;
}
async function invokeDeepSeek(prompt, options) {
    // Prefer OpenRouter if available, fallback to direct DeepSeek API
    const useOpenRouter = !!OPENROUTER_API_KEY;
    const apiBase = useOpenRouter ? OPENROUTER_API_BASE : DEEPSEEK_API_BASE;
    const apiKey = useOpenRouter ? OPENROUTER_API_KEY : DEEPSEEK_API_KEY;
    const model = useOpenRouter ? 'deepseek/deepseek-r1' : 'deepseek-reasoner';
    if (!apiKey) {
        throw new Error('No DeepSeek API key available (neither OpenRouter nor direct DeepSeek)');
    }
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };
    // Add OpenRouter specific headers
    if (useOpenRouter) {
        headers['HTTP-Referer'] = 'https://ai-agent-workflow.com';
        headers['X-Title'] = 'AI Agent Workflow System';
    }
    const response = await fetch(`${apiBase}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: options.temperature || 0.7,
            max_tokens: options.maxTokens || 4000,
        }),
    });
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`${useOpenRouter ? 'OpenRouter' : 'DeepSeek'} API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;
    if (!content) {
        throw new Error(`No content received from ${useOpenRouter ? 'OpenRouter' : 'DeepSeek'}`);
    }
    return content;
}
function determineProvider(model, prompt) {
    // If specific model requested
    if (model === 'gemini')
        return 'gemini';
    if (model === 'deepseek')
        return 'deepseek';
    // Check availability - prefer OpenRouter for DeepSeek if available
    const hasGemini = !!process.env.GEMINI_API_KEY;
    const hasDeepSeek = !!(OPENROUTER_API_KEY || DEEPSEEK_API_KEY);
    // Auto-selection based on availability
    if (!hasGemini && hasDeepSeek)
        return 'deepseek';
    if (hasGemini && !hasDeepSeek)
        return 'gemini';
    // Both available - choose based on task type
    if (prompt.includes('reasoning') || prompt.includes('analysis') || prompt.includes('review') ||
        prompt.includes('code') || prompt.includes('debug') || prompt.includes('refactor')) {
        return 'deepseek'; // DeepSeek R1 is better for reasoning and code tasks
    }
    return 'gemini'; // Gemini for general tasks and documentation
}
async function invokeLLMWithRetry(prompt, options = {}, maxRetries = 3) {
    let lastError;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await invokeLLM(prompt, {
                ...options,
                requestId: `${options.requestId}-attempt-${attempt}`
            });
        }
        catch (error) {
            lastError = error;
            logger_1.logger.warn('LLM invocation attempt failed', {
                attempt,
                maxRetries,
                requestId: options.requestId,
                error: error.message
            });
            if (attempt < maxRetries) {
                // Exponential backoff
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    throw lastError;
}
async function invokeLLMStreaming(prompt, options = {}, onChunk) {
    const { temperature = 0.7, maxTokens = 4000, model = 'auto', requestId = 'unknown' } = options;
    const provider = determineProvider(model, prompt);
    try {
        logger_1.logger.info('Invoking LLM with streaming', {
            requestId,
            provider,
            temperature,
            maxTokens
        });
        if (provider === 'gemini') {
            // Gemini doesn't support streaming in the same way, so we'll simulate it
            const content = await invokeGemini(prompt, { temperature, maxTokens, requestId });
            // Simulate streaming by chunking the response
            const chunkSize = 50;
            let fullContent = '';
            for (let i = 0; i < content.length; i += chunkSize) {
                const chunk = content.slice(i, i + chunkSize);
                fullContent += chunk;
                if (onChunk) {
                    onChunk(chunk);
                }
                // Small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            return fullContent;
        }
        else {
            // DeepSeek streaming implementation
            return await invokeDeepSeekStreaming(prompt, { temperature, maxTokens, requestId }, onChunk);
        }
    }
    catch (error) {
        logger_1.logger.error('LLM streaming invocation failed', { requestId, provider, error });
        throw new Error(`LLM streaming invocation failed: ${error.message}`);
    }
}
async function invokeDeepSeekStreaming(prompt, options, onChunk) {
    // Prefer OpenRouter if available, fallback to direct DeepSeek API
    const useOpenRouter = !!OPENROUTER_API_KEY;
    const apiBase = useOpenRouter ? OPENROUTER_API_BASE : DEEPSEEK_API_BASE;
    const apiKey = useOpenRouter ? OPENROUTER_API_KEY : DEEPSEEK_API_KEY;
    const model = useOpenRouter ? 'deepseek/deepseek-r1' : 'deepseek-reasoner';
    if (!apiKey) {
        throw new Error('No DeepSeek API key available (neither OpenRouter nor direct DeepSeek)');
    }
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };
    // Add OpenRouter specific headers
    if (useOpenRouter) {
        headers['HTTP-Referer'] = 'https://ai-agent-workflow.com';
        headers['X-Title'] = 'AI Agent Workflow System';
    }
    const response = await fetch(`${apiBase}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: options.temperature || 0.7,
            max_tokens: options.maxTokens || 4000,
            stream: true,
        }),
    });
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`${useOpenRouter ? 'OpenRouter' : 'DeepSeek'} API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    let fullContent = '';
    const reader = response.body?.getReader();
    if (!reader) {
        throw new Error('No response body reader available');
    }
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done)
                break;
            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n').filter(line => line.trim() !== '');
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]')
                        continue;
                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices?.[0]?.delta?.content || '';
                        if (content) {
                            fullContent += content;
                            if (onChunk) {
                                onChunk(content);
                            }
                        }
                    }
                    catch (e) {
                        // Ignore parsing errors for malformed chunks
                    }
                }
            }
        }
    }
    finally {
        reader.releaseLock();
    }
    return fullContent;
}
function validateLLMResponse(response, expectedFormat) {
    if (!response || response.trim().length === 0) {
        return false;
    }
    if (expectedFormat === 'json') {
        try {
            JSON.parse(response);
            return true;
        }
        catch {
            return false;
        }
    }
    if (expectedFormat === 'yaml') {
        // Basic YAML validation
        return response.includes(':') && !response.includes('{');
    }
    if (expectedFormat === 'markdown') {
        // Basic markdown validation
        return response.includes('#') || response.includes('*') || response.includes('-');
    }
    return true;
}
function extractJSONFromResponse(response) {
    try {
        // Try to parse the entire response as JSON
        return JSON.parse(response);
    }
    catch {
        // Try to extract JSON from code blocks
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[1]);
            }
            catch {
                // Fall through to next attempt
            }
        }
        // Try to extract JSON from the response
        const jsonStart = response.indexOf('{');
        const jsonEnd = response.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            try {
                return JSON.parse(response.substring(jsonStart, jsonEnd + 1));
            }
            catch {
                // Fall through to error
            }
        }
        throw new Error('No valid JSON found in response');
    }
}
function sanitizePrompt(prompt) {
    // Remove potentially harmful content
    return prompt
        .replace(/\b(eval|exec|system|shell)\s*\(/gi, '[SANITIZED]')
        .replace(/\b(rm|del|delete)\s+/gi, '[SANITIZED]')
        .replace(/\b(DROP|DELETE|TRUNCATE)\s+/gi, '[SANITIZED]')
        .trim();
}
function buildPromptWithContext(basePrompt, context, maxContextLength = 8000) {
    let contextString = JSON.stringify(context, null, 2);
    // Truncate context if too long
    if (contextString.length > maxContextLength) {
        contextString = contextString.substring(0, maxContextLength) + '\n... [TRUNCATED]';
    }
    return `${basePrompt}\n\nContext:\n${contextString}`;
}
function estimateTokenCount(text) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
}
function splitLargePrompt(prompt, maxTokens = 3000) {
    const maxChars = maxTokens * 4;
    const chunks = [];
    if (prompt.length <= maxChars) {
        return [prompt];
    }
    // Split by paragraphs first
    const paragraphs = prompt.split('\n\n');
    let currentChunk = '';
    for (const paragraph of paragraphs) {
        if ((currentChunk + paragraph).length <= maxChars) {
            currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
        }
        else {
            if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = paragraph;
            }
            else {
                // Paragraph is too long, split by sentences
                const sentences = paragraph.split('. ');
                for (const sentence of sentences) {
                    if ((currentChunk + sentence).length <= maxChars) {
                        currentChunk += (currentChunk ? '. ' : '') + sentence;
                    }
                    else {
                        if (currentChunk) {
                            chunks.push(currentChunk);
                        }
                        currentChunk = sentence;
                    }
                }
            }
        }
    }
    if (currentChunk) {
        chunks.push(currentChunk);
    }
    return chunks;
}
//# sourceMappingURL=data:application/json;base64,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