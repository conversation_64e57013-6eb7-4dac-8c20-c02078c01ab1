"use strict";
/**
 * LLM Client for interacting with AI models (Gemini and DeepSeek R1)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.invokeLLM = invokeLLM;
exports.invokeLLMWithRetry = invokeLLMWithRetry;
exports.invokeLLMStreaming = invokeLLMStreaming;
exports.validateLLMResponse = validateLLMResponse;
exports.extractJSONFromResponse = extractJSONFromResponse;
exports.sanitizePrompt = sanitizePrompt;
exports.buildPromptWithContext = buildPromptWithContext;
exports.estimateTokenCount = estimateTokenCount;
exports.splitLargePrompt = splitLargePrompt;
const generative_ai_1 = require("@google/generative-ai");
const logger_1 = require("./logger");
// Initialize Gemini client
const gemini = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
// OpenRouter configuration (preferred for DeepSeek)
const OPENROUTER_API_BASE = 'https://openrouter.ai/api/v1';
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '';
// Direct DeepSeek R1 client configuration (fallback)
const DEEPSEEK_API_BASE = 'https://api.deepseek.com/v1';
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '';
async function invokeLLM(prompt, options = {}) {
    const { temperature = 0.7, maxTokens = 4000, model = 'auto', requestId = 'unknown' } = options;
    // Determine which provider to use
    const provider = determineProvider(model, prompt);
    try {
        logger_1.logger.info('Invoking LLM', {
            requestId,
            provider,
            model,
            temperature,
            maxTokens,
            promptLength: prompt.length
        });
        let content;
        if (provider === 'gemini') {
            content = await invokeGemini(prompt, { temperature, maxTokens, requestId });
        }
        else {
            content = await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
        }
        logger_1.logger.info('LLM response received', {
            requestId,
            provider,
            responseLength: content.length
        });
        return content;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error('LLM invocation failed', { requestId, provider, error: errorMessage });
        // Fallback to alternative provider if primary fails
        if (provider === 'gemini' && DEEPSEEK_API_KEY) {
            logger_1.logger.info('Falling back to DeepSeek', { requestId });
            return await invokeDeepSeek(prompt, { temperature, maxTokens, requestId });
        }
        else if (provider === 'deepseek' && process.env.GEMINI_API_KEY) {
            logger_1.logger.info('Falling back to Gemini', { requestId });
            return await invokeGemini(prompt, { temperature, maxTokens, requestId });
        }
        throw new Error(`LLM invocation failed: ${errorMessage}`);
    }
}
async function invokeGemini(prompt, options) {
    const model = gemini.getGenerativeModel({
        model: 'gemini-1.5-pro',
        generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 4000,
        }
    });
    const systemPrompt = 'You are an expert software architect and developer. Provide detailed, production-ready solutions in JSON format when requested.';
    const fullPrompt = `${systemPrompt}\n\n${prompt}`;
    const result = await model.generateContent(fullPrompt);
    const response = await result.response;
    const content = response.text();
    if (!content) {
        throw new Error('No content received from Gemini');
    }
    return content;
}
async function invokeDeepSeek(prompt, options) {
    // Prefer OpenRouter if available, fallback to direct DeepSeek API
    const useOpenRouter = !!OPENROUTER_API_KEY;
    const apiBase = useOpenRouter ? OPENROUTER_API_BASE : DEEPSEEK_API_BASE;
    const apiKey = useOpenRouter ? OPENROUTER_API_KEY : DEEPSEEK_API_KEY;
    const model = useOpenRouter ? 'deepseek/deepseek-r1' : 'deepseek-reasoner';
    if (!apiKey) {
        throw new Error('No DeepSeek API key available (neither OpenRouter nor direct DeepSeek)');
    }
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };
    // Add OpenRouter specific headers
    if (useOpenRouter) {
        headers['HTTP-Referer'] = 'https://ai-agent-workflow.com';
        headers['X-Title'] = 'AI Agent Workflow System';
    }
    const response = await fetch(`${apiBase}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: options.temperature || 0.7,
            max_tokens: options.maxTokens || 4000,
        }),
    });
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`${useOpenRouter ? 'OpenRouter' : 'DeepSeek'} API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;
    if (!content) {
        throw new Error(`No content received from ${useOpenRouter ? 'OpenRouter' : 'DeepSeek'}`);
    }
    return content;
}
function determineProvider(model, prompt) {
    // If specific model requested
    if (model === 'gemini')
        return 'gemini';
    if (model === 'deepseek')
        return 'deepseek';
    // Check availability - prefer OpenRouter for DeepSeek if available
    const hasGemini = !!process.env.GEMINI_API_KEY;
    const hasDeepSeek = !!(OPENROUTER_API_KEY || DEEPSEEK_API_KEY);
    // Auto-selection based on availability
    if (!hasGemini && hasDeepSeek)
        return 'deepseek';
    if (hasGemini && !hasDeepSeek)
        return 'gemini';
    // Both available - choose based on task type
    if (prompt.includes('reasoning') || prompt.includes('analysis') || prompt.includes('review') ||
        prompt.includes('code') || prompt.includes('debug') || prompt.includes('refactor')) {
        return 'deepseek'; // DeepSeek R1 is better for reasoning and code tasks
    }
    return 'gemini'; // Gemini for general tasks and documentation
}
async function invokeLLMWithRetry(prompt, options = {}, maxRetries = 3) {
    let lastError = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await invokeLLM(prompt, {
                ...options,
                requestId: `${options.requestId}-attempt-${attempt}`
            });
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.warn('LLM invocation attempt failed', {
                attempt,
                maxRetries,
                requestId: options.requestId,
                error: errorMessage
            });
            if (attempt < maxRetries) {
                // Exponential backoff
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    throw lastError || new Error('All retry attempts failed');
}
async function invokeLLMStreaming(prompt, options = {}, onChunk) {
    const { temperature = 0.7, maxTokens = 4000, model = 'auto', requestId = 'unknown' } = options;
    const provider = determineProvider(model, prompt);
    try {
        logger_1.logger.info('Invoking LLM with streaming', {
            requestId,
            provider,
            temperature,
            maxTokens
        });
        if (provider === 'gemini') {
            // Gemini doesn't support streaming in the same way, so we'll simulate it
            const content = await invokeGemini(prompt, { temperature, maxTokens, requestId });
            // Simulate streaming by chunking the response
            const chunkSize = 50;
            let fullContent = '';
            for (let i = 0; i < content.length; i += chunkSize) {
                const chunk = content.slice(i, i + chunkSize);
                fullContent += chunk;
                if (onChunk) {
                    onChunk(chunk);
                }
                // Small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            return fullContent;
        }
        else {
            // DeepSeek streaming implementation
            return await invokeDeepSeekStreaming(prompt, { temperature, maxTokens, requestId }, onChunk);
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error('LLM streaming invocation failed', { requestId, provider, error: errorMessage });
        throw new Error(`LLM streaming invocation failed: ${errorMessage}`);
    }
}
async function invokeDeepSeekStreaming(prompt, options, onChunk) {
    // Prefer OpenRouter if available, fallback to direct DeepSeek API
    const useOpenRouter = !!OPENROUTER_API_KEY;
    const apiBase = useOpenRouter ? OPENROUTER_API_BASE : DEEPSEEK_API_BASE;
    const apiKey = useOpenRouter ? OPENROUTER_API_KEY : DEEPSEEK_API_KEY;
    const model = useOpenRouter ? 'deepseek/deepseek-r1' : 'deepseek-reasoner';
    if (!apiKey) {
        throw new Error('No DeepSeek API key available (neither OpenRouter nor direct DeepSeek)');
    }
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
    };
    // Add OpenRouter specific headers
    if (useOpenRouter) {
        headers['HTTP-Referer'] = 'https://ai-agent-workflow.com';
        headers['X-Title'] = 'AI Agent Workflow System';
    }
    const response = await fetch(`${apiBase}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
            model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software architect and developer. Provide detailed, production-ready solutions.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: options.temperature || 0.7,
            max_tokens: options.maxTokens || 4000,
            stream: true,
        }),
    });
    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`${useOpenRouter ? 'OpenRouter' : 'DeepSeek'} API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    let fullContent = '';
    const reader = response.body?.getReader();
    if (!reader) {
        throw new Error('No response body reader available');
    }
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done)
                break;
            const chunk = new TextDecoder().decode(value);
            const lines = chunk.split('\n').filter(line => line.trim() !== '');
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]')
                        continue;
                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices?.[0]?.delta?.content || '';
                        if (content) {
                            fullContent += content;
                            if (onChunk) {
                                onChunk(content);
                            }
                        }
                    }
                    catch (e) {
                        // Ignore parsing errors for malformed chunks
                    }
                }
            }
        }
    }
    finally {
        reader.releaseLock();
    }
    return fullContent;
}
function validateLLMResponse(response, expectedFormat) {
    if (!response || response.trim().length === 0) {
        return false;
    }
    if (expectedFormat === 'json') {
        try {
            JSON.parse(response);
            return true;
        }
        catch {
            return false;
        }
    }
    if (expectedFormat === 'yaml') {
        // Basic YAML validation
        return response.includes(':') && !response.includes('{');
    }
    if (expectedFormat === 'markdown') {
        // Basic markdown validation
        return response.includes('#') || response.includes('*') || response.includes('-');
    }
    return true;
}
function extractJSONFromResponse(response) {
    try {
        // Try to parse the entire response as JSON
        return JSON.parse(response);
    }
    catch {
        // Try to extract JSON from code blocks
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[1]);
            }
            catch {
                // Fall through to next attempt
            }
        }
        // Try to extract JSON from the response
        const jsonStart = response.indexOf('{');
        const jsonEnd = response.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
            try {
                return JSON.parse(response.substring(jsonStart, jsonEnd + 1));
            }
            catch {
                // Fall through to error
            }
        }
        throw new Error('No valid JSON found in response');
    }
}
function sanitizePrompt(prompt) {
    // Remove potentially harmful content
    return prompt
        .replace(/\b(eval|exec|system|shell)\s*\(/gi, '[SANITIZED]')
        .replace(/\b(rm|del|delete)\s+/gi, '[SANITIZED]')
        .replace(/\b(DROP|DELETE|TRUNCATE)\s+/gi, '[SANITIZED]')
        .trim();
}
function buildPromptWithContext(basePrompt, context, maxContextLength = 8000) {
    let contextString = JSON.stringify(context, null, 2);
    // Truncate context if too long
    if (contextString.length > maxContextLength) {
        contextString = contextString.substring(0, maxContextLength) + '\n... [TRUNCATED]';
    }
    return `${basePrompt}\n\nContext:\n${contextString}`;
}
function estimateTokenCount(text) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
}
function splitLargePrompt(prompt, maxTokens = 3000) {
    const maxChars = maxTokens * 4;
    const chunks = [];
    if (prompt.length <= maxChars) {
        return [prompt];
    }
    // Split by paragraphs first
    const paragraphs = prompt.split('\n\n');
    let currentChunk = '';
    for (const paragraph of paragraphs) {
        if ((currentChunk + paragraph).length <= maxChars) {
            currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
        }
        else {
            if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = paragraph;
            }
            else {
                // Paragraph is too long, split by sentences
                const sentences = paragraph.split('. ');
                for (const sentence of sentences) {
                    if ((currentChunk + sentence).length <= maxChars) {
                        currentChunk += (currentChunk ? '. ' : '') + sentence;
                    }
                    else {
                        if (currentChunk) {
                            chunks.push(currentChunk);
                        }
                        currentChunk = sentence;
                    }
                }
            }
        }
    }
    if (currentChunk) {
        chunks.push(currentChunk);
    }
    return chunks;
}
//# sourceMappingURL=data:application/json;base64,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