/**
 * Security scanning utilities
 */

import { GeneratedFile, SecurityIssue, CodeError } from '../types';

export interface SecurityScanResult {
  issues: SecurityIssue[];
  errors: CodeError[];
  score: number; // 0-100, higher is better
}

export async function runSecurityScan(files: GeneratedFile[]): Promise<SecurityScanResult> {
  const issues: SecurityIssue[] = [];
  const errors: CodeError[] = [];
  let totalScore = 100;

  for (const file of files) {
    const fileResults = scanFile(file);
    issues.push(...fileResults.issues);
    errors.push(...fileResults.errors);
    totalScore -= fileResults.penalty;
  }

  // Additional cross-file security checks
  const crossFileResults = performCrossFileSecurityAnalysis(files);
  issues.push(...crossFileResults.issues);
  errors.push(...crossFileResults.errors);
  totalScore -= crossFileResults.penalty;

  return {
    issues,
    errors,
    score: Math.max(0, totalScore),
  };
}

function scanFile(file: GeneratedFile): {
  issues: SecurityIssue[];
  errors: CodeError[];
  penalty: number;
} {
  const issues: SecurityIssue[] = [];
  const errors: CodeError[] = [];
  let penalty = 0;

  const content = file.content;
  const lines = content.split('\n');

  // Check for common security vulnerabilities
  const securityChecks = [
    {
      pattern: /eval\s*\(/gi,
      severity: 'critical' as const,
      description: 'Use of eval() can lead to code injection vulnerabilities',
      recommendation: 'Avoid eval(). Use JSON.parse() for JSON data or safer alternatives',
      penalty: 20,
    },
    {
      pattern: /innerHTML\s*=/gi,
      severity: 'high' as const,
      description: 'innerHTML can lead to XSS vulnerabilities',
      recommendation: 'Use textContent or sanitize HTML content before setting innerHTML',
      penalty: 15,
    },
    {
      pattern: /document\.write\s*\(/gi,
      severity: 'high' as const,
      description: 'document.write can be exploited for XSS attacks',
      recommendation: 'Use modern DOM manipulation methods instead',
      penalty: 15,
    },
    {
      pattern: /exec\s*\(/gi,
      severity: 'critical' as const,
      description: 'exec() can lead to command injection vulnerabilities',
      recommendation: 'Avoid exec(). Use safer alternatives for command execution',
      penalty: 25,
    },
    {
      pattern: /\$\{[^}]*\}/g,
      severity: 'medium' as const,
      description: 'Template literals with user input can lead to injection attacks',
      recommendation: 'Sanitize user input before using in template literals',
      penalty: 10,
    },
    {
      pattern: /password\s*=\s*['"][^'"]*['"]/gi,
      severity: 'critical' as const,
      description: 'Hardcoded passwords detected',
      recommendation: 'Use environment variables or secure credential storage',
      penalty: 30,
    },
    {
      pattern: /api[_-]?key\s*=\s*['"][^'"]*['"]/gi,
      severity: 'high' as const,
      description: 'Hardcoded API keys detected',
      recommendation: 'Use environment variables for API keys',
      penalty: 20,
    },
    {
      pattern: /secret\s*=\s*['"][^'"]*['"]/gi,
      severity: 'high' as const,
      description: 'Hardcoded secrets detected',
      recommendation: 'Use secure secret management systems',
      penalty: 20,
    },
    {
      pattern: /http:\/\/[^'">\s]+/gi,
      severity: 'medium' as const,
      description: 'Insecure HTTP URLs detected',
      recommendation: 'Use HTTPS for all external communications',
      penalty: 5,
    },
    {
      pattern: /SELECT\s+\*\s+FROM\s+\w+\s+WHERE\s+.*=\s*['"]?\$\{/gi,
      severity: 'critical' as const,
      description: 'Potential SQL injection vulnerability',
      recommendation: 'Use parameterized queries or prepared statements',
      penalty: 25,
    },
  ];

  securityChecks.forEach(check => {
    const matches = content.match(check.pattern);
    if (matches) {
      matches.forEach(match => {
        const lineNumber = findLineNumber(content, match);
        
        issues.push({
          severity: check.severity,
          description: check.description,
          file: file.path,
          line: lineNumber,
          recommendation: check.recommendation,
        });

        if (check.severity === 'critical') {
          errors.push({
            severity: 'critical',
            message: check.description,
            file: file.path,
            line: lineNumber,
            rule: 'security_vulnerability',
          });
        }

        penalty += check.penalty;
      });
    }
  });

  // Language-specific security checks
  switch (file.language.toLowerCase()) {
    case 'javascript':
    case 'typescript':
      scanJavaScriptSecurity(file, issues, errors);
      break;
    case 'python':
      scanPythonSecurity(file, issues, errors);
      break;
    case 'html':
      scanHTMLSecurity(file, issues, errors);
      break;
  }

  return { issues, errors, penalty };
}

function scanJavaScriptSecurity(file: GeneratedFile, issues: SecurityIssue[], errors: CodeError[]): void {
  const content = file.content;

  // Check for unsafe DOM manipulation
  if (content.includes('dangerouslySetInnerHTML')) {
    issues.push({
      severity: 'high',
      description: 'dangerouslySetInnerHTML can lead to XSS if not properly sanitized',
      file: file.path,
      recommendation: 'Ensure content is properly sanitized before using dangerouslySetInnerHTML',
    });
  }

  // Check for localStorage usage without encryption
  if (content.includes('localStorage.setItem') && !content.includes('encrypt')) {
    issues.push({
      severity: 'medium',
      description: 'Sensitive data stored in localStorage without encryption',
      file: file.path,
      recommendation: 'Encrypt sensitive data before storing in localStorage',
    });
  }

  // Check for CORS issues
  if (content.includes('Access-Control-Allow-Origin: *')) {
    issues.push({
      severity: 'medium',
      description: 'Overly permissive CORS policy',
      file: file.path,
      recommendation: 'Restrict CORS to specific domains instead of using wildcard',
    });
  }

  // Check for missing CSRF protection
  if (content.includes('POST') && !content.includes('csrf') && !content.includes('token')) {
    issues.push({
      severity: 'medium',
      description: 'POST requests without CSRF protection',
      file: file.path,
      recommendation: 'Implement CSRF tokens for state-changing operations',
    });
  }
}

function scanPythonSecurity(file: GeneratedFile, issues: SecurityIssue[], errors: CodeError[]): void {
  const content = file.content;

  // Check for pickle usage
  if (content.includes('pickle.loads') || content.includes('pickle.load')) {
    issues.push({
      severity: 'high',
      description: 'pickle.loads can execute arbitrary code',
      file: file.path,
      recommendation: 'Use safer serialization formats like JSON',
    });
  }

  // Check for shell command injection
  if (content.includes('os.system') || content.includes('subprocess.call')) {
    issues.push({
      severity: 'high',
      description: 'Shell command execution can lead to command injection',
      file: file.path,
      recommendation: 'Use subprocess with shell=False and validate inputs',
    });
  }

  // Check for SQL injection in Python
  if (content.includes('execute(') && content.includes('%s') && !content.includes('parameterized')) {
    issues.push({
      severity: 'critical',
      description: 'Potential SQL injection vulnerability',
      file: file.path,
      recommendation: 'Use parameterized queries instead of string formatting',
    });
  }
}

function scanHTMLSecurity(file: GeneratedFile, issues: SecurityIssue[], errors: CodeError[]): void {
  const content = file.content;

  // Check for missing CSP
  if (content.includes('<html>') && !content.includes('Content-Security-Policy')) {
    issues.push({
      severity: 'medium',
      description: 'Missing Content Security Policy',
      file: file.path,
      recommendation: 'Add CSP headers to prevent XSS attacks',
    });
  }

  // Check for inline scripts
  if (content.includes('<script>') && !content.includes('nonce=')) {
    issues.push({
      severity: 'medium',
      description: 'Inline scripts without nonce can be blocked by CSP',
      file: file.path,
      recommendation: 'Use external script files or add nonce attributes',
    });
  }

  // Check for missing HTTPS
  if (content.includes('http://') && !content.includes('localhost')) {
    issues.push({
      severity: 'medium',
      description: 'HTTP links in production can be insecure',
      file: file.path,
      recommendation: 'Use HTTPS for all external links',
    });
  }
}

function performCrossFileSecurityAnalysis(files: GeneratedFile[]): {
  issues: SecurityIssue[];
  errors: CodeError[];
  penalty: number;
} {
  const issues: SecurityIssue[] = [];
  const errors: CodeError[] = [];
  let penalty = 0;

  // Check for missing authentication
  const hasAuthFiles = files.some(f => 
    f.path.includes('auth') || 
    f.content.includes('authenticate') || 
    f.content.includes('jwt')
  );

  const hasAPIFiles = files.some(f => 
    f.content.includes('app.get') || 
    f.content.includes('app.post') || 
    f.content.includes('router.')
  );

  if (hasAPIFiles && !hasAuthFiles) {
    issues.push({
      severity: 'high',
      description: 'API endpoints detected without authentication mechanism',
      recommendation: 'Implement authentication for API endpoints',
    });
    penalty += 15;
  }

  // Check for missing input validation
  const hasValidationFiles = files.some(f => 
    f.content.includes('validate') || 
    f.content.includes('joi') || 
    f.content.includes('yup')
  );

  if (hasAPIFiles && !hasValidationFiles) {
    issues.push({
      severity: 'medium',
      description: 'API endpoints without input validation',
      recommendation: 'Implement input validation for all API endpoints',
    });
    penalty += 10;
  }

  // Check for missing rate limiting
  const hasRateLimiting = files.some(f => 
    f.content.includes('rate-limit') || 
    f.content.includes('rateLimit')
  );

  if (hasAPIFiles && !hasRateLimiting) {
    issues.push({
      severity: 'medium',
      description: 'API endpoints without rate limiting',
      recommendation: 'Implement rate limiting to prevent abuse',
    });
    penalty += 10;
  }

  // Check for missing error handling
  const hasErrorHandling = files.some(f => 
    f.content.includes('try') && f.content.includes('catch')
  );

  if (hasAPIFiles && !hasErrorHandling) {
    issues.push({
      severity: 'medium',
      description: 'Missing comprehensive error handling',
      recommendation: 'Implement proper error handling and logging',
    });
    penalty += 10;
  }

  return { issues, errors, penalty };
}

function findLineNumber(content: string, searchString: string): number {
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchString)) {
      return i + 1;
    }
  }
  return 1;
}

export function generateSecurityReport(scanResult: SecurityScanResult): string {
  const { issues, score } = scanResult;
  
  let report = `# Security Scan Report\n\n`;
  report += `**Overall Security Score: ${score}/100**\n\n`;

  if (issues.length === 0) {
    report += `✅ No security issues detected!\n\n`;
    return report;
  }

  const criticalIssues = issues.filter(i => i.severity === 'critical');
  const highIssues = issues.filter(i => i.severity === 'high');
  const mediumIssues = issues.filter(i => i.severity === 'medium');
  const lowIssues = issues.filter(i => i.severity === 'low');

  if (criticalIssues.length > 0) {
    report += `## 🚨 Critical Issues (${criticalIssues.length})\n\n`;
    criticalIssues.forEach(issue => {
      report += `- **${issue.description}**\n`;
      if (issue.file) report += `  - File: ${issue.file}\n`;
      if (issue.line) report += `  - Line: ${issue.line}\n`;
      report += `  - Recommendation: ${issue.recommendation}\n\n`;
    });
  }

  if (highIssues.length > 0) {
    report += `## ⚠️ High Priority Issues (${highIssues.length})\n\n`;
    highIssues.forEach(issue => {
      report += `- **${issue.description}**\n`;
      if (issue.file) report += `  - File: ${issue.file}\n`;
      if (issue.line) report += `  - Line: ${issue.line}\n`;
      report += `  - Recommendation: ${issue.recommendation}\n\n`;
    });
  }

  if (mediumIssues.length > 0) {
    report += `## ⚡ Medium Priority Issues (${mediumIssues.length})\n\n`;
    mediumIssues.forEach(issue => {
      report += `- **${issue.description}**\n`;
      if (issue.file) report += `  - File: ${issue.file}\n`;
      if (issue.line) report += `  - Line: ${issue.line}\n`;
      report += `  - Recommendation: ${issue.recommendation}\n\n`;
    });
  }

  if (lowIssues.length > 0) {
    report += `## 💡 Low Priority Issues (${lowIssues.length})\n\n`;
    lowIssues.forEach(issue => {
      report += `- **${issue.description}**\n`;
      if (issue.file) report += `  - File: ${issue.file}\n`;
      if (issue.line) report += `  - Line: ${issue.line}\n`;
      report += `  - Recommendation: ${issue.recommendation}\n\n`;
    });
  }

  return report;
}
