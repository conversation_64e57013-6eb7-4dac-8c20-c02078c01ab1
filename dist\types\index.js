"use strict";
/**
 * Core types and interfaces for the AI Agent Workflow System
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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