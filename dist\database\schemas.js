"use strict";
/**
 * DynamoDB table schemas and data access patterns
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserSessionRecord = exports.createReviewRecord = exports.createArtifactRecord = exports.createTaskRecord = exports.createWorkflowStateRecord = exports.ACCESS_PATTERNS = exports.WORKFLOW_TABLE_CONFIG = void 0;
const aws_dynamodb_1 = require("aws-cdk-lib/aws-dynamodb");
// Table definitions for CDK
exports.WORKFLOW_TABLE_CONFIG = {
    tableName: 'AIAgentWorkflowTable',
    partitionKey: { name: 'PK', type: aws_dynamodb_1.AttributeType.STRING },
    sortKey: { name: 'SK', type: aws_dynamodb_1.AttributeType.STRING },
    billingMode: aws_dynamodb_1.BillingMode.PAY_PER_REQUEST,
    timeToLiveAttribute: 'ttl',
    globalSecondaryIndexes: [
        {
            indexName: 'GSI1',
            partitionKey: { name: 'GSI1PK', type: aws_dynamodb_1.AttributeType.STRING },
            sortKey: { name: 'GSI1SK', type: aws_dynamodb_1.AttributeType.STRING },
            projectionType: aws_dynamodb_1.ProjectionType.ALL,
        },
        {
            indexName: 'GSI2',
            partitionKey: { name: 'GSI2PK', type: aws_dynamodb_1.AttributeType.STRING },
            sortKey: { name: 'GSI2SK', type: aws_dynamodb_1.AttributeType.STRING },
            projectionType: aws_dynamodb_1.ProjectionType.ALL,
        },
    ],
    pointInTimeRecovery: true,
    encryption: 'AWS_MANAGED',
};
// Data access patterns
exports.ACCESS_PATTERNS = {
    // Workflow State
    getWorkflowState: (sessionId) => ({
        PK: `SESSION#${sessionId}`,
        SK: { beginsWith: 'WORKFLOW#' },
    }),
    // Tasks
    getTasksBySession: (sessionId) => ({
        PK: `SESSION#${sessionId}`,
        SK: { beginsWith: 'TASK#' },
    }),
    getTasksByStatus: (status) => ({
        IndexName: 'GSI1',
        GSI1PK: `STATUS#${status}`,
    }),
    getTasksByAgent: (agent, status) => ({
        IndexName: 'GSI2',
        GSI2PK: `AGENT#${agent}`,
        GSI2SK: status ? { beginsWith: `STATUS#${status}#` } : undefined,
    }),
    // Artifacts
    getArtifactsBySession: (sessionId) => ({
        PK: `SESSION#${sessionId}`,
        SK: { beginsWith: 'ARTIFACT#' },
    }),
    getArtifactsByTask: (taskId) => ({
        IndexName: 'GSI1',
        GSI1PK: `TASK#${taskId}`,
    }),
    // Reviews
    getReviewsBySession: (sessionId) => ({
        PK: `SESSION#${sessionId}`,
        SK: { beginsWith: 'REVIEW#' },
    }),
    getReviewsByTask: (taskId) => ({
        IndexName: 'GSI1',
        GSI1PK: `TASK#${taskId}`,
    }),
    // User Sessions
    getUserSessions: (userId) => ({
        PK: `USER#${userId}`,
        SK: { beginsWith: 'SESSION#' },
    }),
    getSessionsByDate: (date) => ({
        IndexName: 'GSI1',
        GSI1PK: `CREATED#${date}`,
    }),
};
// Helper functions for creating records
const createWorkflowStateRecord = (sessionId, workflowState, ttlHours = 168 // 7 days default
) => ({
    PK: `SESSION#${sessionId}`,
    SK: `WORKFLOW#${Date.now()}`,
    GSI1PK: `STATUS#${workflowState.status}`,
    GSI1SK: `CREATED#${workflowState.createdAt}`,
    sessionId,
    currentIteration: workflowState.currentIteration,
    maxIterations: workflowState.maxIterations,
    status: workflowState.status,
    specification: workflowState.specification ? JSON.stringify(workflowState.specification) : undefined,
    taskPlan: workflowState.taskPlan ? JSON.stringify(workflowState.taskPlan) : undefined,
    finalOutput: workflowState.finalOutput ? JSON.stringify(workflowState.finalOutput) : undefined,
    createdAt: workflowState.createdAt,
    updatedAt: workflowState.updatedAt,
    ttl: Math.floor(Date.now() / 1000) + (ttlHours * 3600),
});
exports.createWorkflowStateRecord = createWorkflowStateRecord;
const createTaskRecord = (sessionId, task) => ({
    PK: `SESSION#${sessionId}`,
    SK: `TASK#${task.taskId}`,
    GSI1PK: `STATUS#${task.status}`,
    GSI1SK: `PRIORITY#${task.priority}#${task.createdAt || new Date().toISOString()}`,
    GSI2PK: `AGENT#${task.agent}`,
    GSI2SK: `STATUS#${task.status}#${task.createdAt || new Date().toISOString()}`,
    sessionId,
    taskId: task.taskId,
    taskName: task.taskName,
    description: task.description,
    agent: task.agent,
    dependencies: JSON.stringify(task.dependencies || []),
    status: task.status,
    priority: task.priority,
    estimatedDuration: task.estimatedDuration,
    metadata: JSON.stringify(task.metadata || {}),
    createdAt: task.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    completedAt: task.status === 'completed' ? new Date().toISOString() : undefined,
});
exports.createTaskRecord = createTaskRecord;
const createArtifactRecord = (sessionId, taskId, artifact, s3Key) => ({
    PK: `SESSION#${sessionId}`,
    SK: `ARTIFACT#${taskId}#${Date.now()}`,
    GSI1PK: `TASK#${taskId}`,
    GSI1SK: `VERSION#${Date.now()}`,
    sessionId,
    taskId,
    s3Key,
    files: JSON.stringify(artifact.files || []),
    errors: JSON.stringify(artifact.errors || []),
    warnings: JSON.stringify(artifact.warnings || []),
    metadata: JSON.stringify(artifact.metadata || {}),
    createdAt: new Date().toISOString(),
    version: artifact.metadata?.version || '1.0.0',
});
exports.createArtifactRecord = createArtifactRecord;
const createReviewRecord = (sessionId, taskId, review, reviewedBy) => ({
    PK: `SESSION#${sessionId}`,
    SK: `REVIEW#${taskId}#${Date.now()}`,
    GSI1PK: `TASK#${taskId}`,
    GSI1SK: `QUALITY#${review.overallQuality}#${Date.now()}`,
    sessionId,
    taskId,
    overallQuality: review.overallQuality,
    errors: JSON.stringify(review.errors || []),
    suggestions: JSON.stringify(review.suggestions || []),
    securityIssues: JSON.stringify(review.securityIssues || []),
    performanceIssues: JSON.stringify(review.performanceIssues || []),
    updatedSpecDelta: review.updatedSpecDelta ? JSON.stringify(review.updatedSpecDelta) : undefined,
    updatedTasks: review.updatedTasks ? JSON.stringify(review.updatedTasks) : undefined,
    createdAt: new Date().toISOString(),
    reviewedBy,
});
exports.createReviewRecord = createReviewRecord;
const createUserSessionRecord = (userId, sessionId, userRequest, ttlHours = 720 // 30 days default
) => ({
    PK: `USER#${userId}`,
    SK: `SESSION#${sessionId}`,
    GSI1PK: `CREATED#${new Date().toISOString().split('T')[0]}`,
    GSI1SK: `USER#${userId}`,
    userId,
    sessionId,
    userRequest: JSON.stringify(userRequest),
    status: 'active',
    createdAt: new Date().toISOString(),
    lastAccessedAt: new Date().toISOString(),
    ttl: Math.floor(Date.now() / 1000) + (ttlHours * 3600),
});
exports.createUserSessionRecord = createUserSessionRecord;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2NoZW1hcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uL3NyYy9kYXRhYmFzZS9zY2hlbWFzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7R0FFRzs7O0FBRUgsMkRBQXNGO0FBeUZ0Riw0QkFBNEI7QUFDZixRQUFBLHFCQUFxQixHQUFHO0lBQ25DLFNBQVMsRUFBRSxzQkFBc0I7SUFDakMsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7SUFDeEQsT0FBTyxFQUFFLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7SUFDbkQsV0FBVyxFQUFFLDBCQUFXLENBQUMsZUFBZTtJQUN4QyxtQkFBbUIsRUFBRSxLQUFLO0lBQzFCLHNCQUFzQixFQUFFO1FBQ3RCO1lBQ0UsU0FBUyxFQUFFLE1BQU07WUFDakIsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7WUFDNUQsT0FBTyxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7WUFDdkQsY0FBYyxFQUFFLDZCQUFjLENBQUMsR0FBRztTQUNuQztRQUNEO1lBQ0UsU0FBUyxFQUFFLE1BQU07WUFDakIsWUFBWSxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7WUFDNUQsT0FBTyxFQUFFLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsNEJBQWEsQ0FBQyxNQUFNLEVBQUU7WUFDdkQsY0FBYyxFQUFFLDZCQUFjLENBQUMsR0FBRztTQUNuQztLQUNGO0lBQ0QsbUJBQW1CLEVBQUUsSUFBSTtJQUN6QixVQUFVLEVBQUUsYUFBYTtDQUMxQixDQUFDO0FBRUYsdUJBQXVCO0FBQ1YsUUFBQSxlQUFlLEdBQUc7SUFDN0IsaUJBQWlCO0lBQ2pCLGdCQUFnQixFQUFFLENBQUMsU0FBaUIsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUN4QyxFQUFFLEVBQUUsV0FBVyxTQUFTLEVBQUU7UUFDMUIsRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLFdBQVcsRUFBRTtLQUNoQyxDQUFDO0lBRUYsUUFBUTtJQUNSLGlCQUFpQixFQUFFLENBQUMsU0FBaUIsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUN6QyxFQUFFLEVBQUUsV0FBVyxTQUFTLEVBQUU7UUFDMUIsRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLE9BQU8sRUFBRTtLQUM1QixDQUFDO0lBRUYsZ0JBQWdCLEVBQUUsQ0FBQyxNQUFjLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDckMsU0FBUyxFQUFFLE1BQU07UUFDakIsTUFBTSxFQUFFLFVBQVUsTUFBTSxFQUFFO0tBQzNCLENBQUM7SUFFRixlQUFlLEVBQUUsQ0FBQyxLQUFhLEVBQUUsTUFBZSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQ3BELFNBQVMsRUFBRSxNQUFNO1FBQ2pCLE1BQU0sRUFBRSxTQUFTLEtBQUssRUFBRTtRQUN4QixNQUFNLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLFVBQVUsRUFBRSxVQUFVLE1BQU0sR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDLFNBQVM7S0FDakUsQ0FBQztJQUVGLFlBQVk7SUFDWixxQkFBcUIsRUFBRSxDQUFDLFNBQWlCLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDN0MsRUFBRSxFQUFFLFdBQVcsU0FBUyxFQUFFO1FBQzFCLEVBQUUsRUFBRSxFQUFFLFVBQVUsRUFBRSxXQUFXLEVBQUU7S0FDaEMsQ0FBQztJQUVGLGtCQUFrQixFQUFFLENBQUMsTUFBYyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQ3ZDLFNBQVMsRUFBRSxNQUFNO1FBQ2pCLE1BQU0sRUFBRSxRQUFRLE1BQU0sRUFBRTtLQUN6QixDQUFDO0lBRUYsVUFBVTtJQUNWLG1CQUFtQixFQUFFLENBQUMsU0FBaUIsRUFBRSxFQUFFLENBQUMsQ0FBQztRQUMzQyxFQUFFLEVBQUUsV0FBVyxTQUFTLEVBQUU7UUFDMUIsRUFBRSxFQUFFLEVBQUUsVUFBVSxFQUFFLFNBQVMsRUFBRTtLQUM5QixDQUFDO0lBRUYsZ0JBQWdCLEVBQUUsQ0FBQyxNQUFjLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDckMsU0FBUyxFQUFFLE1BQU07UUFDakIsTUFBTSxFQUFFLFFBQVEsTUFBTSxFQUFFO0tBQ3pCLENBQUM7SUFFRixnQkFBZ0I7SUFDaEIsZUFBZSxFQUFFLENBQUMsTUFBYyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQ3BDLEVBQUUsRUFBRSxRQUFRLE1BQU0sRUFBRTtRQUNwQixFQUFFLEVBQUUsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFO0tBQy9CLENBQUM7SUFFRixpQkFBaUIsRUFBRSxDQUFDLElBQVksRUFBRSxFQUFFLENBQUMsQ0FBQztRQUNwQyxTQUFTLEVBQUUsTUFBTTtRQUNqQixNQUFNLEVBQUUsV0FBVyxJQUFJLEVBQUU7S0FDMUIsQ0FBQztDQUNILENBQUM7QUFFRix3Q0FBd0M7QUFDakMsTUFBTSx5QkFBeUIsR0FBRyxDQUN2QyxTQUFpQixFQUNqQixhQUFrQixFQUNsQixXQUFtQixHQUFHLENBQUMsaUJBQWlCO0VBQ25CLEVBQUUsQ0FBQyxDQUFDO0lBQ3pCLEVBQUUsRUFBRSxXQUFXLFNBQVMsRUFBRTtJQUMxQixFQUFFLEVBQUUsWUFBWSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7SUFDNUIsTUFBTSxFQUFFLFVBQVUsYUFBYSxDQUFDLE1BQU0sRUFBRTtJQUN4QyxNQUFNLEVBQUUsV0FBVyxhQUFhLENBQUMsU0FBUyxFQUFFO0lBQzVDLFNBQVM7SUFDVCxnQkFBZ0IsRUFBRSxhQUFhLENBQUMsZ0JBQWdCO0lBQ2hELGFBQWEsRUFBRSxhQUFhLENBQUMsYUFBYTtJQUMxQyxNQUFNLEVBQUUsYUFBYSxDQUFDLE1BQU07SUFDNUIsYUFBYSxFQUFFLGFBQWEsQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO0lBQ3BHLFFBQVEsRUFBRSxhQUFhLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUztJQUNyRixXQUFXLEVBQUUsYUFBYSxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVM7SUFDOUYsU0FBUyxFQUFFLGFBQWEsQ0FBQyxTQUFTO0lBQ2xDLFNBQVMsRUFBRSxhQUFhLENBQUMsU0FBUztJQUNsQyxHQUFHLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxRQUFRLEdBQUcsSUFBSSxDQUFDO0NBQ3ZELENBQUMsQ0FBQztBQW5CVSxRQUFBLHlCQUF5Qiw2QkFtQm5DO0FBRUksTUFBTSxnQkFBZ0IsR0FBRyxDQUM5QixTQUFpQixFQUNqQixJQUFTLEVBQ0csRUFBRSxDQUFDLENBQUM7SUFDaEIsRUFBRSxFQUFFLFdBQVcsU0FBUyxFQUFFO0lBQzFCLEVBQUUsRUFBRSxRQUFRLElBQUksQ0FBQyxNQUFNLEVBQUU7SUFDekIsTUFBTSxFQUFFLFVBQVUsSUFBSSxDQUFDLE1BQU0sRUFBRTtJQUMvQixNQUFNLEVBQUUsWUFBWSxJQUFJLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxTQUFTLElBQUksSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUUsRUFBRTtJQUNqRixNQUFNLEVBQUUsU0FBUyxJQUFJLENBQUMsS0FBSyxFQUFFO0lBQzdCLE1BQU0sRUFBRSxVQUFVLElBQUksQ0FBQyxNQUFNLElBQUksSUFBSSxDQUFDLFNBQVMsSUFBSSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRSxFQUFFO0lBQzdFLFNBQVM7SUFDVCxNQUFNLEVBQUUsSUFBSSxDQUFDLE1BQU07SUFDbkIsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO0lBQ3ZCLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVztJQUM3QixLQUFLLEVBQUUsSUFBSSxDQUFDLEtBQUs7SUFDakIsWUFBWSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLFlBQVksSUFBSSxFQUFFLENBQUM7SUFDckQsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNO0lBQ25CLFFBQVEsRUFBRSxJQUFJLENBQUMsUUFBUTtJQUN2QixpQkFBaUIsRUFBRSxJQUFJLENBQUMsaUJBQWlCO0lBQ3pDLFFBQVEsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksQ0FBQyxRQUFRLElBQUksRUFBRSxDQUFDO0lBQzdDLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUyxJQUFJLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO0lBQ3JELFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtJQUNuQyxXQUFXLEVBQUUsSUFBSSxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFLENBQUMsQ0FBQyxDQUFDLFNBQVM7Q0FDaEYsQ0FBQyxDQUFDO0FBdkJVLFFBQUEsZ0JBQWdCLG9CQXVCMUI7QUFFSSxNQUFNLG9CQUFvQixHQUFHLENBQ2xDLFNBQWlCLEVBQ2pCLE1BQWMsRUFDZCxRQUFhLEVBQ2IsS0FBYSxFQUNHLEVBQUUsQ0FBQyxDQUFDO0lBQ3BCLEVBQUUsRUFBRSxXQUFXLFNBQVMsRUFBRTtJQUMxQixFQUFFLEVBQUUsWUFBWSxNQUFNLElBQUksSUFBSSxDQUFDLEdBQUcsRUFBRSxFQUFFO0lBQ3RDLE1BQU0sRUFBRSxRQUFRLE1BQU0sRUFBRTtJQUN4QixNQUFNLEVBQUUsV0FBVyxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7SUFDL0IsU0FBUztJQUNULE1BQU07SUFDTixLQUFLO0lBQ0wsS0FBSyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUM7SUFDM0MsTUFBTSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLE1BQU0sSUFBSSxFQUFFLENBQUM7SUFDN0MsUUFBUSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFFBQVEsSUFBSSxFQUFFLENBQUM7SUFDakQsUUFBUSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFFBQVEsSUFBSSxFQUFFLENBQUM7SUFDakQsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFLENBQUMsV0FBVyxFQUFFO0lBQ25DLE9BQU8sRUFBRSxRQUFRLENBQUMsUUFBUSxFQUFFLE9BQU8sSUFBSSxPQUFPO0NBQy9DLENBQUMsQ0FBQztBQW5CVSxRQUFBLG9CQUFvQix3QkFtQjlCO0FBRUksTUFBTSxrQkFBa0IsR0FBRyxDQUNoQyxTQUFpQixFQUNqQixNQUFjLEVBQ2QsTUFBVyxFQUNYLFVBQWtCLEVBQ0osRUFBRSxDQUFDLENBQUM7SUFDbEIsRUFBRSxFQUFFLFdBQVcsU0FBUyxFQUFFO0lBQzFCLEVBQUUsRUFBRSxVQUFVLE1BQU0sSUFBSSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7SUFDcEMsTUFBTSxFQUFFLFFBQVEsTUFBTSxFQUFFO0lBQ3hCLE1BQU0sRUFBRSxXQUFXLE1BQU0sQ0FBQyxjQUFjLElBQUksSUFBSSxDQUFDLEdBQUcsRUFBRSxFQUFFO0lBQ3hELFNBQVM7SUFDVCxNQUFNO0lBQ04sY0FBYyxFQUFFLE1BQU0sQ0FBQyxjQUFjO0lBQ3JDLE1BQU0sRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxNQUFNLElBQUksRUFBRSxDQUFDO0lBQzNDLFdBQVcsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxXQUFXLElBQUksRUFBRSxDQUFDO0lBQ3JELGNBQWMsRUFBRSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxjQUFjLElBQUksRUFBRSxDQUFDO0lBQzNELGlCQUFpQixFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLGlCQUFpQixJQUFJLEVBQUUsQ0FBQztJQUNqRSxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVM7SUFDL0YsWUFBWSxFQUFFLE1BQU0sQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO0lBQ25GLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtJQUNuQyxVQUFVO0NBQ1gsQ0FBQyxDQUFDO0FBckJVLFFBQUEsa0JBQWtCLHNCQXFCNUI7QUFFSSxNQUFNLHVCQUF1QixHQUFHLENBQ3JDLE1BQWMsRUFDZCxTQUFpQixFQUNqQixXQUFnQixFQUNoQixXQUFtQixHQUFHLENBQUMsa0JBQWtCO0VBQ3RCLEVBQUUsQ0FBQyxDQUFDO0lBQ3ZCLEVBQUUsRUFBRSxRQUFRLE1BQU0sRUFBRTtJQUNwQixFQUFFLEVBQUUsV0FBVyxTQUFTLEVBQUU7SUFDMUIsTUFBTSxFQUFFLFdBQVcsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUU7SUFDM0QsTUFBTSxFQUFFLFFBQVEsTUFBTSxFQUFFO0lBQ3hCLE1BQU07SUFDTixTQUFTO0lBQ1QsV0FBVyxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVyxDQUFDO0lBQ3hDLE1BQU0sRUFBRSxRQUFRO0lBQ2hCLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRTtJQUNuQyxjQUFjLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7SUFDeEMsR0FBRyxFQUFFLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQztDQUN2RCxDQUFDLENBQUM7QUFqQlUsUUFBQSx1QkFBdUIsMkJBaUJqQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRHluYW1vREIgdGFibGUgc2NoZW1hcyBhbmQgZGF0YSBhY2Nlc3MgcGF0dGVybnNcbiAqL1xuXG5pbXBvcnQgeyBBdHRyaWJ1dGVUeXBlLCBCaWxsaW5nTW9kZSwgUHJvamVjdGlvblR5cGUgfSBmcm9tICdhd3MtY2RrLWxpYi9hd3MtZHluYW1vZGInO1xuXG5leHBvcnQgaW50ZXJmYWNlIFdvcmtmbG93U3RhdGVSZWNvcmQge1xuICBQSzogc3RyaW5nOyAvLyBTRVNTSU9OI3tzZXNzaW9uSWR9XG4gIFNLOiBzdHJpbmc7IC8vIFdPUktGTE9XI3t0aW1lc3RhbXB9XG4gIEdTSTFQSzogc3RyaW5nOyAvLyBTVEFUVVMje3N0YXR1c31cbiAgR1NJMVNLOiBzdHJpbmc7IC8vIENSRUFURUQje2NyZWF0ZWRBdH1cbiAgc2Vzc2lvbklkOiBzdHJpbmc7XG4gIGN1cnJlbnRJdGVyYXRpb246IG51bWJlcjtcbiAgbWF4SXRlcmF0aW9uczogbnVtYmVyO1xuICBzdGF0dXM6ICdpbml0aWFsaXppbmcnIHwgJ2VuaGFuY2luZycgfCAnY3JlYXRpbmcnIHwgJ3Jldmlld2luZycgfCAnZmluYWxpemluZycgfCAnY29tcGxldGVkJyB8ICdmYWlsZWQnO1xuICBzcGVjaWZpY2F0aW9uPzogc3RyaW5nOyAvLyBKU09OIHN0cmluZ2lmaWVkIERldGFpbGVkU3BlY2lmaWNhdGlvblxuICB0YXNrUGxhbj86IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBUYXNrUGxhblxuICBmaW5hbE91dHB1dD86IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBGaW5hbE91dHB1dFxuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gIHR0bD86IG51bWJlcjsgLy8gVFRMIGZvciBhdXRvbWF0aWMgY2xlYW51cFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRhc2tSZWNvcmQge1xuICBQSzogc3RyaW5nOyAvLyBTRVNTSU9OI3tzZXNzaW9uSWR9XG4gIFNLOiBzdHJpbmc7IC8vIFRBU0sje3Rhc2tJZH1cbiAgR1NJMVBLOiBzdHJpbmc7IC8vIFNUQVRVUyN7c3RhdHVzfVxuICBHU0kxU0s6IHN0cmluZzsgLy8gUFJJT1JJVFkje3ByaW9yaXR5fSN7Y3JlYXRlZEF0fVxuICBHU0kyUEs6IHN0cmluZzsgLy8gQUdFTlQje2FnZW50fVxuICBHU0kyU0s6IHN0cmluZzsgLy8gU1RBVFVTI3tzdGF0dXN9I3tjcmVhdGVkQXR9XG4gIHNlc3Npb25JZDogc3RyaW5nO1xuICB0YXNrSWQ6IHN0cmluZztcbiAgdGFza05hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgYWdlbnQ6ICdDb2RlQ3JlYXRvcicgfCAnUmV2aWV3UmVmaW5lJyB8ICdGaW5hbGl6ZXInO1xuICBkZXBlbmRlbmNpZXM6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBhcnJheVxuICBzdGF0dXM6ICdwZW5kaW5nJyB8ICdpbl9wcm9ncmVzcycgfCAnY29tcGxldGVkJyB8ICdmYWlsZWQnO1xuICBwcmlvcml0eTogbnVtYmVyO1xuICBlc3RpbWF0ZWREdXJhdGlvbj86IG51bWJlcjtcbiAgbWV0YWRhdGE6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBtZXRhZGF0YVxuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gIGNvbXBsZXRlZEF0Pzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFydGlmYWN0UmVjb3JkIHtcbiAgUEs6IHN0cmluZzsgLy8gU0VTU0lPTiN7c2Vzc2lvbklkfVxuICBTSzogc3RyaW5nOyAvLyBBUlRJRkFDVCN7dGFza0lkfSN7dGltZXN0YW1wfVxuICBHU0kxUEs6IHN0cmluZzsgLy8gVEFTSyN7dGFza0lkfVxuICBHU0kxU0s6IHN0cmluZzsgLy8gVkVSU0lPTiN7dGltZXN0YW1wfVxuICBzZXNzaW9uSWQ6IHN0cmluZztcbiAgdGFza0lkOiBzdHJpbmc7XG4gIHMzS2V5OiBzdHJpbmc7IC8vIFMzIGtleSB3aGVyZSB0aGUgYXJ0aWZhY3QgaXMgc3RvcmVkXG4gIGZpbGVzOiBzdHJpbmc7IC8vIEpTT04gc3RyaW5naWZpZWQgR2VuZXJhdGVkRmlsZVtdXG4gIGVycm9yczogc3RyaW5nOyAvLyBKU09OIHN0cmluZ2lmaWVkIENvZGVFcnJvcltdXG4gIHdhcm5pbmdzOiBzdHJpbmc7IC8vIEpTT04gc3RyaW5naWZpZWQgQ29kZVdhcm5pbmdbXVxuICBtZXRhZGF0YTogc3RyaW5nOyAvLyBKU09OIHN0cmluZ2lmaWVkIG1ldGFkYXRhXG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB2ZXJzaW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmV2aWV3UmVjb3JkIHtcbiAgUEs6IHN0cmluZzsgLy8gU0VTU0lPTiN7c2Vzc2lvbklkfVxuICBTSzogc3RyaW5nOyAvLyBSRVZJRVcje3Rhc2tJZH0je3RpbWVzdGFtcH1cbiAgR1NJMVBLOiBzdHJpbmc7IC8vIFRBU0sje3Rhc2tJZH1cbiAgR1NJMVNLOiBzdHJpbmc7IC8vIFFVQUxJVFkje292ZXJhbGxRdWFsaXR5fSN7dGltZXN0YW1wfVxuICBzZXNzaW9uSWQ6IHN0cmluZztcbiAgdGFza0lkOiBzdHJpbmc7XG4gIG92ZXJhbGxRdWFsaXR5OiAnZXhjZWxsZW50JyB8ICdnb29kJyB8ICdmYWlyJyB8ICdwb29yJztcbiAgZXJyb3JzOiBzdHJpbmc7IC8vIEpTT04gc3RyaW5naWZpZWQgQ29kZUVycm9yW11cbiAgc3VnZ2VzdGlvbnM6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBTdWdnZXN0aW9uW11cbiAgc2VjdXJpdHlJc3N1ZXM6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBTZWN1cml0eUlzc3VlW11cbiAgcGVyZm9ybWFuY2VJc3N1ZXM6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBQZXJmb3JtYW5jZUlzc3VlW11cbiAgdXBkYXRlZFNwZWNEZWx0YT86IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBwYXJ0aWFsIHNwZWNcbiAgdXBkYXRlZFRhc2tzPzogc3RyaW5nOyAvLyBKU09OIHN0cmluZ2lmaWVkIFRhc2tbXVxuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgcmV2aWV3ZWRCeTogc3RyaW5nOyAvLyBBZ2VudCB0aGF0IHBlcmZvcm1lZCB0aGUgcmV2aWV3XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclNlc3Npb25SZWNvcmQge1xuICBQSzogc3RyaW5nOyAvLyBVU0VSI3t1c2VySWR9XG4gIFNLOiBzdHJpbmc7IC8vIFNFU1NJT04je3Nlc3Npb25JZH1cbiAgR1NJMVBLOiBzdHJpbmc7IC8vIENSRUFURUQje2RhdGV9XG4gIEdTSTFTSzogc3RyaW5nOyAvLyBVU0VSI3t1c2VySWR9XG4gIHVzZXJJZDogc3RyaW5nO1xuICBzZXNzaW9uSWQ6IHN0cmluZztcbiAgdXNlclJlcXVlc3Q6IHN0cmluZzsgLy8gSlNPTiBzdHJpbmdpZmllZCBVc2VyUmVxdWVzdFxuICBzdGF0dXM6IHN0cmluZztcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIGxhc3RBY2Nlc3NlZEF0OiBzdHJpbmc7XG4gIHR0bD86IG51bWJlcjsgLy8gVFRMIGZvciBhdXRvbWF0aWMgY2xlYW51cFxufVxuXG4vLyBUYWJsZSBkZWZpbml0aW9ucyBmb3IgQ0RLXG5leHBvcnQgY29uc3QgV09SS0ZMT1dfVEFCTEVfQ09ORklHID0ge1xuICB0YWJsZU5hbWU6ICdBSUFnZW50V29ya2Zsb3dUYWJsZScsXG4gIHBhcnRpdGlvbktleTogeyBuYW1lOiAnUEsnLCB0eXBlOiBBdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICBzb3J0S2V5OiB7IG5hbWU6ICdTSycsIHR5cGU6IEF0dHJpYnV0ZVR5cGUuU1RSSU5HIH0sXG4gIGJpbGxpbmdNb2RlOiBCaWxsaW5nTW9kZS5QQVlfUEVSX1JFUVVFU1QsXG4gIHRpbWVUb0xpdmVBdHRyaWJ1dGU6ICd0dGwnLFxuICBnbG9iYWxTZWNvbmRhcnlJbmRleGVzOiBbXG4gICAge1xuICAgICAgaW5kZXhOYW1lOiAnR1NJMScsXG4gICAgICBwYXJ0aXRpb25LZXk6IHsgbmFtZTogJ0dTSTFQSycsIHR5cGU6IEF0dHJpYnV0ZVR5cGUuU1RSSU5HIH0sXG4gICAgICBzb3J0S2V5OiB7IG5hbWU6ICdHU0kxU0snLCB0eXBlOiBBdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgICAgcHJvamVjdGlvblR5cGU6IFByb2plY3Rpb25UeXBlLkFMTCxcbiAgICB9LFxuICAgIHtcbiAgICAgIGluZGV4TmFtZTogJ0dTSTInLFxuICAgICAgcGFydGl0aW9uS2V5OiB7IG5hbWU6ICdHU0kyUEsnLCB0eXBlOiBBdHRyaWJ1dGVUeXBlLlNUUklORyB9LFxuICAgICAgc29ydEtleTogeyBuYW1lOiAnR1NJMlNLJywgdHlwZTogQXR0cmlidXRlVHlwZS5TVFJJTkcgfSxcbiAgICAgIHByb2plY3Rpb25UeXBlOiBQcm9qZWN0aW9uVHlwZS5BTEwsXG4gICAgfSxcbiAgXSxcbiAgcG9pbnRJblRpbWVSZWNvdmVyeTogdHJ1ZSxcbiAgZW5jcnlwdGlvbjogJ0FXU19NQU5BR0VEJyxcbn07XG5cbi8vIERhdGEgYWNjZXNzIHBhdHRlcm5zXG5leHBvcnQgY29uc3QgQUNDRVNTX1BBVFRFUk5TID0ge1xuICAvLyBXb3JrZmxvdyBTdGF0ZVxuICBnZXRXb3JrZmxvd1N0YXRlOiAoc2Vzc2lvbklkOiBzdHJpbmcpID0+ICh7XG4gICAgUEs6IGBTRVNTSU9OIyR7c2Vzc2lvbklkfWAsXG4gICAgU0s6IHsgYmVnaW5zV2l0aDogJ1dPUktGTE9XIycgfSxcbiAgfSksXG4gIFxuICAvLyBUYXNrc1xuICBnZXRUYXNrc0J5U2Vzc2lvbjogKHNlc3Npb25JZDogc3RyaW5nKSA9PiAoe1xuICAgIFBLOiBgU0VTU0lPTiMke3Nlc3Npb25JZH1gLFxuICAgIFNLOiB7IGJlZ2luc1dpdGg6ICdUQVNLIycgfSxcbiAgfSksXG4gIFxuICBnZXRUYXNrc0J5U3RhdHVzOiAoc3RhdHVzOiBzdHJpbmcpID0+ICh7XG4gICAgSW5kZXhOYW1lOiAnR1NJMScsXG4gICAgR1NJMVBLOiBgU1RBVFVTIyR7c3RhdHVzfWAsXG4gIH0pLFxuICBcbiAgZ2V0VGFza3NCeUFnZW50OiAoYWdlbnQ6IHN0cmluZywgc3RhdHVzPzogc3RyaW5nKSA9PiAoe1xuICAgIEluZGV4TmFtZTogJ0dTSTInLFxuICAgIEdTSTJQSzogYEFHRU5UIyR7YWdlbnR9YCxcbiAgICBHU0kyU0s6IHN0YXR1cyA/IHsgYmVnaW5zV2l0aDogYFNUQVRVUyMke3N0YXR1c30jYCB9IDogdW5kZWZpbmVkLFxuICB9KSxcbiAgXG4gIC8vIEFydGlmYWN0c1xuICBnZXRBcnRpZmFjdHNCeVNlc3Npb246IChzZXNzaW9uSWQ6IHN0cmluZykgPT4gKHtcbiAgICBQSzogYFNFU1NJT04jJHtzZXNzaW9uSWR9YCxcbiAgICBTSzogeyBiZWdpbnNXaXRoOiAnQVJUSUZBQ1QjJyB9LFxuICB9KSxcbiAgXG4gIGdldEFydGlmYWN0c0J5VGFzazogKHRhc2tJZDogc3RyaW5nKSA9PiAoe1xuICAgIEluZGV4TmFtZTogJ0dTSTEnLFxuICAgIEdTSTFQSzogYFRBU0sjJHt0YXNrSWR9YCxcbiAgfSksXG4gIFxuICAvLyBSZXZpZXdzXG4gIGdldFJldmlld3NCeVNlc3Npb246IChzZXNzaW9uSWQ6IHN0cmluZykgPT4gKHtcbiAgICBQSzogYFNFU1NJT04jJHtzZXNzaW9uSWR9YCxcbiAgICBTSzogeyBiZWdpbnNXaXRoOiAnUkVWSUVXIycgfSxcbiAgfSksXG4gIFxuICBnZXRSZXZpZXdzQnlUYXNrOiAodGFza0lkOiBzdHJpbmcpID0+ICh7XG4gICAgSW5kZXhOYW1lOiAnR1NJMScsXG4gICAgR1NJMVBLOiBgVEFTSyMke3Rhc2tJZH1gLFxuICB9KSxcbiAgXG4gIC8vIFVzZXIgU2Vzc2lvbnNcbiAgZ2V0VXNlclNlc3Npb25zOiAodXNlcklkOiBzdHJpbmcpID0+ICh7XG4gICAgUEs6IGBVU0VSIyR7dXNlcklkfWAsXG4gICAgU0s6IHsgYmVnaW5zV2l0aDogJ1NFU1NJT04jJyB9LFxuICB9KSxcbiAgXG4gIGdldFNlc3Npb25zQnlEYXRlOiAoZGF0ZTogc3RyaW5nKSA9PiAoe1xuICAgIEluZGV4TmFtZTogJ0dTSTEnLFxuICAgIEdTSTFQSzogYENSRUFURUQjJHtkYXRlfWAsXG4gIH0pLFxufTtcblxuLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgY3JlYXRpbmcgcmVjb3Jkc1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVdvcmtmbG93U3RhdGVSZWNvcmQgPSAoXG4gIHNlc3Npb25JZDogc3RyaW5nLFxuICB3b3JrZmxvd1N0YXRlOiBhbnksXG4gIHR0bEhvdXJzOiBudW1iZXIgPSAxNjggLy8gNyBkYXlzIGRlZmF1bHRcbik6IFdvcmtmbG93U3RhdGVSZWNvcmQgPT4gKHtcbiAgUEs6IGBTRVNTSU9OIyR7c2Vzc2lvbklkfWAsXG4gIFNLOiBgV09SS0ZMT1cjJHtEYXRlLm5vdygpfWAsXG4gIEdTSTFQSzogYFNUQVRVUyMke3dvcmtmbG93U3RhdGUuc3RhdHVzfWAsXG4gIEdTSTFTSzogYENSRUFURUQjJHt3b3JrZmxvd1N0YXRlLmNyZWF0ZWRBdH1gLFxuICBzZXNzaW9uSWQsXG4gIGN1cnJlbnRJdGVyYXRpb246IHdvcmtmbG93U3RhdGUuY3VycmVudEl0ZXJhdGlvbixcbiAgbWF4SXRlcmF0aW9uczogd29ya2Zsb3dTdGF0ZS5tYXhJdGVyYXRpb25zLFxuICBzdGF0dXM6IHdvcmtmbG93U3RhdGUuc3RhdHVzLFxuICBzcGVjaWZpY2F0aW9uOiB3b3JrZmxvd1N0YXRlLnNwZWNpZmljYXRpb24gPyBKU09OLnN0cmluZ2lmeSh3b3JrZmxvd1N0YXRlLnNwZWNpZmljYXRpb24pIDogdW5kZWZpbmVkLFxuICB0YXNrUGxhbjogd29ya2Zsb3dTdGF0ZS50YXNrUGxhbiA/IEpTT04uc3RyaW5naWZ5KHdvcmtmbG93U3RhdGUudGFza1BsYW4pIDogdW5kZWZpbmVkLFxuICBmaW5hbE91dHB1dDogd29ya2Zsb3dTdGF0ZS5maW5hbE91dHB1dCA/IEpTT04uc3RyaW5naWZ5KHdvcmtmbG93U3RhdGUuZmluYWxPdXRwdXQpIDogdW5kZWZpbmVkLFxuICBjcmVhdGVkQXQ6IHdvcmtmbG93U3RhdGUuY3JlYXRlZEF0LFxuICB1cGRhdGVkQXQ6IHdvcmtmbG93U3RhdGUudXBkYXRlZEF0LFxuICB0dGw6IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApICsgKHR0bEhvdXJzICogMzYwMCksXG59KTtcblxuZXhwb3J0IGNvbnN0IGNyZWF0ZVRhc2tSZWNvcmQgPSAoXG4gIHNlc3Npb25JZDogc3RyaW5nLFxuICB0YXNrOiBhbnlcbik6IFRhc2tSZWNvcmQgPT4gKHtcbiAgUEs6IGBTRVNTSU9OIyR7c2Vzc2lvbklkfWAsXG4gIFNLOiBgVEFTSyMke3Rhc2sudGFza0lkfWAsXG4gIEdTSTFQSzogYFNUQVRVUyMke3Rhc2suc3RhdHVzfWAsXG4gIEdTSTFTSzogYFBSSU9SSVRZIyR7dGFzay5wcmlvcml0eX0jJHt0YXNrLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCl9YCxcbiAgR1NJMlBLOiBgQUdFTlQjJHt0YXNrLmFnZW50fWAsXG4gIEdTSTJTSzogYFNUQVRVUyMke3Rhc2suc3RhdHVzfSMke3Rhc2suY3JlYXRlZEF0IHx8IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKX1gLFxuICBzZXNzaW9uSWQsXG4gIHRhc2tJZDogdGFzay50YXNrSWQsXG4gIHRhc2tOYW1lOiB0YXNrLnRhc2tOYW1lLFxuICBkZXNjcmlwdGlvbjogdGFzay5kZXNjcmlwdGlvbixcbiAgYWdlbnQ6IHRhc2suYWdlbnQsXG4gIGRlcGVuZGVuY2llczogSlNPTi5zdHJpbmdpZnkodGFzay5kZXBlbmRlbmNpZXMgfHwgW10pLFxuICBzdGF0dXM6IHRhc2suc3RhdHVzLFxuICBwcmlvcml0eTogdGFzay5wcmlvcml0eSxcbiAgZXN0aW1hdGVkRHVyYXRpb246IHRhc2suZXN0aW1hdGVkRHVyYXRpb24sXG4gIG1ldGFkYXRhOiBKU09OLnN0cmluZ2lmeSh0YXNrLm1ldGFkYXRhIHx8IHt9KSxcbiAgY3JlYXRlZEF0OiB0YXNrLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICBjb21wbGV0ZWRBdDogdGFzay5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gbmV3IERhdGUoKS50b0lTT1N0cmluZygpIDogdW5kZWZpbmVkLFxufSk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVBcnRpZmFjdFJlY29yZCA9IChcbiAgc2Vzc2lvbklkOiBzdHJpbmcsXG4gIHRhc2tJZDogc3RyaW5nLFxuICBhcnRpZmFjdDogYW55LFxuICBzM0tleTogc3RyaW5nXG4pOiBBcnRpZmFjdFJlY29yZCA9PiAoe1xuICBQSzogYFNFU1NJT04jJHtzZXNzaW9uSWR9YCxcbiAgU0s6IGBBUlRJRkFDVCMke3Rhc2tJZH0jJHtEYXRlLm5vdygpfWAsXG4gIEdTSTFQSzogYFRBU0sjJHt0YXNrSWR9YCxcbiAgR1NJMVNLOiBgVkVSU0lPTiMke0RhdGUubm93KCl9YCxcbiAgc2Vzc2lvbklkLFxuICB0YXNrSWQsXG4gIHMzS2V5LFxuICBmaWxlczogSlNPTi5zdHJpbmdpZnkoYXJ0aWZhY3QuZmlsZXMgfHwgW10pLFxuICBlcnJvcnM6IEpTT04uc3RyaW5naWZ5KGFydGlmYWN0LmVycm9ycyB8fCBbXSksXG4gIHdhcm5pbmdzOiBKU09OLnN0cmluZ2lmeShhcnRpZmFjdC53YXJuaW5ncyB8fCBbXSksXG4gIG1ldGFkYXRhOiBKU09OLnN0cmluZ2lmeShhcnRpZmFjdC5tZXRhZGF0YSB8fCB7fSksXG4gIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICB2ZXJzaW9uOiBhcnRpZmFjdC5tZXRhZGF0YT8udmVyc2lvbiB8fCAnMS4wLjAnLFxufSk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVSZXZpZXdSZWNvcmQgPSAoXG4gIHNlc3Npb25JZDogc3RyaW5nLFxuICB0YXNrSWQ6IHN0cmluZyxcbiAgcmV2aWV3OiBhbnksXG4gIHJldmlld2VkQnk6IHN0cmluZ1xuKTogUmV2aWV3UmVjb3JkID0+ICh7XG4gIFBLOiBgU0VTU0lPTiMke3Nlc3Npb25JZH1gLFxuICBTSzogYFJFVklFVyMke3Rhc2tJZH0jJHtEYXRlLm5vdygpfWAsXG4gIEdTSTFQSzogYFRBU0sjJHt0YXNrSWR9YCxcbiAgR1NJMVNLOiBgUVVBTElUWSMke3Jldmlldy5vdmVyYWxsUXVhbGl0eX0jJHtEYXRlLm5vdygpfWAsXG4gIHNlc3Npb25JZCxcbiAgdGFza0lkLFxuICBvdmVyYWxsUXVhbGl0eTogcmV2aWV3Lm92ZXJhbGxRdWFsaXR5LFxuICBlcnJvcnM6IEpTT04uc3RyaW5naWZ5KHJldmlldy5lcnJvcnMgfHwgW10pLFxuICBzdWdnZXN0aW9uczogSlNPTi5zdHJpbmdpZnkocmV2aWV3LnN1Z2dlc3Rpb25zIHx8IFtdKSxcbiAgc2VjdXJpdHlJc3N1ZXM6IEpTT04uc3RyaW5naWZ5KHJldmlldy5zZWN1cml0eUlzc3VlcyB8fCBbXSksXG4gIHBlcmZvcm1hbmNlSXNzdWVzOiBKU09OLnN0cmluZ2lmeShyZXZpZXcucGVyZm9ybWFuY2VJc3N1ZXMgfHwgW10pLFxuICB1cGRhdGVkU3BlY0RlbHRhOiByZXZpZXcudXBkYXRlZFNwZWNEZWx0YSA/IEpTT04uc3RyaW5naWZ5KHJldmlldy51cGRhdGVkU3BlY0RlbHRhKSA6IHVuZGVmaW5lZCxcbiAgdXBkYXRlZFRhc2tzOiByZXZpZXcudXBkYXRlZFRhc2tzID8gSlNPTi5zdHJpbmdpZnkocmV2aWV3LnVwZGF0ZWRUYXNrcykgOiB1bmRlZmluZWQsXG4gIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICByZXZpZXdlZEJ5LFxufSk7XG5cbmV4cG9ydCBjb25zdCBjcmVhdGVVc2VyU2Vzc2lvblJlY29yZCA9IChcbiAgdXNlcklkOiBzdHJpbmcsXG4gIHNlc3Npb25JZDogc3RyaW5nLFxuICB1c2VyUmVxdWVzdDogYW55LFxuICB0dGxIb3VyczogbnVtYmVyID0gNzIwIC8vIDMwIGRheXMgZGVmYXVsdFxuKTogVXNlclNlc3Npb25SZWNvcmQgPT4gKHtcbiAgUEs6IGBVU0VSIyR7dXNlcklkfWAsXG4gIFNLOiBgU0VTU0lPTiMke3Nlc3Npb25JZH1gLFxuICBHU0kxUEs6IGBDUkVBVEVEIyR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19YCxcbiAgR1NJMVNLOiBgVVNFUiMke3VzZXJJZH1gLFxuICB1c2VySWQsXG4gIHNlc3Npb25JZCxcbiAgdXNlclJlcXVlc3Q6IEpTT04uc3RyaW5naWZ5KHVzZXJSZXF1ZXN0KSxcbiAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gIGxhc3RBY2Nlc3NlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gIHR0bDogTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCkgKyAodHRsSG91cnMgKiAzNjAwKSxcbn0pO1xuIl19