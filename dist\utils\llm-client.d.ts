/**
 * LLM Client for interacting with AI models (Gemini and DeepSeek R1)
 */
export interface LLMOptions {
    temperature?: number;
    maxTokens?: number;
    model?: 'gemini' | 'deepseek' | 'auto';
    requestId?: string;
    provider?: 'gemini' | 'deepseek' | 'openrouter';
}
export declare function invokeLLM(prompt: string, options?: LLMOptions): Promise<string>;
export declare function invokeLLMWithRetry(prompt: string, options?: LLMOptions, maxRetries?: number): Promise<string>;
export declare function invokeLLMStreaming(prompt: string, options?: LLMOptions, onChunk?: (chunk: string) => void): Promise<string>;
export declare function validateLLMResponse(response: string, expectedFormat?: 'json' | 'yaml' | 'markdown'): boolean;
export declare function extractJSONFromResponse(response: string): any;
export declare function sanitizePrompt(prompt: string): string;
export declare function buildPromptWithContext(basePrompt: string, context: Record<string, any>, maxContextLength?: number): string;
export declare function estimateTokenCount(text: string): number;
export declare function splitLargePrompt(prompt: string, maxTokens?: number): string[];
