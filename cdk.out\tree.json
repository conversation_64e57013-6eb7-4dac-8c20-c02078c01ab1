{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.202.0"}, "children": {"AIAgentWorkflowStack": {"id": "AIAgentWorkflowStack", "path": "AIAgentWorkflowStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.202.0"}, "children": {"EncryptionKey": {"id": "EncryptionKey", "path": "AIAgentWorkflowStack/EncryptionKey", "constructInfo": {"fqn": "aws-cdk-lib.aws_kms.Key", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/EncryptionKey/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_kms.CfnKey", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::KMS::Key", "aws:cdk:cloudformation:props": {"description": "KMS key for AI Agent Workflow System", "enableKeyRotation": true, "keyPolicy": {"Statement": [{"Action": "kms:*", "Effect": "Allow", "Principal": {"AWS": "arn:aws:iam::337909760884:root"}, "Resource": "*"}], "Version": "2012-10-17"}, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "WorkflowTable": {"id": "WorkflowTable", "path": "AIAgentWorkflowStack/WorkflowTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.Table", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowTable/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.CfnTable", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::DynamoDB::Table", "aws:cdk:cloudformation:props": {"attributeDefinitions": [{"attributeName": "PK", "attributeType": "S"}, {"attributeName": "SK", "attributeType": "S"}, {"attributeName": "GSI1PK", "attributeType": "S"}, {"attributeName": "GSI1SK", "attributeType": "S"}, {"attributeName": "GSI2PK", "attributeType": "S"}, {"attributeName": "GSI2SK", "attributeType": "S"}], "billingMode": "PAY_PER_REQUEST", "globalSecondaryIndexes": [{"indexName": "GSI1", "keySchema": [{"attributeName": "GSI1PK", "keyType": "HASH"}, {"attributeName": "GSI1SK", "keyType": "RANGE"}], "projection": {"projectionType": "ALL"}}, {"indexName": "GSI2", "keySchema": [{"attributeName": "GSI2PK", "keyType": "HASH"}, {"attributeName": "GSI2SK", "keyType": "RANGE"}], "projection": {"projectionType": "ALL"}}], "keySchema": [{"attributeName": "PK", "keyType": "HASH"}, {"attributeName": "SK", "keyType": "RANGE"}], "pointInTimeRecoverySpecification": {"pointInTimeRecoveryEnabled": true}, "sseSpecification": {"sseEnabled": true, "kmsMasterKeyId": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}, "sseType": "KMS"}, "streamSpecification": {"streamViewType": "NEW_AND_OLD_IMAGES"}, "tableName": "AIAgentWorkflowTable", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "timeToLiveSpecification": {"attributeName": "ttl", "enabled": true}}}}, "ScalingRole": {"id": "ScalingRole", "path": "AIAgentWorkflowStack/WorkflowTable/ScalingRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}}}, "ArtifactsBucket": {"id": "ArtifactsBucket", "path": "AIAgentWorkflowStack/ArtifactsBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ArtifactsBucket/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"bucketEncryption": {"serverSideEncryptionConfiguration": [{"serverSideEncryptionByDefault": {"sseAlgorithm": "aws:kms", "kmsMasterKeyId": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}}]}, "bucketName": "ai-agent-artifacts-337909760884-us-east-1", "corsConfiguration": {"corsRules": [{"maxAge": 3000, "allowedHeaders": ["*"], "allowedMethods": ["GET", "PUT", "POST"], "allowedOrigins": ["*"]}]}, "lifecycleConfiguration": {"rules": [{"id": "DeleteOldVersions", "noncurrentVersionExpiration": {"noncurrentDays": 30}, "status": "Enabled"}, {"id": "TransitionToIA", "status": "Enabled", "transitions": [{"storageClass": "STANDARD_IA", "transitionInDays": 30}, {"storageClass": "GLACIER", "transitionInDays": 90}]}]}, "publicAccessBlockConfiguration": {"blockPublicAcls": true, "blockPublicPolicy": true, "ignorePublicAcls": true, "restrictPublicBuckets": true}, "tags": [{"key": "aws-cdk:auto-delete-objects", "value": "true"}, {"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "versioningConfiguration": {"status": "Enabled"}}}}, "Policy": {"id": "Policy", "path": "AIAgentWorkflowStack/ArtifactsBucket/Policy", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ArtifactsBucket/Policy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "ArtifactsBucket2AAC5544"}, "policyDocument": {"Statement": [{"Action": ["s3:DeleteObject*", "s3:GetBucket*", "s3:List*", "s3:PutBucketPolicy"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}}}}}, "AutoDeleteObjectsCustomResource": {"id": "AutoDeleteObjectsCustomResource", "path": "AIAgentWorkflowStack/ArtifactsBucket/AutoDeleteObjectsCustomResource", "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.202.0", "metadata": []}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AIAgentWorkflowStack/ArtifactsBucket/AutoDeleteObjectsCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}}}, "Custom::S3AutoDeleteObjectsCustomResourceProvider": {"id": "Custom::S3AutoDeleteObjectsCustomResourceProvider", "path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider", "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProviderBase", "version": "2.202.0"}, "children": {"Staging": {"id": "Staging", "path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.202.0"}}, "Role": {"id": "Role", "path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}, "Handler": {"id": "Handler", "path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "DescriptionEnhancerFunction": {"id": "DescriptionEnhancerFunction", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.202.0", "metadata": []}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}], "Version": "2012-10-17"}, "policyName": "DescriptionEnhancerFunctionServiceRoleDefaultPolicyACC2A16A", "roles": [{"Ref": "DescriptionEnhancerFunctionServiceRole186883E6"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"zipFile": "\n        exports.handler = async (event) => {\n          console.log('Description Enhancer called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              sessionId: 'test-session-' + Date.now(),\n              specification: {\n                projectName: 'Generated Project',\n                description: 'A test project generated by AI Agent Workflow',\n                features: ['Authentication', 'API', 'Database'],\n                techStack: {\n                  frontend: { framework: 'React', language: 'TypeScript' },\n                  backend: { framework: 'Express', language: 'Node.js' }\n                }\n              },\n              taskPlan: {\n                tasks: [{\n                  taskId: 'task-1',\n                  taskName: 'Setup Project Structure',\n                  description: 'Create basic project structure',\n                  status: 'pending'\n                }]\n              },\n              status: 'enhancement_completed'\n            })\n          };\n        };\n      "}, "description": "Enhances user descriptions and creates detailed specifications", "environment": {"variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "handler": "index.handler", "memorySize": 1024, "role": {"Fn::GetAtt": ["DescriptionEnhancerFunctionServiceRole186883E6", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "timeout": 900, "tracingConfig": {"mode": "Active"}}}}, "LogRetention": {"id": "LogRetention", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/LogRetention", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogRetention", "version": "2.202.0"}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/LogRetention/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "LogGroup": {"id": "LogGroup", "path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}}}, "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a": {"id": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Code": {"id": "Code", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.202.0"}, "children": {"Stage": {"id": "Stage", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.202.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.202.0", "metadata": []}}}}, "ServiceRole": {"id": "ServiceRole", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["logs:DeleteRetentionPolicy", "logs:PutRetentionPolicy"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "policyName": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB", "roles": [{"Ref": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "CodeCreatorFunction": {"id": "CodeCreatorFunction", "path": "AIAgentWorkflowStack/CodeCreatorFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.202.0", "metadata": []}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "policyName": "CodeCreatorFunctionServiceRoleDefaultPolicyCEF966DD", "roles": [{"Ref": "CodeCreatorFunctionServiceRole9BB85716"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/CodeCreatorFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"zipFile": "\n        exports.handler = async (event) => {\n          console.log('Code Creator called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              artifacts: [{\n                path: 'src/index.js',\n                content: 'console.log(\"Hello World!\");',\n                type: 'source',\n                language: 'javascript'\n              }],\n              status: 'code_generation_completed'\n            })\n          };\n        };\n      "}, "description": "Generates code files based on specifications and tasks", "environment": {"variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "handler": "index.handler", "memorySize": 2048, "role": {"Fn::GetAtt": ["CodeCreatorFunctionServiceRole9BB85716", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "timeout": 900, "tracingConfig": {"mode": "Active"}}}}, "LogRetention": {"id": "LogRetention", "path": "AIAgentWorkflowStack/CodeCreatorFunction/LogRetention", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogRetention", "version": "2.202.0"}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/CodeCreatorFunction/LogRetention/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "LogGroup": {"id": "LogGroup", "path": "AIAgentWorkflowStack/CodeCreatorFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}}}, "ReviewRefineFunction": {"id": "ReviewRefineFunction", "path": "AIAgentWorkflowStack/ReviewRefineFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.202.0", "metadata": []}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "policyName": "ReviewRefineFunctionServiceRoleDefaultPolicy0BD1FFFC", "roles": [{"Ref": "ReviewRefineFunctionServiceRole3AA53DF4"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ReviewRefineFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"zipFile": "\n        exports.handler = async (event) => {\n          console.log('Review & Refine called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              reviewResults: {\n                errors: [],\n                warnings: [],\n                suggestions: ['Code looks good!'],\n                qualityScore: 85\n              },\n              status: 'review_completed'\n            })\n          };\n        };\n      "}, "description": "Reviews and refines generated code", "environment": {"variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "handler": "index.handler", "memorySize": 1536, "role": {"Fn::GetAtt": ["ReviewRefineFunctionServiceRole3AA53DF4", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "timeout": 600, "tracingConfig": {"mode": "Active"}}}}, "LogRetention": {"id": "LogRetention", "path": "AIAgentWorkflowStack/ReviewRefineFunction/LogRetention", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogRetention", "version": "2.202.0"}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ReviewRefineFunction/LogRetention/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "LogGroup": {"id": "LogGroup", "path": "AIAgentWorkflowStack/ReviewRefineFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}}}, "FinalizerFunction": {"id": "FinalizerFunction", "path": "AIAgentWorkflowStack/FinalizerFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.202.0", "metadata": []}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "policyName": "FinalizerFunctionServiceRoleDefaultPolicyA64092A9", "roles": [{"Ref": "FinalizerFunctionServiceRole7963A895"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/FinalizerFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"zipFile": "\n        exports.handler = async (event) => {\n          console.log('Finalizer called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              finalOutput: {\n                downloadUrl: 'https://example.com/download/project.zip',\n                documentation: 'Project documentation generated successfully',\n                deploymentInstructions: 'Run npm install && npm start'\n              },\n              status: 'finalization_completed'\n            })\n          };\n        };\n      "}, "description": "Generates final artifacts and documentation", "environment": {"variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "handler": "index.handler", "memorySize": 1536, "role": {"Fn::GetAtt": ["FinalizerFunctionServiceRole7963A895", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "timeout": 600, "tracingConfig": {"mode": "Active"}}}}, "LogRetention": {"id": "LogRetention", "path": "AIAgentWorkflowStack/FinalizerFunction/LogRetention", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogRetention", "version": "2.202.0"}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/FinalizerFunction/LogRetention/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "LogGroup": {"id": "LogGroup", "path": "AIAgentWorkflowStack/FinalizerFunction/LogGroup", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}}}, "EnhanceDescriptionTask": {"id": "EnhanceDescriptionTask", "path": "AIAgentWorkflowStack/EnhanceDescriptionTask", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "CreateCodeTask": {"id": "CreateCodeTask", "path": "AIAgentWorkflowStack/CreateCodeTask", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "ReviewRefineTask": {"id": "ReviewRefineTask", "path": "AIAgentWorkflowStack/ReviewRefineTask", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "FinalizeTask": {"id": "FinalizeTask", "path": "AIAgentWorkflowStack/FinalizeTask", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "InitializeWorkflow": {"id": "InitializeWorkflow", "path": "AIAgentWorkflowStack/InitializeWorkflow", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "PrepareTaskExecution": {"id": "PrepareTaskExecution", "path": "AIAgentWorkflowStack/PrepareTaskExecution", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "ExecuteTasksMap": {"id": "ExecuteTasksMap", "path": "AIAgentWorkflowStack/ExecuteTasksMap", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Map", "version": "2.202.0"}}, "CollectArtifacts": {"id": "CollectArtifacts", "path": "AIAgentWorkflowStack/CollectArtifacts", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "WorkflowCompleted": {"id": "WorkflowCompleted", "path": "AIAgentWorkflowStack/WorkflowCompleted", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "FinalizeTaskMaxIterations": {"id": "FinalizeTaskMaxIterations", "path": "AIAgentWorkflowStack/FinalizeTaskMaxIterations", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "FinalizeTaskNormal": {"id": "FinalizeTaskNormal", "path": "AIAgentWorkflowStack/FinalizeTaskNormal", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions_tasks.LambdaInvoke", "version": "2.202.0"}}, "EvaluateReviewResults": {"id": "EvaluateReviewResults", "path": "AIAgentWorkflowStack/EvaluateReviewResults", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Choice", "version": "2.202.0"}}, "PrepareNextIteration": {"id": "PrepareNextIteration", "path": "AIAgentWorkflowStack/PrepareNextIteration", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "MaxIterationsReached": {"id": "MaxIterationsReached", "path": "AIAgentWorkflowStack/MaxIterationsReached", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.Pass", "version": "2.202.0"}}, "StateMachineLogGroup": {"id": "StateMachineLogGroup", "path": "AIAgentWorkflowStack/StateMachineLogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/StateMachineLogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"retentionInDays": 7, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "WorkflowStateMachine": {"id": "WorkflowStateMachine", "path": "AIAgentWorkflowStack/WorkflowStateMachine", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.StateMachine", "version": "2.202.0", "metadata": []}, "children": {"Role": {"id": "Role", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Role", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportRole": {"id": "ImportRole", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/ImportRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, ":*"]]}]}, {"Action": ["logs:CreateLogDelivery", "logs:DeleteLogDelivery", "logs:DescribeLogGroups", "logs:DescribeResourcePolicies", "logs:GetLogDelivery", "logs:ListLogDeliveries", "logs:PutResourcePolicy", "logs:UpdateLogDelivery", "xray:GetSamplingRules", "xray:GetSamplingTargets", "xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "policyName": "WorkflowStateMachineRoleDefaultPolicyDAB74B4D", "roles": [{"Ref": "WorkflowStateMachineRoleE0545793"}]}}}}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowStateMachine/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_stepfunctions.CfnStateMachine", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::StepFunctions::StateMachine", "aws:cdk:cloudformation:props": {"definitionString": {"Fn::Join": ["", ["{\"StartAt\":\"InitializeWorkflow\",\"States\":{\"InitializeWorkflow\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"userRequest.$\":\"$.userRequest\",\"currentIteration\":0,\"maxIterations\":5,\"status\":\"initializing\"},\"Next\":\"EnhanceDescriptionTask\"},\"EnhanceDescriptionTask\":{\"Next\":\"PrepareTaskExecution\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"PrepareTaskExecution\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"currentIteration.$\":\"$.currentIteration\",\"maxIterations.$\":\"$.maxIterations\",\"artifacts\":[],\"reviews\":[]},\"Next\":\"ExecuteTasksMap\"},\"PrepareNextIteration\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"currentIteration.$\":\"States.MathAdd($.currentIteration, 1)\",\"maxIterations.$\":\"$.maxIterations\",\"artifacts.$\":\"$.artifacts\",\"reviews.$\":\"$.reviewResults\"},\"Next\":\"PrepareTaskExecution\"},\"EvaluateReviewResults\":{\"Type\":\"Choice\",\"Choices\":[{\"And\":[{\"Variable\":\"$.reviewResults[0].errors\",\"IsPresent\":true},{\"Variable\":\"$.currentIteration\",\"NumericLessThan\":5}],\"Next\":\"PrepareNextIteration\"},{\"Variable\":\"$.currentIteration\",\"NumericGreaterThanEquals\":5,\"Next\":\"MaxIterationsReached\"}],\"Default\":\"FinalizeTaskNormal\"},\"ReviewRefineTask\":{\"Next\":\"EvaluateReviewResults\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"CollectArtifacts\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"artifacts.$\":\"$[*].Payload\",\"currentIteration.$\":\"$.currentIteration\",\"maxIterations.$\":\"$.maxIterations\"},\"Next\":\"ReviewRefineTask\"},\"ExecuteTasksMap\":{\"Type\":\"Map\",\"Next\":\"CollectArtifacts\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"task.$\":\"$$.Map.Item.Value\",\"iteration.$\":\"$.currentIteration\"},\"ItemsPath\":\"$.taskPlan.tasks\",\"MaxConcurrency\":3,\"Iterator\":{\"StartAt\":\"CreateCodeTask\",\"States\":{\"CreateCodeTask\":{\"End\":true,\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}}}}},\"FinalizeTaskNormal\":{\"Next\":\"WorkflowCompleted\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"WorkflowCompleted\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"status\":\"completed\",\"finalOutput.$\":\"$.finalOutput\",\"message\":\"Workflow completed successfully\",\"completedAt.$\":\"$$.State.EnteredTime\"},\"End\":true},\"FinalizeTaskMaxIterations\":{\"Next\":\"WorkflowCompleted\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"MaxIterationsReached\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"status\":\"max_iterations_reached\",\"message\":\"Maximum iterations reached. Proceeding with current artifacts.\",\"artifacts.$\":\"$.artifacts\",\"reviews.$\":\"$.reviewResults\"},\"Next\":\"FinalizeTaskMaxIterations\"}},\"TimeoutSeconds\":7200}"]]}, "loggingConfiguration": {"destinations": [{"cloudWatchLogsLogGroup": {"logGroupArn": {"Fn::GetAtt": ["StateMachineLogGroup15B91BCB", "<PERSON><PERSON>"]}}}], "level": "ALL"}, "roleArn": {"Fn::GetAtt": ["WorkflowStateMachineRoleE0545793", "<PERSON><PERSON>"]}, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "tracingConfiguration": {"enabled": true}}}}}}, "WorkflowApi": {"id": "WorkflowApi", "path": "AIAgentWorkflowStack/WorkflowApi", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.RestApi", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnRestApi", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::RestApi", "aws:cdk:cloudformation:props": {"description": "API for AI Agent Workflow System", "name": "AI Agent Workflow API", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}, "CloudWatchRole": {"id": "CloudWatchRole", "path": "AIAgentWorkflowStack/WorkflowApi/CloudWatchRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportCloudWatchRole": {"id": "ImportCloudWatchRole", "path": "AIAgentWorkflowStack/WorkflowApi/CloudWatchRole/ImportCloudWatchRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/CloudWatchRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "apigateway.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs"]]}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "Account": {"id": "Account", "path": "AIAgentWorkflowStack/WorkflowApi/Account", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnAccount", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Account", "aws:cdk:cloudformation:props": {"cloudWatchRoleArn": {"Fn::GetAtt": ["WorkflowApiCloudWatchRole89411D9E", "<PERSON><PERSON>"]}}}}, "Deployment": {"id": "Deployment", "path": "AIAgentWorkflowStack/WorkflowApi/Deployment", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Deployment", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Deployment/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnDeployment", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Deployment", "aws:cdk:cloudformation:props": {"description": "API for AI Agent Workflow System", "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}, "DeploymentStage.prod": {"id": "DeploymentStage.prod", "path": "AIAgentWorkflowStack/WorkflowApi/DeploymentStage.prod", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Stage", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/DeploymentStage.prod/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnStage", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Stage", "aws:cdk:cloudformation:props": {"deploymentId": {"Ref": "WorkflowApiDeployment5BD8B8386f483f33cec646eff48257f9e9ca6c37"}, "methodSettings": [{"httpMethod": "*", "resourcePath": "/*", "dataTraceEnabled": true, "loggingLevel": "INFO", "metricsEnabled": true}], "restApiId": {"Ref": "WorkflowApiB9CC683F"}, "stageName": "prod", "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "tracingEnabled": true}}}}}, "Endpoint": {"id": "Endpoint", "path": "AIAgentWorkflowStack/WorkflowApi/Endpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.202.0"}}, "Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AIAgentWorkflowStack/WorkflowApi/Default", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.ResourceBase", "version": "2.202.0", "metadata": []}, "children": {"OPTIONS": {"id": "OPTIONS", "path": "AIAgentWorkflowStack/WorkflowApi/Default/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}, "enhance": {"id": "enhance", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "pathPart": "enhance", "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "WorkflowApienhanceCF77A6D6"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}, "POST": {"id": "POST", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"ApiPermission.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance": {"id": "ApiPermission.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-east-1:337909760884:", {"Ref": "WorkflowApiB9CC683F"}, "/", {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "/POST/enhance"]]}}}}, "ApiPermission.Test.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance": {"id": "ApiPermission.Test.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.Test.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-east-1:337909760884:", {"Ref": "WorkflowApiB9CC683F"}, "/test-invoke-stage/POST/enhance"]]}}}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": true, "authorizationType": "NONE", "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST", "requestTemplates": {"application/json": "{\"userRequest\":\"$input.json(\\\"$\\\")\",\"sessionId\":\"$context.requestId\",\"userId\":\"$context.identity.apiKey\"}"}, "integrationResponses": [{"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"statusCode": "400", "selectionPattern": ".*\"statusCode\":400.*", "responseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}, {"statusCode": "500", "selectionPattern": ".*\"statusCode\":500.*", "responseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}]}, "methodResponses": [{"statusCode": "200"}, {"statusCode": "400"}, {"statusCode": "500"}], "resourceId": {"Ref": "WorkflowApienhanceCF77A6D6"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}}}, "workflow": {"id": "workflow", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "pathPart": "workflow", "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "WorkflowApiworkflow843BCBF5"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}, "POST": {"id": "POST", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": true, "authorizationType": "NONE", "httpMethod": "POST", "integration": {"type": "AWS", "uri": "arn:aws:apigateway:us-east-1:states:action/StartExecution", "integrationHttpMethod": "POST", "requestTemplates": {"application/json": {"Fn::Join": ["", ["{\"stateMachineArn\":\"", {"Ref": "WorkflowStateMachine67D94DDA"}, "\",\"input\":\"{\\\"sessionId\\\":\\\"$context.requestId\\\",\\\"userRequest\\\":\\\"$input.json(\\\\\\\"$\\\\\\\")\\\"}\"}"]]}}, "integrationResponses": [{"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "responseTemplates": {"application/json": "{\"executionArn\":\"$input.json(\\\"$.executionArn\\\")\",\"startDate\":\"$input.json(\\\"$.startDate\\\")\",\"message\":\"Workflow started successfully\"}"}}], "credentials": {"Fn::GetAtt": ["StepFunctionsApiRole3B314676", "<PERSON><PERSON>"]}}, "methodResponses": [{"statusCode": "200"}, {"statusCode": "400"}, {"statusCode": "500"}], "resourceId": {"Ref": "WorkflowApiworkflow843BCBF5"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}}}, "status": {"id": "status", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "pathPart": "status", "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "WorkflowApistatus89EB6A98"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}, "GET": {"id": "GET", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status/GET", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApi/Default/status/GET/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "NONE", "httpMethod": "GET", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "integrationResponses": [{"statusCode": "200", "responseTemplates": {"application/json": "{\"status\":\"healthy\",\"timestamp\":\"$context.requestTime\",\"version\":\"1.0.0\"}"}}]}, "methodResponses": [{"statusCode": "200"}], "resourceId": {"Ref": "WorkflowApistatus89EB6A98"}, "restApiId": {"Ref": "WorkflowApiB9CC683F"}}}}}}}}}}}}, "WorkflowApiKey": {"id": "WorkflowApiKey", "path": "AIAgentWorkflowStack/WorkflowApiKey", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.ApiKey", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowApiKey/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnApiKey", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::ApiKey", "aws:cdk:cloudformation:props": {"description": "API Key for AI Agent Workflow System", "enabled": true, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "WorkflowUsagePlan": {"id": "WorkflowUsagePlan", "path": "AIAgentWorkflowStack/WorkflowUsagePlan", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.UsagePlan", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/WorkflowUsagePlan/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnUsagePlan", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::UsagePlan", "aws:cdk:cloudformation:props": {"apiStages": [{"apiId": {"Ref": "WorkflowApiB9CC683F"}, "stage": {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "throttle": {}}], "description": "Usage plan for AI Agent Workflow API", "quota": {"limit": 10000, "period": "MONTH"}, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "throttle": {"burstLimit": 200, "rateLimit": 100}, "usagePlanName": "AI Agent Workflow Usage Plan"}}}, "UsagePlanKeyResource:AIAgentWorkflowStackWorkflowApiKey9C182D45": {"id": "UsagePlanKeyResource:AIAgentWorkflowStackWorkflowApiKey9C182D45", "path": "AIAgentWorkflowStack/WorkflowUsagePlan/UsagePlanKeyResource:AIAgentWorkflowStackWorkflowApiKey9C182D45", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnUsagePlanKey", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::UsagePlanKey", "aws:cdk:cloudformation:props": {"keyId": {"Ref": "WorkflowApiKeyD3ED92A1"}, "keyType": "API_KEY", "usagePlanId": {"Ref": "WorkflowUsagePlanCB7C6F4A"}}}}}}, "StepFunctionsApiRole": {"id": "StepFunctionsApiRole", "path": "AIAgentWorkflowStack/StepFunctionsApiRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.202.0", "metadata": []}, "children": {"ImportStepFunctionsApiRole": {"id": "ImportStepFunctionsApiRole", "path": "AIAgentWorkflowStack/StepFunctionsApiRole/ImportStepFunctionsApiRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.202.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/StepFunctionsApiRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "apigateway.amazonaws.com"}}], "Version": "2012-10-17"}, "policies": [{"policyName": "StepFunctionsExecutionPolicy", "policyDocument": {"Statement": [{"Action": "states:StartExecution", "Effect": "Allow", "Resource": {"Ref": "WorkflowStateMachine67D94DDA"}}], "Version": "2012-10-17"}}], "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "ErrorNotificationTopic": {"id": "ErrorNotificationTopic", "path": "AIAgentWorkflowStack/ErrorNotificationTopic", "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.Topic", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/ErrorNotificationTopic/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_sns.CfnTopic", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::SNS::Topic", "aws:cdk:cloudformation:props": {"displayName": "AI Agent Workflow Errors", "kmsMasterKeyId": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}, "tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}]}}}}}, "descriptionEnhancerErrorAlarm": {"id": "descriptionEnhancerErrorAlarm", "path": "AIAgentWorkflowStack/descriptionEnhancerErrorAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/descriptionEnhancerErrorAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "evaluationPeriods": 2, "metricName": "Errors", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 5, "treatMissingData": "notBreaching"}}}}}, "descriptionEnhancerDurationAlarm": {"id": "descriptionEnhancerDurationAlarm", "path": "AIAgentWorkflowStack/descriptionEnhancerDurationAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/descriptionEnhancerDurationAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "evaluationPeriods": 2, "metricName": "Duration", "namespace": "AWS/Lambda", "period": 300, "statistic": "Average", "threshold": 600000, "treatMissingData": "notBreaching"}}}}}, "descriptionEnhancerThrottleAlarm": {"id": "descriptionEnhancerThrottleAlarm", "path": "AIAgentWorkflowStack/descriptionEnhancerThrottleAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/descriptionEnhancerThrottleAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "evaluationPeriods": 1, "metricName": "<PERSON>hrottles", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "codeCreatorErrorAlarm": {"id": "codeCreatorErrorAlarm", "path": "AIAgentWorkflowStack/codeCreatorErrorAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/codeCreatorErrorAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "CodeCreatorFunction11132470"}}], "evaluationPeriods": 2, "metricName": "Errors", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 5, "treatMissingData": "notBreaching"}}}}}, "codeCreatorDurationAlarm": {"id": "codeCreatorDurationAlarm", "path": "AIAgentWorkflowStack/codeCreatorDurationAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/codeCreatorDurationAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "CodeCreatorFunction11132470"}}], "evaluationPeriods": 2, "metricName": "Duration", "namespace": "AWS/Lambda", "period": 300, "statistic": "Average", "threshold": 600000, "treatMissingData": "notBreaching"}}}}}, "codeCreatorThrottleAlarm": {"id": "codeCreatorThrottleAlarm", "path": "AIAgentWorkflowStack/codeCreatorThrottleAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/codeCreatorThrottleAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "CodeCreatorFunction11132470"}}], "evaluationPeriods": 1, "metricName": "<PERSON>hrottles", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "reviewRefineErrorAlarm": {"id": "reviewRefineErrorAlarm", "path": "AIAgentWorkflowStack/reviewRefineErrorAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/reviewRefineErrorAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "ReviewRefineFunction5325599E"}}], "evaluationPeriods": 2, "metricName": "Errors", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 5, "treatMissingData": "notBreaching"}}}}}, "reviewRefineDurationAlarm": {"id": "reviewRefineDurationAlarm", "path": "AIAgentWorkflowStack/reviewRefineDurationAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/reviewRefineDurationAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "ReviewRefineFunction5325599E"}}], "evaluationPeriods": 2, "metricName": "Duration", "namespace": "AWS/Lambda", "period": 300, "statistic": "Average", "threshold": 600000, "treatMissingData": "notBreaching"}}}}}, "reviewRefineThrottleAlarm": {"id": "reviewRefineThrottleAlarm", "path": "AIAgentWorkflowStack/reviewRefineThrottleAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/reviewRefineThrottleAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "ReviewRefineFunction5325599E"}}], "evaluationPeriods": 1, "metricName": "<PERSON>hrottles", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "finalizerErrorAlarm": {"id": "finalizer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "AIAgentWorkflowStack/finalizerErrorAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/finalizerErrorAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "evaluationPeriods": 2, "metricName": "Errors", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 5, "treatMissingData": "notBreaching"}}}}}, "finalizerDurationAlarm": {"id": "finalizerDurationAlarm", "path": "AIAgentWorkflowStack/finalizerDurationAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/finalizerDurationAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "evaluationPeriods": 2, "metricName": "Duration", "namespace": "AWS/Lambda", "period": 300, "statistic": "Average", "threshold": 600000, "treatMissingData": "notBreaching"}}}}}, "finalizerThrottleAlarm": {"id": "finalizer<PERSON><PERSON><PERSON><PERSON>Alar<PERSON>", "path": "AIAgentWorkflowStack/finalizerThrottleAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/finalizerThrottleAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "FunctionName", "value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "evaluationPeriods": 1, "metricName": "<PERSON>hrottles", "namespace": "AWS/Lambda", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "StateMachineFailedAlarm": {"id": "StateMachineFailedAlarm", "path": "AIAgentWorkflowStack/StateMachineFailedAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/StateMachineFailedAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "StateMachineArn", "value": {"Ref": "WorkflowStateMachine67D94DDA"}}], "evaluationPeriods": 1, "metricName": "ExecutionsFailed", "namespace": "AWS/States", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "DynamoDBThrottleAlarm": {"id": "DynamoDBThrottleAlarm", "path": "AIAgentWorkflowStack/DynamoDBThrottleAlarm", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.Alarm", "version": "2.202.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "AIAgentWorkflowStack/DynamoDBThrottleAlarm/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudwatch.CfnAlarm", "version": "2.202.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudWatch::Alarm", "aws:cdk:cloudformation:props": {"tags": [{"key": "ManagedBy", "value": "CDK"}, {"key": "Project", "value": "AIAgentWorkflow"}, {"key": "Repository", "value": "ai-agent-workflow-serverless"}], "comparisonOperator": "GreaterThanOrEqualToThreshold", "dimensions": [{"name": "TableName", "value": {"Ref": "WorkflowTable0AE28607"}}], "evaluationPeriods": 2, "metricName": "ThrottledRequests", "namespace": "AWS/DynamoDB", "period": 300, "statistic": "Sum", "threshold": 1, "treatMissingData": "notBreaching"}}}}}, "ApiEndpoint": {"id": "ApiEndpoint", "path": "AIAgentWorkflowStack/ApiEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.202.0"}}, "StateMachineArn": {"id": "StateMachineArn", "path": "AIAgentWorkflowStack/StateMachineArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.202.0"}}, "WorkflowTableName": {"id": "WorkflowTableName", "path": "AIAgentWorkflowStack/WorkflowTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.202.0"}}, "ArtifactsBucketName": {"id": "ArtifactsBucketName", "path": "AIAgentWorkflowStack/ArtifactsBucketName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.202.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "AIAgentWorkflowStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "AIAgentWorkflowStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.202.0"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "AIAgentWorkflowStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.202.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "AIAgentWorkflowStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.202.0"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}