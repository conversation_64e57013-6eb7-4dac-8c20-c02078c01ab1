/**
 * Tests for validation utilities
 */

import { validateUserRequest, validateGeneratedFile, validateProjectStructure } from '../../src/utils/validation';
import { UserRequest, GeneratedFile } from '../../src/types';

describe('Validation Utils', () => {
  describe('validateUserRequest', () => {
    it('should validate a valid user request', () => {
      const userRequest: UserRequest = {
        userDescription: 'Create a modern e-commerce website with user authentication',
        preferences: {
          techStack: ['react', 'next.js'],
          deploymentTarget: 'aws',
          includeTests: true,
          includeCICD: true,
        },
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject request with missing description', () => {
      const userRequest: UserRequest = {
        userDescription: '',
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('User description is required');
    });

    it('should reject request with too short description', () => {
      const userRequest: UserRequest = {
        userDescription: 'short',
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('User description must be at least 10 characters long');
    });

    it('should reject request with too long description', () => {
      const userRequest: UserRequest = {
        userDescription: 'a'.repeat(5001),
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('User description must be less than 5000 characters');
    });

    it('should warn about unknown tech stack', () => {
      const userRequest: UserRequest = {
        userDescription: 'Create a website with unknown framework',
        preferences: {
          techStack: ['unknown-framework'],
        },
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Unknown tech stack preferences: unknown-framework');
    });

    it('should warn about potentially inappropriate content', () => {
      const userRequest: UserRequest = {
        userDescription: 'Create a website for hacking purposes',
      };

      const result = validateUserRequest(userRequest);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Description contains potentially inappropriate content');
    });
  });

  describe('validateGeneratedFile', () => {
    it('should validate a valid generated file', () => {
      const file: GeneratedFile = {
        path: 'src/components/Button.tsx',
        content: 'export const Button = () => <button>Click me</button>;',
        type: 'source',
        language: 'typescript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject file with missing required fields', () => {
      const file: GeneratedFile = {
        path: '',
        content: '',
        type: 'source',
        language: 'typescript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File path is required');
      expect(result.errors).toContain('File content is required');
    });

    it('should reject file with invalid path', () => {
      const file: GeneratedFile = {
        path: '../../../etc/passwd',
        content: 'malicious content',
        type: 'source',
        language: 'typescript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File path cannot contain parent directory references (..)');
    });

    it('should reject file with absolute path', () => {
      const file: GeneratedFile = {
        path: '/absolute/path/file.ts',
        content: 'content',
        type: 'source',
        language: 'typescript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File path cannot be absolute');
    });

    it('should warn about extension mismatch', () => {
      const file: GeneratedFile = {
        path: 'file.js',
        content: 'const x: string = "hello";',
        type: 'source',
        language: 'typescript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain("File extension 'js' doesn't match language 'typescript'");
    });

    it('should warn about potentially unsafe code', () => {
      const file: GeneratedFile = {
        path: 'unsafe.js',
        content: 'eval(userInput);',
        type: 'source',
        language: 'javascript',
      };

      const result = validateGeneratedFile(file);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('File content contains potentially unsafe code patterns');
    });
  });

  describe('validateProjectStructure', () => {
    it('should validate a well-structured project', () => {
      const files: GeneratedFile[] = [
        {
          path: 'package.json',
          content: '{"name": "test-project"}',
          type: 'config',
          language: 'json',
        },
        {
          path: 'src/index.ts',
          content: 'console.log("Hello World");',
          type: 'source',
          language: 'typescript',
        },
        {
          path: 'src/components/Button.tsx',
          content: 'export const Button = () => <button>Click</button>;',
          type: 'source',
          language: 'typescript',
        },
      ];

      const result = validateProjectStructure(files);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate file paths', () => {
      const files: GeneratedFile[] = [
        {
          path: 'src/index.ts',
          content: 'content 1',
          type: 'source',
          language: 'typescript',
        },
        {
          path: 'src/index.ts',
          content: 'content 2',
          type: 'source',
          language: 'typescript',
        },
      ];

      const result = validateProjectStructure(files);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate file paths detected: src/index.ts');
    });

    it('should warn about React project without package.json', () => {
      const files: GeneratedFile[] = [
        {
          path: 'src/App.tsx',
          content: 'import React from "react"; export const App = () => <div>Hello</div>;',
          type: 'source',
          language: 'typescript',
        },
      ];

      const result = validateProjectStructure(files);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('React project detected but no package.json found');
    });

    it('should suggest organizing files in src directory', () => {
      const files: GeneratedFile[] = Array.from({ length: 6 }, (_, i) => ({
        path: `file${i}.ts`,
        content: 'content',
        type: 'source' as const,
        language: 'typescript',
      }));

      const result = validateProjectStructure(files);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Consider organizing files in a src/ directory');
    });
  });
});
