/**
 * CloudWatch Dashboard for AI Agent Workflow System
 */
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';
export interface DashboardProps {
    lambdaFunctions: {
        descriptionEnhancer: lambda.Function;
        codeCreator: lambda.Function;
        reviewRefine: lambda.Function;
        finalizer: lambda.Function;
    };
    stateMachine: stepfunctions.StateMachine;
    workflowTable: dynamodb.Table;
    api: apigateway.RestApi;
}
export declare class WorkflowDashboard extends Construct {
    readonly dashboard: cloudwatch.Dashboard;
    constructor(scope: Construct, id: string, props: DashboardProps);
    private addApiGatewayWidgets;
    private addLambdaWidgets;
    private addStepFunctionsWidgets;
    private addDynamoDBWidgets;
    private addSystemOverviewWidgets;
}
export declare function createCustomMetrics(scope: Construct): {
    workflowCompletionRate: cloudwatch.Metric;
    averageProcessingTime: cloudwatch.Metric;
    codeQualityScore: cloudwatch.Metric;
};
export declare function createAlerts(scope: Construct, props: DashboardProps, notificationTopic: string): void;
