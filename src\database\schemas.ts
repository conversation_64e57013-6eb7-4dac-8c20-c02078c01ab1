/**
 * DynamoDB table schemas and data access patterns
 */

import { AttributeType, BillingMode, ProjectionType } from 'aws-cdk-lib/aws-dynamodb';

export interface WorkflowStateRecord {
  PK: string; // SESSION#{sessionId}
  SK: string; // WORKFLOW#{timestamp}
  GSI1PK: string; // STATUS#{status}
  GSI1SK: string; // CREATED#{createdAt}
  sessionId: string;
  currentIteration: number;
  maxIterations: number;
  status: 'initializing' | 'enhancing' | 'creating' | 'reviewing' | 'finalizing' | 'completed' | 'failed';
  specification?: string; // JSON stringified DetailedSpecification
  taskPlan?: string; // JSON stringified TaskPlan
  finalOutput?: string; // JSON stringified FinalOutput
  createdAt: string;
  updatedAt: string;
  ttl?: number; // TTL for automatic cleanup
}

export interface TaskRecord {
  PK: string; // SESSION#{sessionId}
  SK: string; // TASK#{taskId}
  GSI1PK: string; // STATUS#{status}
  GSI1SK: string; // PRIORITY#{priority}#{createdAt}
  GSI2PK: string; // AGENT#{agent}
  GSI2SK: string; // STATUS#{status}#{createdAt}
  sessionId: string;
  taskId: string;
  taskName: string;
  description: string;
  agent: 'CodeCreator' | 'ReviewRefine' | 'Finalizer';
  dependencies: string; // JSON stringified array
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  priority: number;
  estimatedDuration?: number;
  metadata: string; // JSON stringified metadata
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface ArtifactRecord {
  PK: string; // SESSION#{sessionId}
  SK: string; // ARTIFACT#{taskId}#{timestamp}
  GSI1PK: string; // TASK#{taskId}
  GSI1SK: string; // VERSION#{timestamp}
  sessionId: string;
  taskId: string;
  s3Key: string; // S3 key where the artifact is stored
  files: string; // JSON stringified GeneratedFile[]
  errors: string; // JSON stringified CodeError[]
  warnings: string; // JSON stringified CodeWarning[]
  metadata: string; // JSON stringified metadata
  createdAt: string;
  version: string;
}

export interface ReviewRecord {
  PK: string; // SESSION#{sessionId}
  SK: string; // REVIEW#{taskId}#{timestamp}
  GSI1PK: string; // TASK#{taskId}
  GSI1SK: string; // QUALITY#{overallQuality}#{timestamp}
  sessionId: string;
  taskId: string;
  overallQuality: 'excellent' | 'good' | 'fair' | 'poor';
  errors: string; // JSON stringified CodeError[]
  suggestions: string; // JSON stringified Suggestion[]
  securityIssues: string; // JSON stringified SecurityIssue[]
  performanceIssues: string; // JSON stringified PerformanceIssue[]
  updatedSpecDelta?: string; // JSON stringified partial spec
  updatedTasks?: string; // JSON stringified Task[]
  createdAt: string;
  reviewedBy: string; // Agent that performed the review
}

export interface UserSessionRecord {
  PK: string; // USER#{userId}
  SK: string; // SESSION#{sessionId}
  GSI1PK: string; // CREATED#{date}
  GSI1SK: string; // USER#{userId}
  userId: string;
  sessionId: string;
  userRequest: string; // JSON stringified UserRequest
  status: string;
  createdAt: string;
  lastAccessedAt: string;
  ttl?: number; // TTL for automatic cleanup
}

// Table definitions for CDK
export const WORKFLOW_TABLE_CONFIG = {
  tableName: 'AIAgentWorkflowTable',
  partitionKey: { name: 'PK', type: AttributeType.STRING },
  sortKey: { name: 'SK', type: AttributeType.STRING },
  billingMode: BillingMode.PAY_PER_REQUEST,
  timeToLiveAttribute: 'ttl',
  globalSecondaryIndexes: [
    {
      indexName: 'GSI1',
      partitionKey: { name: 'GSI1PK', type: AttributeType.STRING },
      sortKey: { name: 'GSI1SK', type: AttributeType.STRING },
      projectionType: ProjectionType.ALL,
    },
    {
      indexName: 'GSI2',
      partitionKey: { name: 'GSI2PK', type: AttributeType.STRING },
      sortKey: { name: 'GSI2SK', type: AttributeType.STRING },
      projectionType: ProjectionType.ALL,
    },
  ],
  pointInTimeRecovery: true,
  encryption: 'AWS_MANAGED',
};

// Data access patterns
export const ACCESS_PATTERNS = {
  // Workflow State
  getWorkflowState: (sessionId: string) => ({
    PK: `SESSION#${sessionId}`,
    SK: { beginsWith: 'WORKFLOW#' },
  }),
  
  // Tasks
  getTasksBySession: (sessionId: string) => ({
    PK: `SESSION#${sessionId}`,
    SK: { beginsWith: 'TASK#' },
  }),
  
  getTasksByStatus: (status: string) => ({
    IndexName: 'GSI1',
    GSI1PK: `STATUS#${status}`,
  }),
  
  getTasksByAgent: (agent: string, status?: string) => ({
    IndexName: 'GSI2',
    GSI2PK: `AGENT#${agent}`,
    GSI2SK: status ? { beginsWith: `STATUS#${status}#` } : undefined,
  }),
  
  // Artifacts
  getArtifactsBySession: (sessionId: string) => ({
    PK: `SESSION#${sessionId}`,
    SK: { beginsWith: 'ARTIFACT#' },
  }),
  
  getArtifactsByTask: (taskId: string) => ({
    IndexName: 'GSI1',
    GSI1PK: `TASK#${taskId}`,
  }),
  
  // Reviews
  getReviewsBySession: (sessionId: string) => ({
    PK: `SESSION#${sessionId}`,
    SK: { beginsWith: 'REVIEW#' },
  }),
  
  getReviewsByTask: (taskId: string) => ({
    IndexName: 'GSI1',
    GSI1PK: `TASK#${taskId}`,
  }),
  
  // User Sessions
  getUserSessions: (userId: string) => ({
    PK: `USER#${userId}`,
    SK: { beginsWith: 'SESSION#' },
  }),
  
  getSessionsByDate: (date: string) => ({
    IndexName: 'GSI1',
    GSI1PK: `CREATED#${date}`,
  }),
};

// Helper functions for creating records
export const createWorkflowStateRecord = (
  sessionId: string,
  workflowState: any,
  ttlHours: number = 168 // 7 days default
): WorkflowStateRecord => ({
  PK: `SESSION#${sessionId}`,
  SK: `WORKFLOW#${Date.now()}`,
  GSI1PK: `STATUS#${workflowState.status}`,
  GSI1SK: `CREATED#${workflowState.createdAt}`,
  sessionId,
  currentIteration: workflowState.currentIteration,
  maxIterations: workflowState.maxIterations,
  status: workflowState.status,
  specification: workflowState.specification ? JSON.stringify(workflowState.specification) : undefined,
  taskPlan: workflowState.taskPlan ? JSON.stringify(workflowState.taskPlan) : undefined,
  finalOutput: workflowState.finalOutput ? JSON.stringify(workflowState.finalOutput) : undefined,
  createdAt: workflowState.createdAt,
  updatedAt: workflowState.updatedAt,
  ttl: Math.floor(Date.now() / 1000) + (ttlHours * 3600),
});

export const createTaskRecord = (
  sessionId: string,
  task: any
): TaskRecord => ({
  PK: `SESSION#${sessionId}`,
  SK: `TASK#${task.taskId}`,
  GSI1PK: `STATUS#${task.status}`,
  GSI1SK: `PRIORITY#${task.priority}#${task.createdAt || new Date().toISOString()}`,
  GSI2PK: `AGENT#${task.agent}`,
  GSI2SK: `STATUS#${task.status}#${task.createdAt || new Date().toISOString()}`,
  sessionId,
  taskId: task.taskId,
  taskName: task.taskName,
  description: task.description,
  agent: task.agent,
  dependencies: JSON.stringify(task.dependencies || []),
  status: task.status,
  priority: task.priority,
  estimatedDuration: task.estimatedDuration,
  metadata: JSON.stringify(task.metadata || {}),
  createdAt: task.createdAt || new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  completedAt: task.status === 'completed' ? new Date().toISOString() : undefined,
});

export const createArtifactRecord = (
  sessionId: string,
  taskId: string,
  artifact: any,
  s3Key: string
): ArtifactRecord => ({
  PK: `SESSION#${sessionId}`,
  SK: `ARTIFACT#${taskId}#${Date.now()}`,
  GSI1PK: `TASK#${taskId}`,
  GSI1SK: `VERSION#${Date.now()}`,
  sessionId,
  taskId,
  s3Key,
  files: JSON.stringify(artifact.files || []),
  errors: JSON.stringify(artifact.errors || []),
  warnings: JSON.stringify(artifact.warnings || []),
  metadata: JSON.stringify(artifact.metadata || {}),
  createdAt: new Date().toISOString(),
  version: artifact.metadata?.version || '1.0.0',
});

export const createReviewRecord = (
  sessionId: string,
  taskId: string,
  review: any,
  reviewedBy: string
): ReviewRecord => ({
  PK: `SESSION#${sessionId}`,
  SK: `REVIEW#${taskId}#${Date.now()}`,
  GSI1PK: `TASK#${taskId}`,
  GSI1SK: `QUALITY#${review.overallQuality}#${Date.now()}`,
  sessionId,
  taskId,
  overallQuality: review.overallQuality,
  errors: JSON.stringify(review.errors || []),
  suggestions: JSON.stringify(review.suggestions || []),
  securityIssues: JSON.stringify(review.securityIssues || []),
  performanceIssues: JSON.stringify(review.performanceIssues || []),
  updatedSpecDelta: review.updatedSpecDelta ? JSON.stringify(review.updatedSpecDelta) : undefined,
  updatedTasks: review.updatedTasks ? JSON.stringify(review.updatedTasks) : undefined,
  createdAt: new Date().toISOString(),
  reviewedBy,
});

export const createUserSessionRecord = (
  userId: string,
  sessionId: string,
  userRequest: any,
  ttlHours: number = 720 // 30 days default
): UserSessionRecord => ({
  PK: `USER#${userId}`,
  SK: `SESSION#${sessionId}`,
  GSI1PK: `CREATED#${new Date().toISOString().split('T')[0]}`,
  GSI1SK: `USER#${userId}`,
  userId,
  sessionId,
  userRequest: JSON.stringify(userRequest),
  status: 'active',
  createdAt: new Date().toISOString(),
  lastAccessedAt: new Date().toISOString(),
  ttl: Math.floor(Date.now() / 1000) + (ttlHours * 3600),
});
