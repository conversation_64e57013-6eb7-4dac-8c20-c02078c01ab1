#!/bin/bash

# AI Agent Workflow System API Testing Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
API_ENDPOINT=${API_ENDPOINT:-""}
API_KEY=${API_KEY:-""}

# Check if required variables are set
check_config() {
    if [ -z "$API_ENDPOINT" ]; then
        print_error "API_ENDPOINT environment variable is not set"
        echo "Please set it with: export API_ENDPOINT=https://your-api-id.execute-api.region.amazonaws.com/prod"
        exit 1
    fi
    
    if [ -z "$API_KEY" ]; then
        print_error "API_KEY environment variable is not set"
        echo "Please set it with: export API_KEY=your-api-key"
        exit 1
    fi
    
    print_success "Configuration validated"
}

# Test health endpoint
test_health() {
    print_status "Testing health endpoint..."
    
    RESPONSE=$(curl -s -w "\n%{http_code}" "$API_ENDPOINT/status")
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "Health check passed"
        echo "Response: $BODY"
    else
        print_error "Health check failed with HTTP $HTTP_CODE"
        echo "Response: $BODY"
        return 1
    fi
}

# Test description enhancement
test_enhance() {
    print_status "Testing description enhancement..."
    
    REQUEST_BODY='{
        "userDescription": "Create a modern e-commerce website with user authentication, product catalog, shopping cart, and payment integration using Stripe",
        "preferences": {
            "techStack": ["react", "next.js", "typescript"],
            "deploymentTarget": "aws",
            "includeTests": true,
            "includeCICD": true
        }
    }'
    
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$API_ENDPOINT/enhance" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $API_KEY" \
        -d "$REQUEST_BODY")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "Description enhancement test passed"
        
        # Parse and display key information
        SESSION_ID=$(echo "$BODY" | jq -r '.sessionId // "N/A"')
        PROJECT_NAME=$(echo "$BODY" | jq -r '.specification.projectName // "N/A"')
        TASK_COUNT=$(echo "$BODY" | jq -r '.taskPlan.tasks | length // 0')
        
        echo "Session ID: $SESSION_ID"
        echo "Project Name: $PROJECT_NAME"
        echo "Number of Tasks: $TASK_COUNT"
        
        # Save session ID for workflow test
        export TEST_SESSION_ID="$SESSION_ID"
        
    else
        print_error "Description enhancement test failed with HTTP $HTTP_CODE"
        echo "Response: $BODY"
        return 1
    fi
}

# Test workflow execution
test_workflow() {
    print_status "Testing complete workflow..."
    
    REQUEST_BODY='{
        "userDescription": "Create a simple blog website with authentication and content management",
        "preferences": {
            "techStack": ["react", "express"],
            "deploymentTarget": "aws",
            "includeTests": false,
            "includeCICD": false
        }
    }'
    
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$API_ENDPOINT/workflow" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $API_KEY" \
        -d "$REQUEST_BODY")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "Workflow execution test passed"
        
        # Parse execution information
        EXECUTION_ARN=$(echo "$BODY" | jq -r '.executionArn // "N/A"')
        START_DATE=$(echo "$BODY" | jq -r '.startDate // "N/A"')
        
        echo "Execution ARN: $EXECUTION_ARN"
        echo "Start Date: $START_DATE"
        
        print_status "Workflow started successfully. You can monitor progress in the AWS Console."
        
    else
        print_error "Workflow execution test failed with HTTP $HTTP_CODE"
        echo "Response: $BODY"
        return 1
    fi
}

# Test error handling
test_error_handling() {
    print_status "Testing error handling..."
    
    # Test with invalid request (missing description)
    REQUEST_BODY='{
        "userDescription": "",
        "preferences": {}
    }'
    
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$API_ENDPOINT/enhance" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $API_KEY" \
        -d "$REQUEST_BODY")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "400" ]; then
        print_success "Error handling test passed (correctly returned 400)"
        echo "Error response: $BODY"
    else
        print_warning "Error handling test unexpected result: HTTP $HTTP_CODE"
        echo "Response: $BODY"
    fi
}

# Test authentication
test_auth() {
    print_status "Testing authentication..."
    
    # Test without API key
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$API_ENDPOINT/enhance" \
        -H "Content-Type: application/json" \
        -d '{"userDescription": "test"}')
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    
    if [ "$HTTP_CODE" = "403" ] || [ "$HTTP_CODE" = "401" ]; then
        print_success "Authentication test passed (correctly rejected request without API key)"
    else
        print_warning "Authentication test unexpected result: HTTP $HTTP_CODE"
    fi
}

# Performance test
test_performance() {
    print_status "Running basic performance test..."
    
    REQUEST_BODY='{
        "userDescription": "Create a simple landing page",
        "preferences": {
            "techStack": ["react"],
            "deploymentTarget": "aws"
        }
    }'
    
    START_TIME=$(date +%s.%N)
    
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST "$API_ENDPOINT/enhance" \
        -H "Content-Type: application/json" \
        -H "x-api-key: $API_KEY" \
        -d "$REQUEST_BODY")
    
    END_TIME=$(date +%s.%N)
    DURATION=$(echo "$END_TIME - $START_TIME" | bc)
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "Performance test completed in ${DURATION}s"
        
        if (( $(echo "$DURATION < 30" | bc -l) )); then
            print_success "Response time is acceptable (< 30s)"
        else
            print_warning "Response time is slow (> 30s)"
        fi
    else
        print_error "Performance test failed with HTTP $HTTP_CODE"
    fi
}

# Main testing function
main() {
    echo ""
    print_status "Starting AI Agent Workflow System API Tests"
    echo ""
    
    # Parse command line arguments
    RUN_ALL=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --health)
                RUN_ALL=false
                test_health
                shift
                ;;
            --enhance)
                RUN_ALL=false
                test_enhance
                shift
                ;;
            --workflow)
                RUN_ALL=false
                test_workflow
                shift
                ;;
            --performance)
                RUN_ALL=false
                test_performance
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --health        Test health endpoint only"
                echo "  --enhance       Test enhancement endpoint only"
                echo "  --workflow      Test workflow endpoint only"
                echo "  --performance   Run performance test only"
                echo "  --help          Show this help message"
                echo ""
                echo "Environment Variables:"
                echo "  API_ENDPOINT    API Gateway endpoint URL"
                echo "  API_KEY         API key for authentication"
                echo ""
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Check configuration
    check_config
    
    if [ "$RUN_ALL" = true ]; then
        # Run all tests
        echo ""
        test_health
        echo ""
        test_auth
        echo ""
        test_error_handling
        echo ""
        test_enhance
        echo ""
        test_workflow
        echo ""
        test_performance
        echo ""
        
        print_success "All API tests completed!"
    fi
    
    echo ""
}

# Run main function
main "$@"
