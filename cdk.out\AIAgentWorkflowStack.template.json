{"Description": "AI Agent Workflow System - Serverless multi-agent workflow for creating websites, backends, and chatbots", "Resources": {"EncryptionKey1B843E66": {"Type": "AWS::KMS::Key", "Properties": {"Description": "KMS key for AI Agent Workflow System", "EnableKeyRotation": true, "KeyPolicy": {"Statement": [{"Action": "kms:*", "Effect": "Allow", "Principal": {"AWS": "arn:aws:iam::337909760884:root"}, "Resource": "*"}], "Version": "2012-10-17"}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/EncryptionKey/Resource"}}, "WorkflowTable0AE28607": {"Type": "AWS::DynamoDB::Table", "Properties": {"AttributeDefinitions": [{"AttributeName": "PK", "AttributeType": "S"}, {"AttributeName": "SK", "AttributeType": "S"}, {"AttributeName": "GSI1PK", "AttributeType": "S"}, {"AttributeName": "GSI1SK", "AttributeType": "S"}, {"AttributeName": "GSI2PK", "AttributeType": "S"}, {"AttributeName": "GSI2SK", "AttributeType": "S"}], "BillingMode": "PAY_PER_REQUEST", "GlobalSecondaryIndexes": [{"IndexName": "GSI1", "KeySchema": [{"AttributeName": "GSI1PK", "KeyType": "HASH"}, {"AttributeName": "GSI1SK", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "GSI2", "KeySchema": [{"AttributeName": "GSI2PK", "KeyType": "HASH"}, {"AttributeName": "GSI2SK", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}], "KeySchema": [{"AttributeName": "PK", "KeyType": "HASH"}, {"AttributeName": "SK", "KeyType": "RANGE"}], "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}, "SSESpecification": {"KMSMasterKeyId": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}, "SSEEnabled": true, "SSEType": "KMS"}, "StreamSpecification": {"StreamViewType": "NEW_AND_OLD_IMAGES"}, "TableName": "AIAgentWorkflowTable", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "TimeToLiveSpecification": {"AttributeName": "ttl", "Enabled": true}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowTable/Resource"}}, "ArtifactsBucket2AAC5544": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"BucketEncryption": {"ServerSideEncryptionConfiguration": [{"ServerSideEncryptionByDefault": {"KMSMasterKeyID": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}, "SSEAlgorithm": "aws:kms"}}]}, "BucketName": "ai-agent-artifacts-337909760884-us-east-1", "CorsConfiguration": {"CorsRules": [{"AllowedHeaders": ["*"], "AllowedMethods": ["GET", "PUT", "POST"], "AllowedOrigins": ["*"], "MaxAge": 3000}]}, "LifecycleConfiguration": {"Rules": [{"Id": "DeleteOldVersions", "NoncurrentVersionExpiration": {"NoncurrentDays": 30}, "Status": "Enabled"}, {"Id": "TransitionToIA", "Status": "Enabled", "Transitions": [{"StorageClass": "STANDARD_IA", "TransitionInDays": 30}, {"StorageClass": "GLACIER", "TransitionInDays": 90}]}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}, "Tags": [{"Key": "aws-cdk:auto-delete-objects", "Value": "true"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "VersioningConfiguration": {"Status": "Enabled"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ArtifactsBucket/Resource"}}, "ArtifactsBucketPolicy852CB646": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "ArtifactsBucket2AAC5544"}, "PolicyDocument": {"Statement": [{"Action": ["s3:DeleteObject*", "s3:GetBucket*", "s3:List*", "s3:PutBucketPolicy"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ArtifactsBucket/Policy/Resource"}}, "ArtifactsBucketAutoDeleteObjectsCustomResource0E3B4320": {"Type": "Custom::S3AutoDeleteObjects", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F", "<PERSON><PERSON>"]}, "BucketName": {"Ref": "ArtifactsBucket2AAC5544"}}, "DependsOn": ["ArtifactsBucketPolicy852CB646"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ArtifactsBucket/AutoDeleteObjectsCustomResource/Default"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-337909760884-us-east-1", "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "index.handler", "Role": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Description": {"Fn::Join": ["", ["Lambda function for auto-deleting objects in ", {"Ref": "ArtifactsBucket2AAC5544"}, " S3 bucket."]]}}, "DependsOn": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "aws:asset:path": "asset.faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6", "aws:asset:property": "Code"}}, "DescriptionEnhancerFunctionServiceRole186883E6": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/Resource"}}, "DescriptionEnhancerFunctionServiceRoleDefaultPolicyACC2A16A": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "DescriptionEnhancerFunctionServiceRoleDefaultPolicyACC2A16A", "Roles": [{"Ref": "DescriptionEnhancerFunctionServiceRole186883E6"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/DefaultPolicy/Resource"}}, "DescriptionEnhancerFunction61F18C4F": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": "\n        exports.handler = async (event) => {\n          console.log('Description Enhancer called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              sessionId: 'test-session-' + Date.now(),\n              specification: {\n                projectName: 'Generated Project',\n                description: 'A test project generated by AI Agent Workflow',\n                features: ['Authentication', 'API', 'Database'],\n                techStack: {\n                  frontend: { framework: 'React', language: 'TypeScript' },\n                  backend: { framework: 'Express', language: 'Node.js' }\n                }\n              },\n              taskPlan: {\n                tasks: [{\n                  taskId: 'task-1',\n                  taskName: 'Setup Project Structure',\n                  description: 'Create basic project structure',\n                  status: 'pending'\n                }]\n              },\n              status: 'enhancement_completed'\n            })\n          };\n        };\n      "}, "Description": "Enhances user descriptions and creates detailed specifications", "Environment": {"Variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "Handler": "index.handler", "MemorySize": 1024, "Role": {"Fn::GetAtt": ["DescriptionEnhancerFunctionServiceRole186883E6", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Timeout": 900, "TracingConfig": {"Mode": "Active"}}, "DependsOn": ["DescriptionEnhancerFunctionServiceRoleDefaultPolicyACC2A16A", "DescriptionEnhancerFunctionServiceRole186883E6"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/Resource"}}, "DescriptionEnhancerFunctionLogRetentionA35D1A77": {"Type": "Custom::LogRetention", "Properties": {"ServiceToken": {"Fn::GetAtt": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A", "<PERSON><PERSON>"]}, "LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "DescriptionEnhancerFunction61F18C4F"}]]}, "RetentionInDays": 7}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/DescriptionEnhancerFunction/LogRetention/Resource"}}, "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/Resource"}}, "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["logs:DeleteRetentionPolicy", "logs:PutRetentionPolicy"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB", "Roles": [{"Ref": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/DefaultPolicy/Resource"}}, "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A": {"Type": "AWS::Lambda::Function", "Properties": {"Handler": "index.handler", "Runtime": "nodejs22.x", "Timeout": 900, "Code": {"S3Bucket": "cdk-hnb659fds-assets-337909760884-us-east-1", "S3Key": "2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d.zip"}, "Role": {"Fn::GetAtt": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB", "<PERSON><PERSON>"]}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "DependsOn": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB", "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Resource", "aws:asset:path": "asset.2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "CodeCreatorFunctionServiceRole9BB85716": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/Resource"}}, "CodeCreatorFunctionServiceRoleDefaultPolicyCEF966DD": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "CodeCreatorFunctionServiceRoleDefaultPolicyCEF966DD", "Roles": [{"Ref": "CodeCreatorFunctionServiceRole9BB85716"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/DefaultPolicy/Resource"}}, "CodeCreatorFunction11132470": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": "\n        exports.handler = async (event) => {\n          console.log('Code Creator called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              artifacts: [{\n                path: 'src/index.js',\n                content: 'console.log(\"Hello World!\");',\n                type: 'source',\n                language: 'javascript'\n              }],\n              status: 'code_generation_completed'\n            })\n          };\n        };\n      "}, "Description": "Generates code files based on specifications and tasks", "Environment": {"Variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "Handler": "index.handler", "MemorySize": 2048, "Role": {"Fn::GetAtt": ["CodeCreatorFunctionServiceRole9BB85716", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Timeout": 900, "TracingConfig": {"Mode": "Active"}}, "DependsOn": ["CodeCreatorFunctionServiceRoleDefaultPolicyCEF966DD", "CodeCreatorFunctionServiceRole9BB85716"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/CodeCreatorFunction/Resource"}}, "CodeCreatorFunctionLogRetentionCB5EF515": {"Type": "Custom::LogRetention", "Properties": {"ServiceToken": {"Fn::GetAtt": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A", "<PERSON><PERSON>"]}, "LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "CodeCreatorFunction11132470"}]]}, "RetentionInDays": 7}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/CodeCreatorFunction/LogRetention/Resource"}}, "ReviewRefineFunctionServiceRole3AA53DF4": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/Resource"}}, "ReviewRefineFunctionServiceRoleDefaultPolicy0BD1FFFC": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "ReviewRefineFunctionServiceRoleDefaultPolicy0BD1FFFC", "Roles": [{"Ref": "ReviewRefineFunctionServiceRole3AA53DF4"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/DefaultPolicy/Resource"}}, "ReviewRefineFunction5325599E": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": "\n        exports.handler = async (event) => {\n          console.log('Review & Refine called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              reviewResults: {\n                errors: [],\n                warnings: [],\n                suggestions: ['Code looks good!'],\n                qualityScore: 85\n              },\n              status: 'review_completed'\n            })\n          };\n        };\n      "}, "Description": "Reviews and refines generated code", "Environment": {"Variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "Handler": "index.handler", "MemorySize": 1536, "Role": {"Fn::GetAtt": ["ReviewRefineFunctionServiceRole3AA53DF4", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Timeout": 600, "TracingConfig": {"Mode": "Active"}}, "DependsOn": ["ReviewRefineFunctionServiceRoleDefaultPolicy0BD1FFFC", "ReviewRefineFunctionServiceRole3AA53DF4"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ReviewRefineFunction/Resource"}}, "ReviewRefineFunctionLogRetentionD96E5990": {"Type": "Custom::LogRetention", "Properties": {"ServiceToken": {"Fn::GetAtt": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A", "<PERSON><PERSON>"]}, "LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "ReviewRefineFunction5325599E"}]]}, "RetentionInDays": 7}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ReviewRefineFunction/LogRetention/Resource"}}, "FinalizerFunctionServiceRole7963A895": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/Resource"}}, "FinalizerFunctionServiceRoleDefaultPolicyA64092A9": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}, {"Action": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "Effect": "Allow", "Resource": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}}, {"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:ConditionCheckItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable", "dynamodb:GetItem", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:PutItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:UpdateItem"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["WorkflowTable0AE28607", "<PERSON><PERSON>"]}, "/index/*"]]}]}, {"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ArtifactsBucket2AAC5544", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "FinalizerFunctionServiceRoleDefaultPolicyA64092A9", "Roles": [{"Ref": "FinalizerFunctionServiceRole7963A895"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/FinalizerFunction/ServiceRole/DefaultPolicy/Resource"}}, "FinalizerFunctionCCBC19F3": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": "\n        exports.handler = async (event) => {\n          console.log('Finalizer called with:', JSON.stringify(event, null, 2));\n          return {\n            statusCode: 200,\n            body: JSON.stringify({\n              finalOutput: {\n                downloadUrl: 'https://example.com/download/project.zip',\n                documentation: 'Project documentation generated successfully',\n                deploymentInstructions: 'Run npm install && npm start'\n              },\n              status: 'finalization_completed'\n            })\n          };\n        };\n      "}, "Description": "Generates final artifacts and documentation", "Environment": {"Variables": {"WORKFLOW_TABLE_NAME": {"Ref": "WorkflowTable0AE28607"}, "ARTIFACTS_BUCKET_NAME": {"Ref": "ArtifactsBucket2AAC5544"}, "LOG_LEVEL": "info", "GEMINI_API_KEY": "", "OPENROUTER_API_KEY": "", "DEEPSEEK_API_KEY": ""}}, "Handler": "index.handler", "MemorySize": 1536, "Role": {"Fn::GetAtt": ["FinalizerFunctionServiceRole7963A895", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Timeout": 600, "TracingConfig": {"Mode": "Active"}}, "DependsOn": ["FinalizerFunctionServiceRoleDefaultPolicyA64092A9", "FinalizerFunctionServiceRole7963A895"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/FinalizerFunction/Resource"}}, "FinalizerFunctionLogRetention0FC5B818": {"Type": "Custom::LogRetention", "Properties": {"ServiceToken": {"Fn::GetAtt": ["LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A", "<PERSON><PERSON>"]}, "LogGroupName": {"Fn::Join": ["", ["/aws/lambda/", {"Ref": "FinalizerFunctionCCBC19F3"}]]}, "RetentionInDays": 7}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/FinalizerFunction/LogRetention/Resource"}}, "StateMachineLogGroup15B91BCB": {"Type": "AWS::Logs::LogGroup", "Properties": {"RetentionInDays": 7, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/StateMachineLogGroup/Resource"}}, "WorkflowStateMachineRoleE0545793": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/Resource"}}, "WorkflowStateMachineRoleDefaultPolicyDAB74B4D": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, ":*"]]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, ":*"]]}]}, {"Action": ["logs:CreateLogDelivery", "logs:DeleteLogDelivery", "logs:DescribeLogGroups", "logs:DescribeResourcePolicies", "logs:GetLogDelivery", "logs:ListLogDeliveries", "logs:PutResourcePolicy", "logs:UpdateLogDelivery", "xray:GetSamplingRules", "xray:GetSamplingTargets", "xray:PutTelemetryRecords", "xray:PutTraceSegments"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}, "PolicyName": "WorkflowStateMachineRoleDefaultPolicyDAB74B4D", "Roles": [{"Ref": "WorkflowStateMachineRoleE0545793"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowStateMachine/Role/DefaultPolicy/Resource"}}, "WorkflowStateMachine67D94DDA": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": {"Fn::Join": ["", ["{\"StartAt\":\"InitializeWorkflow\",\"States\":{\"InitializeWorkflow\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"userRequest.$\":\"$.userRequest\",\"currentIteration\":0,\"maxIterations\":5,\"status\":\"initializing\"},\"Next\":\"EnhanceDescriptionTask\"},\"EnhanceDescriptionTask\":{\"Next\":\"PrepareTaskExecution\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"PrepareTaskExecution\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"currentIteration.$\":\"$.currentIteration\",\"maxIterations.$\":\"$.maxIterations\",\"artifacts\":[],\"reviews\":[]},\"Next\":\"ExecuteTasksMap\"},\"PrepareNextIteration\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"currentIteration.$\":\"States.MathAdd($.currentIteration, 1)\",\"maxIterations.$\":\"$.maxIterations\",\"artifacts.$\":\"$.artifacts\",\"reviews.$\":\"$.reviewResults\"},\"Next\":\"PrepareTaskExecution\"},\"EvaluateReviewResults\":{\"Type\":\"Choice\",\"Choices\":[{\"And\":[{\"Variable\":\"$.reviewResults[0].errors\",\"IsPresent\":true},{\"Variable\":\"$.currentIteration\",\"NumericLessThan\":5}],\"Next\":\"PrepareNextIteration\"},{\"Variable\":\"$.currentIteration\",\"NumericGreaterThanEquals\":5,\"Next\":\"MaxIterationsReached\"}],\"Default\":\"FinalizeTaskNormal\"},\"ReviewRefineTask\":{\"Next\":\"EvaluateReviewResults\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["ReviewRefineFunction5325599E", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"CollectArtifacts\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"taskPlan.$\":\"$.taskPlan\",\"artifacts.$\":\"$[*].Payload\",\"currentIteration.$\":\"$.currentIteration\",\"maxIterations.$\":\"$.maxIterations\"},\"Next\":\"ReviewRefineTask\"},\"ExecuteTasksMap\":{\"Type\":\"Map\",\"Next\":\"CollectArtifacts\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"specification.$\":\"$.specification\",\"task.$\":\"$$.Map.Item.Value\",\"iteration.$\":\"$.currentIteration\"},\"ItemsPath\":\"$.taskPlan.tasks\",\"MaxConcurrency\":3,\"Iterator\":{\"StartAt\":\"CreateCodeTask\",\"States\":{\"CreateCodeTask\":{\"End\":true,\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["CodeCreatorFunction11132470", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}}}}},\"FinalizeTaskNormal\":{\"Next\":\"WorkflowCompleted\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"WorkflowCompleted\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"status\":\"completed\",\"finalOutput.$\":\"$.finalOutput\",\"message\":\"Workflow completed successfully\",\"completedAt.$\":\"$$.State.EnteredTime\"},\"End\":true},\"FinalizeTaskMaxIterations\":{\"Next\":\"WorkflowCompleted\",\"Retry\":[{\"ErrorEquals\":[\"Lambda.ClientExecutionTimeoutException\",\"Lambda.ServiceException\",\"Lambda.AWSLambdaException\",\"Lambda.SdkClientException\"],\"IntervalSeconds\":2,\"MaxAttempts\":6,\"BackoffRate\":2}],\"Type\":\"Task\",\"OutputPath\":\"$.Payload\",\"Resource\":\"arn:", {"Ref": "AWS::Partition"}, ":states:::lambda:invoke\",\"Parameters\":{\"FunctionName\":\"", {"Fn::GetAtt": ["FinalizerFunctionCCBC19F3", "<PERSON><PERSON>"]}, "\",\"Payload.$\":\"$\"}},\"MaxIterationsReached\":{\"Type\":\"Pass\",\"Parameters\":{\"sessionId.$\":\"$.sessionId\",\"status\":\"max_iterations_reached\",\"message\":\"Maximum iterations reached. Proceeding with current artifacts.\",\"artifacts.$\":\"$.artifacts\",\"reviews.$\":\"$.reviewResults\"},\"Next\":\"FinalizeTaskMaxIterations\"}},\"TimeoutSeconds\":7200}"]]}, "LoggingConfiguration": {"Destinations": [{"CloudWatchLogsLogGroup": {"LogGroupArn": {"Fn::GetAtt": ["StateMachineLogGroup15B91BCB", "<PERSON><PERSON>"]}}}], "Level": "ALL"}, "RoleArn": {"Fn::GetAtt": ["WorkflowStateMachineRoleE0545793", "<PERSON><PERSON>"]}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "TracingConfiguration": {"Enabled": true}}, "DependsOn": ["WorkflowStateMachineRoleDefaultPolicyDAB74B4D", "WorkflowStateMachineRoleE0545793"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowStateMachine/Resource"}}, "WorkflowApiB9CC683F": {"Type": "AWS::ApiGateway::RestApi", "Properties": {"Description": "API for AI Agent Workflow System", "Name": "AI Agent Workflow API", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Resource"}}, "WorkflowApiCloudWatchRole89411D9E": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "apigateway.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs"]]}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/CloudWatchRole/Resource"}}, "WorkflowApiAccount9EF7FB09": {"Type": "AWS::ApiGateway::Account", "Properties": {"CloudWatchRoleArn": {"Fn::GetAtt": ["WorkflowApiCloudWatchRole89411D9E", "<PERSON><PERSON>"]}}, "DependsOn": ["WorkflowApiB9CC683F"], "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Account"}}, "WorkflowApiDeployment5BD8B8386f483f33cec646eff48257f9e9ca6c37": {"Type": "AWS::ApiGateway::Deployment", "Properties": {"Description": "API for AI Agent Workflow System", "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "DependsOn": ["WorkflowApienhanceOPTIONS729F011E", "WorkflowApienhancePOST285C704C", "WorkflowApienhanceCF77A6D6", "WorkflowApiOPTIONS79A098FC", "WorkflowApistatusGET50C9E4F5", "WorkflowApistatusOPTIONSC24293F2", "WorkflowApistatus89EB6A98", "WorkflowApiworkflowOPTIONS5F08821B", "WorkflowApiworkflowPOST56F4BBCB", "WorkflowApiworkflow843BCBF5"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Deployment/Resource", "aws:cdk:do-not-refactor": true}}, "WorkflowApiDeploymentStageprod72BAE0C9": {"Type": "AWS::ApiGateway::Stage", "Properties": {"DeploymentId": {"Ref": "WorkflowApiDeployment5BD8B8386f483f33cec646eff48257f9e9ca6c37"}, "MethodSettings": [{"DataTraceEnabled": true, "HttpMethod": "*", "LoggingLevel": "INFO", "MetricsEnabled": true, "ResourcePath": "/*"}], "RestApiId": {"Ref": "WorkflowApiB9CC683F"}, "StageName": "prod", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "TracingEnabled": true}, "DependsOn": ["WorkflowApiAccount9EF7FB09"], "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/DeploymentStage.prod/Resource"}}, "WorkflowApiOPTIONS79A098FC": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/OPTIONS/Resource"}}, "WorkflowApienhanceCF77A6D6": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "PathPart": "enhance", "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/Resource"}}, "WorkflowApienhanceOPTIONS729F011E": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "WorkflowApienhanceCF77A6D6"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/OPTIONS/Resource"}}, "WorkflowApienhancePOSTApiPermissionAIAgentWorkflowStackWorkflowApi55692A50POSTenhanceCE03D6B0": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-east-1:337909760884:", {"Ref": "WorkflowApiB9CC683F"}, "/", {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "/POST/enhance"]]}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance"}}, "WorkflowApienhancePOSTApiPermissionTestAIAgentWorkflowStackWorkflowApi55692A50POSTenhanceA0A0F77A": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:us-east-1:337909760884:", {"Ref": "WorkflowApiB9CC683F"}, "/test-invoke-stage/POST/enhance"]]}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.Test.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance"}}, "WorkflowApienhancePOST285C704C": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": true, "AuthorizationType": "NONE", "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "StatusCode": "200"}, {"ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\":400.*", "StatusCode": "400"}, {"ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "SelectionPattern": ".*\"statusCode\":500.*", "StatusCode": "500"}], "RequestTemplates": {"application/json": "{\"userRequest\":\"$input.json(\\\"$\\\")\",\"sessionId\":\"$context.requestId\",\"userId\":\"$context.identity.apiKey\"}"}, "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["DescriptionEnhancerFunction61F18C4F", "<PERSON><PERSON>"]}, "/invocations"]]}}, "MethodResponses": [{"StatusCode": "200"}, {"StatusCode": "400"}, {"StatusCode": "500"}], "ResourceId": {"Ref": "WorkflowApienhanceCF77A6D6"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/Resource"}}, "WorkflowApiworkflow843BCBF5": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "PathPart": "workflow", "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/Resource"}}, "WorkflowApiworkflowOPTIONS5F08821B": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "WorkflowApiworkflow843BCBF5"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/OPTIONS/Resource"}}, "WorkflowApiworkflowPOST56F4BBCB": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": true, "AuthorizationType": "NONE", "HttpMethod": "POST", "Integration": {"Credentials": {"Fn::GetAtt": ["StepFunctionsApiRole3B314676", "<PERSON><PERSON>"]}, "IntegrationHttpMethod": "POST", "IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}, "ResponseTemplates": {"application/json": "{\"executionArn\":\"$input.json(\\\"$.executionArn\\\")\",\"startDate\":\"$input.json(\\\"$.startDate\\\")\",\"message\":\"Workflow started successfully\"}"}, "StatusCode": "200"}], "RequestTemplates": {"application/json": {"Fn::Join": ["", ["{\"stateMachineArn\":\"", {"Ref": "WorkflowStateMachine67D94DDA"}, "\",\"input\":\"{\\\"sessionId\\\":\\\"$context.requestId\\\",\\\"userRequest\\\":\\\"$input.json(\\\\\\\"$\\\\\\\")\\\"}\"}"]]}}, "Type": "AWS", "Uri": "arn:aws:apigateway:us-east-1:states:action/StartExecution"}, "MethodResponses": [{"StatusCode": "200"}, {"StatusCode": "400"}, {"StatusCode": "500"}], "ResourceId": {"Ref": "WorkflowApiworkflow843BCBF5"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/workflow/POST/Resource"}}, "WorkflowApistatus89EB6A98": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["WorkflowApiB9CC683F", "RootResourceId"]}, "PathPart": "status", "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/status/Resource"}}, "WorkflowApistatusOPTIONSC24293F2": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "WorkflowApistatus89EB6A98"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/status/OPTIONS/Resource"}}, "WorkflowApistatusGET50C9E4F5": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "GET", "Integration": {"IntegrationResponses": [{"ResponseTemplates": {"application/json": "{\"status\":\"healthy\",\"timestamp\":\"$context.requestTime\",\"version\":\"1.0.0\"}"}, "StatusCode": "200"}], "RequestTemplates": {"application/json": "{\"statusCode\": 200}"}, "Type": "MOCK"}, "MethodResponses": [{"StatusCode": "200"}], "ResourceId": {"Ref": "WorkflowApistatus89EB6A98"}, "RestApiId": {"Ref": "WorkflowApiB9CC683F"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApi/Default/status/GET/Resource"}}, "WorkflowApiKeyD3ED92A1": {"Type": "AWS::ApiGateway::ApiKey", "Properties": {"Description": "API Key for AI Agent Workflow System", "Enabled": true, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowApiKey/Resource"}}, "WorkflowUsagePlanCB7C6F4A": {"Type": "AWS::ApiGateway::UsagePlan", "Properties": {"ApiStages": [{"ApiId": {"Ref": "WorkflowApiB9CC683F"}, "Stage": {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "Throttle": {}}], "Description": "Usage plan for AI Agent Workflow API", "Quota": {"Limit": 10000, "Period": "MONTH"}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Throttle": {"BurstLimit": 200, "RateLimit": 100}, "UsagePlanName": "AI Agent Workflow Usage Plan"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowUsagePlan/Resource"}}, "WorkflowUsagePlanUsagePlanKeyResourceAIAgentWorkflowStackWorkflowApiKey9C182D459B14483E": {"Type": "AWS::ApiGateway::UsagePlanKey", "Properties": {"KeyId": {"Ref": "WorkflowApiKeyD3ED92A1"}, "KeyType": "API_KEY", "UsagePlanId": {"Ref": "WorkflowUsagePlanCB7C6F4A"}}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/WorkflowUsagePlan/UsagePlanKeyResource:AIAgentWorkflowStackWorkflowApiKey9C182D45"}}, "StepFunctionsApiRole3B314676": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "apigateway.amazonaws.com"}}], "Version": "2012-10-17"}, "Policies": [{"PolicyDocument": {"Statement": [{"Action": "states:StartExecution", "Effect": "Allow", "Resource": {"Ref": "WorkflowStateMachine67D94DDA"}}], "Version": "2012-10-17"}, "PolicyName": "StepFunctionsExecutionPolicy"}], "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/StepFunctionsApiRole/Resource"}}, "ErrorNotificationTopic0DACB6F3": {"Type": "AWS::SNS::Topic", "Properties": {"DisplayName": "AI Agent Workflow Errors", "KmsMasterKeyId": {"Fn::GetAtt": ["EncryptionKey1B843E66", "<PERSON><PERSON>"]}, "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/ErrorNotificationTopic/Resource"}}, "descriptionEnhancerErrorAlarmB44CE132": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "EvaluationPeriods": 2, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 5, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/descriptionEnhancerErrorAlarm/Resource"}}, "descriptionEnhancerDurationAlarm85FCFAD9": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "EvaluationPeriods": 2, "MetricName": "Duration", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Average", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 600000, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/descriptionEnhancerDurationAlarm/Resource"}}, "descriptionEnhancerThrottleAlarmDA4A9020": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "DescriptionEnhancerFunction61F18C4F"}}], "EvaluationPeriods": 1, "MetricName": "<PERSON>hrottles", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/descriptionEnhancerThrottleAlarm/Resource"}}, "codeCreatorErrorAlarm54EDFA6E": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "CodeCreatorFunction11132470"}}], "EvaluationPeriods": 2, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 5, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/codeCreatorErrorAlarm/Resource"}}, "codeCreatorDurationAlarm62B698E9": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "CodeCreatorFunction11132470"}}], "EvaluationPeriods": 2, "MetricName": "Duration", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Average", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 600000, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/codeCreatorDurationAlarm/Resource"}}, "codeCreatorThrottleAlarm894F3E5C": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "CodeCreatorFunction11132470"}}], "EvaluationPeriods": 1, "MetricName": "<PERSON>hrottles", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/codeCreatorThrottleAlarm/Resource"}}, "reviewRefineErrorAlarmAC4A9E11": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "ReviewRefineFunction5325599E"}}], "EvaluationPeriods": 2, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 5, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/reviewRefineErrorAlarm/Resource"}}, "reviewRefineDurationAlarm2B9ACC7F": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "ReviewRefineFunction5325599E"}}], "EvaluationPeriods": 2, "MetricName": "Duration", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Average", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 600000, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/reviewRefineDurationAlarm/Resource"}}, "reviewRefineThrottleAlarm43B3D31E": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "ReviewRefineFunction5325599E"}}], "EvaluationPeriods": 1, "MetricName": "<PERSON>hrottles", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/reviewRefineThrottleAlarm/Resource"}}, "finalizerErrorAlarmB06CD5D7": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "EvaluationPeriods": 2, "MetricName": "Errors", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 5, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/finalizerErrorAlarm/Resource"}}, "finalizerDurationAlarm00CE24F0": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "EvaluationPeriods": 2, "MetricName": "Duration", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Average", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 600000, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/finalizerDurationAlarm/Resource"}}, "finalizerThrottleAlarm2689022E": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "FunctionName", "Value": {"Ref": "FinalizerFunctionCCBC19F3"}}], "EvaluationPeriods": 1, "MetricName": "<PERSON>hrottles", "Namespace": "AWS/Lambda", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/finalizerThrottleAlarm/Resource"}}, "StateMachineFailedAlarm79113CC8": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "StateMachineArn", "Value": {"Ref": "WorkflowStateMachine67D94DDA"}}], "EvaluationPeriods": 1, "MetricName": "ExecutionsFailed", "Namespace": "AWS/States", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/StateMachineFailedAlarm/Resource"}}, "DynamoDBThrottleAlarmF21EBBE2": {"Type": "AWS::CloudWatch::Alarm", "Properties": {"ComparisonOperator": "GreaterThanOrEqualToThreshold", "Dimensions": [{"Name": "TableName", "Value": {"Ref": "WorkflowTable0AE28607"}}], "EvaluationPeriods": 2, "MetricName": "ThrottledRequests", "Namespace": "AWS/DynamoDB", "Period": 300, "Statistic": "Sum", "Tags": [{"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}], "Threshold": 1, "TreatMissingData": "notBreaching"}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/DynamoDBThrottleAlarm/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/22R227bMAyGn6X3ipZ5fYG0w4qhCWo43XXAyKyj2hYNk0pgCHr3QbKDpECv+JGU+PNQ6GJd6PUDXHhl6nbV2aMOewHTKrjwIbQ96/CKk3r+cK84RVVPDnqqjzq8w7HDFM8QFf/S4cmbFiUFF5pNSZ010y28+LPzBIxRddAfa9Dhj3dGLLn0+J5LHHvLbMlFZaHXoaJZPdubwExRddSwDltqKhR0ucqWmpeR/JCeXTm1fQBmFNabZBQLDh+LMB8EuGW9zc39dWdq8esDHUpgVjsY1POJrEG1FxDcgTlZl/u796OCwTYgeIFJhwpZNoPNQ9xwYwx5J+o3Dh1NPbq8zjtvL9BcKzeoKmTyo8G0RrVDOVGdkgtds4vIzJvBLidd6B9Dg2UHedPfO/n4nAZ+p8GafPcEUZmOfH0BMScdNh2MfS6cIMY7fc9C/bftfE2VI51tjWOeJ58kzWldk/68eRm8ROWoRv3JP85FoX8+6vXDJ1u7Gr0T26OuZvsf/Y6YHNwCAAA="}, "Metadata": {"aws:cdk:path": "AIAgentWorkflowStack/CDKMetadata/Default"}}}, "Outputs": {"WorkflowApiEndpointC0DC4592": {"Value": {"Fn::Join": ["", ["https://", {"Ref": "WorkflowApiB9CC683F"}, ".execute-api.us-east-1.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "/"]]}}, "ApiEndpoint": {"Description": "API Gateway endpoint URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "WorkflowApiB9CC683F"}, ".execute-api.us-east-1.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "WorkflowApiDeploymentStageprod72BAE0C9"}, "/"]]}}, "StateMachineArn": {"Description": "Step Functions state machine ARN", "Value": {"Ref": "WorkflowStateMachine67D94DDA"}}, "WorkflowTableName": {"Description": "DynamoDB table name", "Value": {"Ref": "WorkflowTable0AE28607"}}, "ArtifactsBucketName": {"Description": "S3 bucket name for artifacts", "Value": {"Ref": "ArtifactsBucket2AAC5544"}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}