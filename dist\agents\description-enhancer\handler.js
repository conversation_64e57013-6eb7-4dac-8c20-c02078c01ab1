"use strict";
/**
 * Description Enhancer Agent
 * Takes user input and creates detailed specifications and task plans
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const validation_1 = require("../../utils/validation");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Description Enhancer Agent started', { requestId, event });
    try {
        // Parse and validate input
        const body = JSON.parse(event.body || '{}');
        const userRequest = body.userRequest;
        const sessionId = body.sessionId || (0, uuid_1.v4)();
        const userId = body.userId || 'anonymous';
        // Validate user request
        const validationResult = (0, validation_1.validateUserRequest)(userRequest);
        if (!validationResult.isValid) {
            return {
                statusCode: 400,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                },
                body: JSON.stringify({
                    error: 'Invalid user request',
                    details: validationResult.errors,
                }),
            };
        }
        // Create user session record
        const userSessionRecord = (0, schemas_1.createUserSessionRecord)(userId, sessionId, userRequest);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: userSessionRecord,
        }));
        // Initialize workflow state
        const workflowState = {
            sessionId,
            currentIteration: 0,
            maxIterations: 5,
            status: 'enhancing',
            artifacts: [],
            reviews: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        // Enhance the description using LLM
        const enhancementResult = await enhanceDescription(userRequest, requestId);
        workflowState.specification = enhancementResult.specification;
        workflowState.taskPlan = enhancementResult.taskPlan;
        workflowState.status = 'creating';
        // Save workflow state to DynamoDB
        const workflowRecord = (0, schemas_1.createWorkflowStateRecord)(sessionId, workflowState);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: workflowRecord,
        }));
        logger_1.logger.info('Description enhancement completed', {
            requestId,
            sessionId,
            tasksCount: enhancementResult.taskPlan.tasks.length
        });
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
                sessionId,
                specification: enhancementResult.specification,
                taskPlan: enhancementResult.taskPlan,
                status: 'enhancement_completed',
                message: 'Description enhanced successfully. Code generation will begin shortly.',
            }),
        };
    }
    catch (error) {
        logger_1.logger.error('Description Enhancer Agent failed', { requestId, error });
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
                error: 'Internal server error',
                message: 'Failed to enhance description',
                requestId,
            }),
        };
    }
};
exports.handler = handler;
async function enhanceDescription(userRequest, requestId) {
    const enhancementPrompt = `
You are an expert software architect and project manager. Your task is to transform a user's high-level description into a comprehensive, detailed specification and task plan for building a production-ready application.

User Request:
${JSON.stringify(userRequest, null, 2)}

Please analyze this request and create:

1. A DETAILED SPECIFICATION that includes:
   - Project name and comprehensive description
   - Complete feature list with priorities
   - Recommended tech stack (frontend, backend, database, infrastructure)
   - System architecture and components
   - Data models with relationships
   - API endpoints specification
   - UI components structure
   - Chatbot flows (if applicable)
   - Deployment configuration

2. A COMPREHENSIVE TASK PLAN that breaks down the work into specific, actionable tasks:
   - Each task should be atomic and completable by a code generation agent
   - Include proper dependencies between tasks
   - Prioritize tasks logically (infrastructure → backend → frontend → integration)
   - Estimate complexity and duration
   - Categorize tasks by type (frontend, backend, chatbot, infrastructure, documentation)

Requirements:
- Make the specification production-ready with proper error handling, security, and scalability considerations
- Include modern best practices and patterns
- Consider CI/CD, testing, monitoring, and documentation
- Ensure the task plan is executable and well-structured
- Include specific file names and code structure in task descriptions

Return your response as a JSON object with 'specification' and 'taskPlan' properties that match the TypeScript interfaces provided.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(enhancementPrompt, {
            temperature: 0.7,
            maxTokens: 4000,
            requestId,
        });
        const result = JSON.parse(response);
        // Validate and enhance the response
        const specification = validateAndEnhanceSpecification(result.specification, userRequest);
        const taskPlan = validateAndEnhanceTaskPlan(result.taskPlan, specification);
        return { specification, taskPlan };
    }
    catch (error) {
        logger_1.logger.error('LLM enhancement failed', { requestId, error });
        throw new Error('Failed to enhance description with LLM');
    }
}
function validateAndEnhanceSpecification(spec, userRequest) {
    // Add validation and enhancement logic
    const enhanced = {
        projectName: spec.projectName || generateProjectName(userRequest.userDescription),
        description: spec.description || userRequest.userDescription,
        features: spec.features || [],
        techStack: spec.techStack || getDefaultTechStack(userRequest),
        architecture: spec.architecture || getDefaultArchitecture(),
        dataModels: spec.dataModels || [],
        apiEndpoints: spec.apiEndpoints || [],
        uiComponents: spec.uiComponents || [],
        chatbotFlows: spec.chatbotFlows,
        deploymentConfig: spec.deploymentConfig || getDefaultDeploymentConfig(),
        metadata: {
            createdAt: new Date().toISOString(),
            version: '1.0.0',
            estimatedComplexity: estimateComplexity(spec),
        },
    };
    return enhanced;
}
function validateAndEnhanceTaskPlan(taskPlan, specification) {
    const tasks = (taskPlan.tasks || []).map((task, index) => ({
        taskId: task.taskId || (0, uuid_1.v4)(),
        taskName: task.taskName || `Task ${index + 1}`,
        description: task.description || '',
        agent: task.agent || 'CodeCreator',
        dependencies: task.dependencies || [],
        status: 'pending',
        priority: task.priority || index + 1,
        estimatedDuration: task.estimatedDuration || 30,
        metadata: {
            category: task.metadata?.category || 'backend',
            complexity: task.metadata?.complexity || 'medium',
            files: task.metadata?.files || [],
        },
    }));
    return {
        tasks,
        totalEstimatedDuration: tasks.reduce((sum, task) => sum + (task.estimatedDuration || 0), 0),
        dependencies: tasks.reduce((deps, task) => {
            deps[task.taskId] = task.dependencies;
            return deps;
        }, {}),
    };
}
function generateProjectName(description) {
    // Simple project name generation logic
    const words = description.toLowerCase().split(' ').slice(0, 3);
    return words.join('-') + '-app';
}
function getDefaultTechStack(userRequest) {
    return {
        frontend: {
            framework: userRequest.preferences?.techStack?.includes('react') ? 'React' : 'Next.js',
            language: 'TypeScript',
            styling: 'Tailwind CSS',
            stateManagement: 'Zustand',
        },
        backend: {
            framework: 'Express.js',
            language: 'Node.js',
            database: 'PostgreSQL',
            authentication: 'JWT',
        },
        infrastructure: {
            cloud: 'AWS',
            deployment: 'Serverless',
            monitoring: 'CloudWatch',
        },
    };
}
function getDefaultArchitecture() {
    return {
        type: 'serverless',
        components: [],
        integrations: [],
    };
}
function getDefaultDeploymentConfig() {
    return {
        environment: 'development',
        environmentVariables: {},
        scaling: {
            minInstances: 1,
            maxInstances: 10,
        },
    };
}
function estimateComplexity(spec) {
    const featureCount = (spec.features || []).length;
    const modelCount = (spec.dataModels || []).length;
    const endpointCount = (spec.apiEndpoints || []).length;
    const totalComplexity = featureCount + modelCount + endpointCount;
    if (totalComplexity < 10)
        return 'low';
    if (totalComplexity < 25)
        return 'medium';
    return 'high';
}
//# sourceMappingURL=data:application/json;base64,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