"use strict";
/**
 * Description Enhancer Agent
 * Takes user input and creates detailed specifications and task plans
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const validation_1 = require("../../utils/validation");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Description Enhancer Agent started', { requestId, event });
    try {
        // Parse and validate input
        const body = JSON.parse(event.body || '{}');
        const userRequest = body.userRequest;
        const sessionId = body.sessionId || (0, uuid_1.v4)();
        const userId = body.userId || 'anonymous';
        // Validate user request
        const validationResult = (0, validation_1.validateUserRequest)(userRequest);
        if (!validationResult.isValid) {
            return {
                statusCode: 400,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                },
                body: JSON.stringify({
                    error: 'Invalid user request',
                    details: validationResult.errors,
                }),
            };
        }
        // Create user session record
        const userSessionRecord = (0, schemas_1.createUserSessionRecord)(userId, sessionId, userRequest);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: userSessionRecord,
        }));
        // Initialize workflow state
        const workflowState = {
            sessionId,
            currentIteration: 0,
            maxIterations: 5,
            status: 'enhancing',
            artifacts: [],
            reviews: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        // Enhance the description using LLM
        const enhancementResult = await enhanceDescription(userRequest, requestId);
        workflowState.specification = enhancementResult.specification;
        workflowState.taskPlan = enhancementResult.taskPlan;
        workflowState.status = 'creating';
        // Save workflow state to DynamoDB
        const workflowRecord = (0, schemas_1.createWorkflowStateRecord)(sessionId, workflowState);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: workflowRecord,
        }));
        logger_1.logger.info('Description enhancement completed', {
            requestId,
            sessionId,
            tasksCount: enhancementResult.taskPlan.tasks.length
        });
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
                sessionId,
                specification: enhancementResult.specification,
                taskPlan: enhancementResult.taskPlan,
                status: 'enhancement_completed',
                message: 'Description enhanced successfully. Code generation will begin shortly.',
            }),
        };
    }
    catch (error) {
        logger_1.logger.error('Description Enhancer Agent failed', { requestId, error });
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
            },
            body: JSON.stringify({
                error: 'Internal server error',
                message: 'Failed to enhance description',
                requestId,
            }),
        };
    }
};
exports.handler = handler;
async function enhanceDescription(userRequest, requestId) {
    const enhancementPrompt = `
You are an expert software architect and project manager. Your task is to transform a user's high-level description into a comprehensive, detailed specification and task plan for building a production-ready application.

User Request:
${JSON.stringify(userRequest, null, 2)}

Please analyze this request and create:

1. A DETAILED SPECIFICATION that includes:
   - Project name and comprehensive description
   - Complete feature list with priorities
   - Recommended tech stack (frontend, backend, database, infrastructure)
   - System architecture and components
   - Data models with relationships
   - API endpoints specification
   - UI components structure
   - Chatbot flows (if applicable)
   - Deployment configuration

2. A COMPREHENSIVE TASK PLAN that breaks down the work into specific, actionable tasks:
   - Each task should be atomic and completable by a code generation agent
   - Include proper dependencies between tasks
   - Prioritize tasks logically (infrastructure → backend → frontend → integration)
   - Estimate complexity and duration
   - Categorize tasks by type (frontend, backend, chatbot, infrastructure, documentation)

Requirements:
- Make the specification production-ready with proper error handling, security, and scalability considerations
- Include modern best practices and patterns
- Consider CI/CD, testing, monitoring, and documentation
- Ensure the task plan is executable and well-structured
- Include specific file names and code structure in task descriptions

Return your response as a JSON object with 'specification' and 'taskPlan' properties that match the TypeScript interfaces provided.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(enhancementPrompt, {
            temperature: 0.7,
            maxTokens: 4000,
            requestId,
        });
        const result = JSON.parse(response);
        // Validate and enhance the response
        const specification = validateAndEnhanceSpecification(result.specification, userRequest);
        const taskPlan = validateAndEnhanceTaskPlan(result.taskPlan, specification);
        return { specification, taskPlan };
    }
    catch (error) {
        logger_1.logger.error('LLM enhancement failed', { requestId, error });
        throw new Error('Failed to enhance description with LLM');
    }
}
function validateAndEnhanceSpecification(spec, userRequest) {
    // Add validation and enhancement logic
    const enhanced = {
        projectName: spec.projectName || generateProjectName(userRequest.userDescription),
        description: spec.description || userRequest.userDescription,
        features: spec.features || [],
        techStack: spec.techStack || getDefaultTechStack(userRequest),
        architecture: spec.architecture || getDefaultArchitecture(),
        dataModels: spec.dataModels || [],
        apiEndpoints: spec.apiEndpoints || [],
        uiComponents: spec.uiComponents || [],
        chatbotFlows: spec.chatbotFlows,
        deploymentConfig: spec.deploymentConfig || getDefaultDeploymentConfig(),
        metadata: {
            createdAt: new Date().toISOString(),
            version: '1.0.0',
            estimatedComplexity: estimateComplexity(spec),
        },
    };
    return enhanced;
}
function validateAndEnhanceTaskPlan(taskPlan, specification) {
    const tasks = (taskPlan.tasks || []).map((task, index) => ({
        taskId: task.taskId || (0, uuid_1.v4)(),
        taskName: task.taskName || `Task ${index + 1}`,
        description: task.description || '',
        agent: task.agent || 'CodeCreator',
        dependencies: task.dependencies || [],
        status: 'pending',
        priority: task.priority || index + 1,
        estimatedDuration: task.estimatedDuration || 30,
        metadata: {
            category: task.metadata?.category || 'backend',
            complexity: task.metadata?.complexity || 'medium',
            files: task.metadata?.files || [],
        },
    }));
    return {
        tasks,
        totalEstimatedDuration: tasks.reduce((sum, task) => sum + (task.estimatedDuration || 0), 0),
        dependencies: tasks.reduce((deps, task) => {
            deps[task.taskId] = task.dependencies;
            return deps;
        }, {}),
    };
}
function generateProjectName(description) {
    // Simple project name generation logic
    const words = description.toLowerCase().split(' ').slice(0, 3);
    return words.join('-') + '-app';
}
function getDefaultTechStack(userRequest) {
    return {
        frontend: {
            framework: userRequest.preferences?.techStack?.includes('react') ? 'React' : 'Next.js',
            language: 'TypeScript',
            styling: 'Tailwind CSS',
            stateManagement: 'Zustand',
        },
        backend: {
            framework: 'Express.js',
            language: 'Node.js',
            database: 'PostgreSQL',
            authentication: 'JWT',
        },
        infrastructure: {
            cloud: 'AWS',
            deployment: 'Serverless',
            monitoring: 'CloudWatch',
        },
    };
}
function getDefaultArchitecture() {
    return {
        type: 'serverless',
        components: [],
        integrations: [],
    };
}
function getDefaultDeploymentConfig() {
    return {
        environment: 'development',
        environmentVariables: {},
        scaling: {
            minInstances: 1,
            maxInstances: 10,
        },
    };
}
function estimateComplexity(spec) {
    const featureCount = (spec.features || []).length;
    const modelCount = (spec.dataModels || []).length;
    const endpointCount = (spec.apiEndpoints || []).length;
    const totalComplexity = featureCount + modelCount + endpointCount;
    if (totalComplexity < 10)
        return 'low';
    if (totalComplexity < 25)
        return 'medium';
    return 'high';
}
//# sourceMappingURL=data:application/json;base64,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