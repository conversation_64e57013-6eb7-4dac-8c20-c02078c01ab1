"use strict";
/**
 * CloudWatch Dashboard for AI Agent Workflow System
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowDashboard = void 0;
exports.createCustomMetrics = createCustomMetrics;
exports.createAlerts = createAlerts;
const cdk = __importStar(require("aws-cdk-lib"));
const cloudwatch = __importStar(require("aws-cdk-lib/aws-cloudwatch"));
const constructs_1 = require("constructs");
class WorkflowDashboard extends constructs_1.Construct {
    constructor(scope, id, props) {
        super(scope, id);
        this.dashboard = new cloudwatch.Dashboard(this, 'AIAgentWorkflowDashboard', {
            dashboardName: 'AI-Agent-Workflow-System',
            defaultInterval: cdk.Duration.hours(1),
        });
        this.addApiGatewayWidgets(props.api);
        this.addLambdaWidgets(props.lambdaFunctions);
        this.addStepFunctionsWidgets(props.stateMachine);
        this.addDynamoDBWidgets(props.workflowTable);
        this.addSystemOverviewWidgets(props);
    }
    addApiGatewayWidgets(api) {
        // API Gateway metrics
        const apiRequestsWidget = new cloudwatch.GraphWidget({
            title: 'API Gateway Requests',
            left: [
                api.metricCount({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            ],
            right: [
                api.metricLatency({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Average',
                }),
            ],
            width: 12,
            height: 6,
        });
        const apiErrorsWidget = new cloudwatch.GraphWidget({
            title: 'API Gateway Errors',
            left: [
                api.metricClientError({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
                api.metricServerError({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            ],
            width: 12,
            height: 6,
        });
        this.dashboard.addWidgets(apiRequestsWidget, apiErrorsWidget);
    }
    addLambdaWidgets(lambdaFunctions) {
        // Lambda invocations
        const lambdaInvocationsWidget = new cloudwatch.GraphWidget({
            title: 'Lambda Function Invocations',
            left: Object.values(lambdaFunctions).map(func => func.metricInvocations({
                period: cdk.Duration.minutes(5),
                statistic: 'Sum',
            })),
            width: 12,
            height: 6,
        });
        // Lambda errors
        const lambdaErrorsWidget = new cloudwatch.GraphWidget({
            title: 'Lambda Function Errors',
            left: Object.values(lambdaFunctions).map(func => func.metricErrors({
                period: cdk.Duration.minutes(5),
                statistic: 'Sum',
            })),
            width: 12,
            height: 6,
        });
        // Lambda duration
        const lambdaDurationWidget = new cloudwatch.GraphWidget({
            title: 'Lambda Function Duration',
            left: Object.values(lambdaFunctions).map(func => func.metricDuration({
                period: cdk.Duration.minutes(5),
                statistic: 'Average',
            })),
            width: 12,
            height: 6,
        });
        // Lambda throttles
        const lambdaThrottlesWidget = new cloudwatch.GraphWidget({
            title: 'Lambda Function Throttles',
            left: Object.values(lambdaFunctions).map(func => func.metricThrottles({
                period: cdk.Duration.minutes(5),
                statistic: 'Sum',
            })),
            width: 12,
            height: 6,
        });
        this.dashboard.addWidgets(lambdaInvocationsWidget, lambdaErrorsWidget, lambdaDurationWidget, lambdaThrottlesWidget);
    }
    addStepFunctionsWidgets(stateMachine) {
        // Step Functions executions
        const sfnExecutionsWidget = new cloudwatch.GraphWidget({
            title: 'Step Functions Executions',
            left: [
                stateMachine.metricStarted({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
                stateMachine.metricSucceeded({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
                stateMachine.metricFailed({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            ],
            width: 12,
            height: 6,
        });
        // Step Functions duration
        const sfnDurationWidget = new cloudwatch.GraphWidget({
            title: 'Step Functions Execution Duration',
            left: [
                new cloudwatch.Metric({
                    namespace: 'AWS/States',
                    metricName: 'ExecutionTime',
                    dimensionsMap: {
                        StateMachineArn: stateMachine.stateMachineArn,
                    },
                    period: cdk.Duration.minutes(5),
                    statistic: 'Average',
                }),
            ],
            width: 12,
            height: 6,
        });
        this.dashboard.addWidgets(sfnExecutionsWidget, sfnDurationWidget);
    }
    addDynamoDBWidgets(table) {
        // DynamoDB read/write capacity
        const dynamoCapacityWidget = new cloudwatch.GraphWidget({
            title: 'DynamoDB Read/Write Capacity',
            left: [
                table.metricConsumedReadCapacityUnits({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
                table.metricConsumedWriteCapacityUnits({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            ],
            width: 12,
            height: 6,
        });
        // DynamoDB throttles
        const dynamoThrottlesWidget = new cloudwatch.GraphWidget({
            title: 'DynamoDB Throttles',
            left: [
                table.metricThrottledRequests({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            ],
            width: 12,
            height: 6,
        });
        this.dashboard.addWidgets(dynamoCapacityWidget, dynamoThrottlesWidget);
    }
    addSystemOverviewWidgets(props) {
        // System health overview
        const systemHealthWidget = new cloudwatch.SingleValueWidget({
            title: 'System Health Overview',
            metrics: [
                props.api.metricCount({
                    period: cdk.Duration.hours(1),
                    statistic: 'Sum',
                }),
                props.stateMachine.metricSucceeded({
                    period: cdk.Duration.hours(1),
                    statistic: 'Sum',
                }),
            ],
            width: 12,
            height: 6,
        });
        // Error rate overview
        const errorRateWidget = new cloudwatch.GraphWidget({
            title: 'Overall Error Rate',
            left: [
                new cloudwatch.MathExpression({
                    expression: '(api_errors + lambda_errors + sfn_errors) / (api_requests + lambda_invocations + sfn_executions) * 100',
                    usingMetrics: {
                        api_errors: props.api.metricServerError({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                        api_requests: props.api.metricCount({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                        lambda_errors: props.lambdaFunctions.descriptionEnhancer.metricErrors({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                        lambda_invocations: props.lambdaFunctions.descriptionEnhancer.metricInvocations({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                        sfn_errors: props.stateMachine.metricFailed({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                        sfn_executions: props.stateMachine.metricStarted({
                            period: cdk.Duration.minutes(5),
                            statistic: 'Sum',
                        }),
                    },
                    label: 'Error Rate (%)',
                }),
            ],
            width: 12,
            height: 6,
        });
        this.dashboard.addWidgets(systemHealthWidget, errorRateWidget);
    }
}
exports.WorkflowDashboard = WorkflowDashboard;
function createCustomMetrics(scope) {
    const workflowCompletionRate = new cloudwatch.Metric({
        namespace: 'AIAgentWorkflow/Custom',
        metricName: 'WorkflowCompletionRate',
        period: cdk.Duration.minutes(5),
        statistic: 'Average',
    });
    const averageProcessingTime = new cloudwatch.Metric({
        namespace: 'AIAgentWorkflow/Custom',
        metricName: 'AverageProcessingTime',
        period: cdk.Duration.minutes(5),
        statistic: 'Average',
    });
    const codeQualityScore = new cloudwatch.Metric({
        namespace: 'AIAgentWorkflow/Custom',
        metricName: 'CodeQualityScore',
        period: cdk.Duration.minutes(5),
        statistic: 'Average',
    });
    return {
        workflowCompletionRate,
        averageProcessingTime,
        codeQualityScore,
    };
}
function createAlerts(scope, props, notificationTopic) {
    // High error rate alarm
    const highErrorRateAlarm = new cloudwatch.Alarm(scope, 'HighErrorRateAlarm', {
        metric: new cloudwatch.MathExpression({
            expression: '(errors / requests) * 100',
            usingMetrics: {
                errors: props.api.metricServerError({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
                requests: props.api.metricCount({
                    period: cdk.Duration.minutes(5),
                    statistic: 'Sum',
                }),
            },
        }),
        threshold: 5, // 5% error rate
        evaluationPeriods: 2,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });
    // Long execution time alarm
    const longExecutionAlarm = new cloudwatch.Alarm(scope, 'LongExecutionAlarm', {
        metric: new cloudwatch.Metric({
            namespace: 'AWS/States',
            metricName: 'ExecutionTime',
            dimensionsMap: {
                StateMachineArn: props.stateMachine.stateMachineArn,
            },
            period: cdk.Duration.minutes(5),
            statistic: 'Average',
        }),
        threshold: 3600000, // 1 hour in milliseconds
        evaluationPeriods: 1,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });
    // DynamoDB throttle alarm
    const dynamoThrottleAlarm = new cloudwatch.Alarm(scope, 'DynamoThrottleAlarm', {
        metric: props.workflowTable.metricThrottledRequests({
            period: cdk.Duration.minutes(5),
            statistic: 'Sum',
        }),
        threshold: 1,
        evaluationPeriods: 1,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });
}
//# sourceMappingURL=data:application/json;base64,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