#!/usr/bin/env node
/**
 * CDK App entry point for AI Agent Workflow System
 */

import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { AIAgentWorkflowStack } from './ai-agent-workflow-stack';

const app = new cdk.App();

// Get environment configuration
const account = process.env.CDK_DEFAULT_ACCOUNT || process.env.AWS_ACCOUNT_ID;
const region = process.env.CDK_DEFAULT_REGION || process.env.AWS_REGION || 'us-east-1';

// Environment configuration
const env = {
  account,
  region,
};

// Stack configuration
const stackProps: cdk.StackProps = {
  env,
  description: 'AI Agent Workflow System - Serverless multi-agent workflow for creating websites, backends, and chatbots',
  tags: {
    Project: 'AIAgentWorkflow',
    Environment: process.env.ENVIRONMENT || 'development',
    Owner: 'AIAgentWorkflowSystem',
    CostCenter: 'Development',
  },
};

// Create the main stack
new AIAgentWorkflowStack(app, 'AIAgentWorkflowStack', stackProps);

// Add stack-level tags
cdk.Tags.of(app).add('Project', 'AIAgentWorkflow');
cdk.Tags.of(app).add('ManagedBy', 'CDK');
cdk.Tags.of(app).add('Repository', 'ai-agent-workflow-serverless');

app.synth();
