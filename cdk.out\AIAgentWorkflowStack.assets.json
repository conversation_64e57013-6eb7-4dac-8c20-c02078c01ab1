{"version": "44.0.0", "files": {"faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6": {"displayName": "AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider Code", "source": {"path": "asset.faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6", "packaging": "zip"}, "destinations": {"337909760884-us-east-1": {"bucketName": "cdk-hnb659fds-assets-337909760884-us-east-1", "objectKey": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip", "region": "us-east-1", "assumeRoleArn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-file-publishing-role-337909760884-us-east-1"}}}, "2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d": {"displayName": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Code", "source": {"path": "asset.2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d", "packaging": "zip"}, "destinations": {"337909760884-us-east-1": {"bucketName": "cdk-hnb659fds-assets-337909760884-us-east-1", "objectKey": "2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d.zip", "region": "us-east-1", "assumeRoleArn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-file-publishing-role-337909760884-us-east-1"}}}, "d0add1cf45436fe9279757e700f6f5eccbf1deeee2e584ad9ba9abe6e87c0cbb": {"displayName": "AIAgentWorkflowStack Template", "source": {"path": "AIAgentWorkflowStack.template.json", "packaging": "file"}, "destinations": {"337909760884-us-east-1": {"bucketName": "cdk-hnb659fds-assets-337909760884-us-east-1", "objectKey": "d0add1cf45436fe9279757e700f6f5eccbf1deeee2e584ad9ba9abe6e87c0cbb.json", "region": "us-east-1", "assumeRoleArn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-file-publishing-role-337909760884-us-east-1"}}}}, "dockerImages": {}}