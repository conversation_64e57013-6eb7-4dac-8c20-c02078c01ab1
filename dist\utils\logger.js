"use strict";
/**
 * Centralized logging utility
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
exports.createLogger = createLogger;
exports.logExecutionTime = logExecutionTime;
exports.logWithMetrics = logWithMetrics;
exports.createRequestLogger = createRequestLogger;
class Logger {
    constructor(serviceName = 'ai-agent-workflow') {
        this.serviceName = serviceName;
    }
    formatMessage(level, message, context) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            service: this.serviceName,
            message,
            ...context,
        };
        return JSON.stringify(logEntry);
    }
    info(message, context) {
        console.log(this.formatMessage('INFO', message, context));
    }
    warn(message, context) {
        console.warn(this.formatMessage('WARN', message, context));
    }
    error(message, context) {
        console.error(this.formatMessage('ERROR', message, context));
    }
    debug(message, context) {
        if (process.env.LOG_LEVEL === 'debug') {
            console.debug(this.formatMessage('DEBUG', message, context));
        }
    }
    trace(message, context) {
        if (process.env.LOG_LEVEL === 'trace') {
            console.trace(this.formatMessage('TRACE', message, context));
        }
    }
}
exports.Logger = Logger;
exports.logger = new Logger();
function createLogger(serviceName) {
    return new Logger(serviceName);
}
function logExecutionTime(operation, fn, context) {
    return new Promise(async (resolve, reject) => {
        const startTime = Date.now();
        exports.logger.info(`Starting ${operation}`, context);
        try {
            const result = await fn();
            const duration = Date.now() - startTime;
            exports.logger.info(`Completed ${operation}`, { ...context, duration });
            resolve(result);
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);
            exports.logger.error(`Failed ${operation}`, { ...context, duration, error: errorMessage });
            reject(error);
        }
    });
}
function logWithMetrics(message, metrics, context) {
    exports.logger.info(message, { ...context, metrics });
}
function createRequestLogger(requestId) {
    return {
        info: (message, context) => exports.logger.info(message, { ...context, requestId }),
        warn: (message, context) => exports.logger.warn(message, { ...context, requestId }),
        error: (message, context) => exports.logger.error(message, { ...context, requestId }),
        debug: (message, context) => exports.logger.debug(message, { ...context, requestId }),
    };
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9nZ2VyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vc3JjL3V0aWxzL2xvZ2dlci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7O0dBRUc7OztBQXlESCxvQ0FFQztBQUVELDRDQXFCQztBQUVELHdDQU1DO0FBRUQsa0RBV0M7QUE3RkQsTUFBYSxNQUFNO0lBR2pCLFlBQVksY0FBc0IsbUJBQW1CO1FBQ25ELElBQUksQ0FBQyxXQUFXLEdBQUcsV0FBVyxDQUFDO0lBQ2pDLENBQUM7SUFFTyxhQUFhLENBQUMsS0FBYSxFQUFFLE9BQWUsRUFBRSxPQUFvQjtRQUN4RSxNQUFNLFNBQVMsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQzNDLE1BQU0sUUFBUSxHQUFHO1lBQ2YsU0FBUztZQUNULEtBQUs7WUFDTCxPQUFPLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDekIsT0FBTztZQUNQLEdBQUcsT0FBTztTQUNYLENBQUM7UUFFRixPQUFPLElBQUksQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDbEMsQ0FBQztJQUVELElBQUksQ0FBQyxPQUFlLEVBQUUsT0FBb0I7UUFDeEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUMsQ0FBQztJQUM1RCxDQUFDO0lBRUQsSUFBSSxDQUFDLE9BQWUsRUFBRSxPQUFvQjtRQUN4QyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQzdELENBQUM7SUFFRCxLQUFLLENBQUMsT0FBZSxFQUFFLE9BQW9CO1FBQ3pDLE9BQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUM7SUFDL0QsQ0FBQztJQUVELEtBQUssQ0FBQyxPQUFlLEVBQUUsT0FBb0I7UUFDekMsSUFBSSxPQUFPLENBQUMsR0FBRyxDQUFDLFNBQVMsS0FBSyxPQUFPLEVBQUUsQ0FBQztZQUN0QyxPQUFPLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsT0FBTyxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQy9ELENBQUM7SUFDSCxDQUFDO0lBRUQsS0FBSyxDQUFDLE9BQWUsRUFBRSxPQUFvQjtRQUN6QyxJQUFJLE9BQU8sQ0FBQyxHQUFHLENBQUMsU0FBUyxLQUFLLE9BQU8sRUFBRSxDQUFDO1lBQ3RDLE9BQU8sQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUUsT0FBTyxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFDL0QsQ0FBQztJQUNILENBQUM7Q0FDRjtBQTNDRCx3QkEyQ0M7QUFFWSxRQUFBLE1BQU0sR0FBRyxJQUFJLE1BQU0sRUFBRSxDQUFDO0FBRW5DLFNBQWdCLFlBQVksQ0FBQyxXQUFtQjtJQUM5QyxPQUFPLElBQUksTUFBTSxDQUFDLFdBQVcsQ0FBQyxDQUFDO0FBQ2pDLENBQUM7QUFFRCxTQUFnQixnQkFBZ0IsQ0FDOUIsU0FBaUIsRUFDakIsRUFBb0IsRUFDcEIsT0FBb0I7SUFFcEIsT0FBTyxJQUFJLE9BQU8sQ0FBQyxLQUFLLEVBQUUsT0FBTyxFQUFFLE1BQU0sRUFBRSxFQUFFO1FBQzNDLE1BQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUM3QixjQUFNLENBQUMsSUFBSSxDQUFDLFlBQVksU0FBUyxFQUFFLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFFOUMsSUFBSSxDQUFDO1lBQ0gsTUFBTSxNQUFNLEdBQUcsTUFBTSxFQUFFLEVBQUUsQ0FBQztZQUMxQixNQUFNLFFBQVEsR0FBRyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsU0FBUyxDQUFDO1lBQ3hDLGNBQU0sQ0FBQyxJQUFJLENBQUMsYUFBYSxTQUFTLEVBQUUsRUFBRSxFQUFFLEdBQUcsT0FBTyxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUM7WUFDaEUsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBQ2xCLENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLFNBQVMsQ0FBQztZQUN4QyxNQUFNLFlBQVksR0FBRyxLQUFLLFlBQVksS0FBSyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7WUFDNUUsY0FBTSxDQUFDLEtBQUssQ0FBQyxVQUFVLFNBQVMsRUFBRSxFQUFFLEVBQUUsR0FBRyxPQUFPLEVBQUUsUUFBUSxFQUFFLEtBQUssRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDO1lBQ25GLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoQixDQUFDO0lBQ0gsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDO0FBRUQsU0FBZ0IsY0FBYyxDQUM1QixPQUFlLEVBQ2YsT0FBK0IsRUFDL0IsT0FBb0I7SUFFcEIsY0FBTSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE9BQU8sRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDO0FBQ2hELENBQUM7QUFFRCxTQUFnQixtQkFBbUIsQ0FBQyxTQUFpQjtJQUNuRCxPQUFPO1FBQ0wsSUFBSSxFQUFFLENBQUMsT0FBZSxFQUFFLE9BQW9CLEVBQUUsRUFBRSxDQUM5QyxjQUFNLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxFQUFFLEdBQUcsT0FBTyxFQUFFLFNBQVMsRUFBRSxDQUFDO1FBQ2pELElBQUksRUFBRSxDQUFDLE9BQWUsRUFBRSxPQUFvQixFQUFFLEVBQUUsQ0FDOUMsY0FBTSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBRSxHQUFHLE9BQU8sRUFBRSxTQUFTLEVBQUUsQ0FBQztRQUNqRCxLQUFLLEVBQUUsQ0FBQyxPQUFlLEVBQUUsT0FBb0IsRUFBRSxFQUFFLENBQy9DLGNBQU0sQ0FBQyxLQUFLLENBQUMsT0FBTyxFQUFFLEVBQUUsR0FBRyxPQUFPLEVBQUUsU0FBUyxFQUFFLENBQUM7UUFDbEQsS0FBSyxFQUFFLENBQUMsT0FBZSxFQUFFLE9BQW9CLEVBQUUsRUFBRSxDQUMvQyxjQUFNLENBQUMsS0FBSyxDQUFDLE9BQU8sRUFBRSxFQUFFLEdBQUcsT0FBTyxFQUFFLFNBQVMsRUFBRSxDQUFDO0tBQ25ELENBQUM7QUFDSixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDZW50cmFsaXplZCBsb2dnaW5nIHV0aWxpdHlcbiAqL1xuXG5leHBvcnQgaW50ZXJmYWNlIExvZ0NvbnRleHQge1xuICByZXF1ZXN0SWQ/OiBzdHJpbmc7XG4gIHNlc3Npb25JZD86IHN0cmluZztcbiAgdGFza0lkPzogc3RyaW5nO1xuICBhZ2VudD86IHN0cmluZztcbiAgW2tleTogc3RyaW5nXTogYW55O1xufVxuXG5leHBvcnQgY2xhc3MgTG9nZ2VyIHtcbiAgcHJpdmF0ZSBzZXJ2aWNlTmFtZTogc3RyaW5nO1xuXG4gIGNvbnN0cnVjdG9yKHNlcnZpY2VOYW1lOiBzdHJpbmcgPSAnYWktYWdlbnQtd29ya2Zsb3cnKSB7XG4gICAgdGhpcy5zZXJ2aWNlTmFtZSA9IHNlcnZpY2VOYW1lO1xuICB9XG5cbiAgcHJpdmF0ZSBmb3JtYXRNZXNzYWdlKGxldmVsOiBzdHJpbmcsIG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IExvZ0NvbnRleHQpOiBzdHJpbmcge1xuICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcbiAgICBjb25zdCBsb2dFbnRyeSA9IHtcbiAgICAgIHRpbWVzdGFtcCxcbiAgICAgIGxldmVsLFxuICAgICAgc2VydmljZTogdGhpcy5zZXJ2aWNlTmFtZSxcbiAgICAgIG1lc3NhZ2UsXG4gICAgICAuLi5jb250ZXh0LFxuICAgIH07XG5cbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkobG9nRW50cnkpO1xuICB9XG5cbiAgaW5mbyhtZXNzYWdlOiBzdHJpbmcsIGNvbnRleHQ/OiBMb2dDb250ZXh0KTogdm9pZCB7XG4gICAgY29uc29sZS5sb2codGhpcy5mb3JtYXRNZXNzYWdlKCdJTkZPJywgbWVzc2FnZSwgY29udGV4dCkpO1xuICB9XG5cbiAgd2FybihtZXNzYWdlOiBzdHJpbmcsIGNvbnRleHQ/OiBMb2dDb250ZXh0KTogdm9pZCB7XG4gICAgY29uc29sZS53YXJuKHRoaXMuZm9ybWF0TWVzc2FnZSgnV0FSTicsIG1lc3NhZ2UsIGNvbnRleHQpKTtcbiAgfVxuXG4gIGVycm9yKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IExvZ0NvbnRleHQpOiB2b2lkIHtcbiAgICBjb25zb2xlLmVycm9yKHRoaXMuZm9ybWF0TWVzc2FnZSgnRVJST1InLCBtZXNzYWdlLCBjb250ZXh0KSk7XG4gIH1cblxuICBkZWJ1ZyhtZXNzYWdlOiBzdHJpbmcsIGNvbnRleHQ/OiBMb2dDb250ZXh0KTogdm9pZCB7XG4gICAgaWYgKHByb2Nlc3MuZW52LkxPR19MRVZFTCA9PT0gJ2RlYnVnJykge1xuICAgICAgY29uc29sZS5kZWJ1Zyh0aGlzLmZvcm1hdE1lc3NhZ2UoJ0RFQlVHJywgbWVzc2FnZSwgY29udGV4dCkpO1xuICAgIH1cbiAgfVxuXG4gIHRyYWNlKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IExvZ0NvbnRleHQpOiB2b2lkIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTE9HX0xFVkVMID09PSAndHJhY2UnKSB7XG4gICAgICBjb25zb2xlLnRyYWNlKHRoaXMuZm9ybWF0TWVzc2FnZSgnVFJBQ0UnLCBtZXNzYWdlLCBjb250ZXh0KSk7XG4gICAgfVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBsb2dnZXIgPSBuZXcgTG9nZ2VyKCk7XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVMb2dnZXIoc2VydmljZU5hbWU6IHN0cmluZyk6IExvZ2dlciB7XG4gIHJldHVybiBuZXcgTG9nZ2VyKHNlcnZpY2VOYW1lKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGxvZ0V4ZWN1dGlvblRpbWU8VD4oXG4gIG9wZXJhdGlvbjogc3RyaW5nLFxuICBmbjogKCkgPT4gUHJvbWlzZTxUPixcbiAgY29udGV4dD86IExvZ0NvbnRleHRcbik6IFByb21pc2U8VD4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoYXN5bmMgKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgbG9nZ2VyLmluZm8oYFN0YXJ0aW5nICR7b3BlcmF0aW9ufWAsIGNvbnRleHQpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGZuKCk7XG4gICAgICBjb25zdCBkdXJhdGlvbiA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICBsb2dnZXIuaW5mbyhgQ29tcGxldGVkICR7b3BlcmF0aW9ufWAsIHsgLi4uY29udGV4dCwgZHVyYXRpb24gfSk7XG4gICAgICByZXNvbHZlKHJlc3VsdCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGR1cmF0aW9uID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogU3RyaW5nKGVycm9yKTtcbiAgICAgIGxvZ2dlci5lcnJvcihgRmFpbGVkICR7b3BlcmF0aW9ufWAsIHsgLi4uY29udGV4dCwgZHVyYXRpb24sIGVycm9yOiBlcnJvck1lc3NhZ2UgfSk7XG4gICAgICByZWplY3QoZXJyb3IpO1xuICAgIH1cbiAgfSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBsb2dXaXRoTWV0cmljcyhcbiAgbWVzc2FnZTogc3RyaW5nLFxuICBtZXRyaWNzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+LFxuICBjb250ZXh0PzogTG9nQ29udGV4dFxuKTogdm9pZCB7XG4gIGxvZ2dlci5pbmZvKG1lc3NhZ2UsIHsgLi4uY29udGV4dCwgbWV0cmljcyB9KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVJlcXVlc3RMb2dnZXIocmVxdWVzdElkOiBzdHJpbmcpIHtcbiAgcmV0dXJuIHtcbiAgICBpbmZvOiAobWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogTG9nQ29udGV4dCkgPT4gXG4gICAgICBsb2dnZXIuaW5mbyhtZXNzYWdlLCB7IC4uLmNvbnRleHQsIHJlcXVlc3RJZCB9KSxcbiAgICB3YXJuOiAobWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogTG9nQ29udGV4dCkgPT4gXG4gICAgICBsb2dnZXIud2FybihtZXNzYWdlLCB7IC4uLmNvbnRleHQsIHJlcXVlc3RJZCB9KSxcbiAgICBlcnJvcjogKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IExvZ0NvbnRleHQpID0+IFxuICAgICAgbG9nZ2VyLmVycm9yKG1lc3NhZ2UsIHsgLi4uY29udGV4dCwgcmVxdWVzdElkIH0pLFxuICAgIGRlYnVnOiAobWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogTG9nQ29udGV4dCkgPT4gXG4gICAgICBsb2dnZXIuZGVidWcobWVzc2FnZSwgeyAuLi5jb250ZXh0LCByZXF1ZXN0SWQgfSksXG4gIH07XG59XG4iXX0=