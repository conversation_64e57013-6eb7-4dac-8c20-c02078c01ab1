"use strict";
/**
 * Code validation utilities for static analysis
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateGeneratedCode = validateGeneratedCode;
const validation_1 = require("./validation");
async function validateGeneratedCode(files) {
    const errors = [];
    const warnings = [];
    let totalLinesOfCode = 0;
    let totalComplexity = 0;
    for (const file of files) {
        // Basic file validation
        const fileValidation = (0, validation_1.validateGeneratedFile)(file);
        // Convert validation errors to CodeError format
        fileValidation.errors.forEach(error => {
            errors.push({
                severity: 'major',
                message: error,
                file: file.path,
                rule: 'file_validation',
            });
        });
        fileValidation.warnings.forEach(warning => {
            warnings.push({
                message: warning,
                file: file.path,
                suggestion: 'Review file structure and naming conventions',
            });
        });
        // Syntax validation
        const syntaxValidation = (0, validation_1.validateCodeSyntax)(file);
        syntaxValidation.errors.forEach(error => {
            errors.push({
                severity: 'critical',
                message: error,
                file: file.path,
                rule: 'syntax_error',
            });
        });
        syntaxValidation.warnings.forEach(warning => {
            warnings.push({
                message: warning,
                file: file.path,
                suggestion: 'Review code syntax and formatting',
            });
        });
        // Calculate metrics
        const fileMetrics = calculateFileMetrics(file);
        totalLinesOfCode += fileMetrics.linesOfCode;
        totalComplexity += fileMetrics.complexity;
        // Language-specific validation
        const languageValidation = validateLanguageSpecific(file);
        errors.push(...languageValidation.errors);
        warnings.push(...languageValidation.warnings);
    }
    const maintainabilityIndex = calculateMaintainabilityIndex(totalLinesOfCode, totalComplexity);
    return {
        errors,
        warnings,
        metrics: {
            linesOfCode: totalLinesOfCode,
            complexity: totalComplexity,
            maintainabilityIndex,
        },
    };
}
function calculateFileMetrics(file) {
    const lines = file.content.split('\n');
    const linesOfCode = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('/*');
    }).length;
    let complexity = 1; // Base complexity
    // Calculate cyclomatic complexity
    const complexityPatterns = [
        /\bif\b/g,
        /\belse\b/g,
        /\bwhile\b/g,
        /\bfor\b/g,
        /\bswitch\b/g,
        /\bcase\b/g,
        /\bcatch\b/g,
        /\b&&\b/g,
        /\b\|\|\b/g,
        /\?\s*:/g, // Ternary operator
    ];
    for (const pattern of complexityPatterns) {
        const matches = file.content.match(pattern);
        if (matches) {
            complexity += matches.length;
        }
    }
    return { linesOfCode, complexity };
}
function calculateMaintainabilityIndex(linesOfCode, complexity) {
    // Simplified maintainability index calculation
    // Real formula is more complex, but this gives a good approximation
    const halsteadVolume = Math.log2(linesOfCode) * 10; // Simplified
    const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode));
    return Math.round(maintainabilityIndex);
}
function validateLanguageSpecific(file) {
    const errors = [];
    const warnings = [];
    switch (file.language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            validateJavaScript(file, errors, warnings);
            break;
        case 'python':
            validatePython(file, errors, warnings);
            break;
        case 'html':
            validateHTML(file, errors, warnings);
            break;
        case 'css':
            validateCSS(file, errors, warnings);
            break;
    }
    return { errors, warnings };
}
function validateJavaScript(file, errors, warnings) {
    const content = file.content;
    const lines = content.split('\n');
    // Check for common issues
    lines.forEach((line, index) => {
        const lineNumber = index + 1;
        const trimmed = line.trim();
        // Check for var usage (prefer let/const)
        if (trimmed.includes('var ')) {
            warnings.push({
                message: 'Use let or const instead of var',
                file: file.path,
                line: lineNumber,
                suggestion: 'Replace var with let or const for better scoping',
            });
        }
        // Check for == usage (prefer ===)
        if (trimmed.includes('==') && !trimmed.includes('===')) {
            warnings.push({
                message: 'Use strict equality (===) instead of loose equality (==)',
                file: file.path,
                line: lineNumber,
                suggestion: 'Replace == with === for type-safe comparisons',
            });
        }
        // Check for missing semicolons
        if (trimmed.length > 0 &&
            !trimmed.endsWith(';') &&
            !trimmed.endsWith('{') &&
            !trimmed.endsWith('}') &&
            !trimmed.startsWith('//') &&
            !trimmed.startsWith('/*')) {
            warnings.push({
                message: 'Missing semicolon',
                file: file.path,
                line: lineNumber,
                suggestion: 'Add semicolon at the end of the statement',
            });
        }
        // Check for console.log in production code
        if (trimmed.includes('console.log') && !content.includes('// debug')) {
            warnings.push({
                message: 'Console.log statement found',
                file: file.path,
                line: lineNumber,
                suggestion: 'Remove console.log statements in production code',
            });
        }
    });
    // Check for missing error handling
    if (content.includes('async ') && !content.includes('try') && !content.includes('catch')) {
        warnings.push({
            message: 'Async function without error handling',
            file: file.path,
            suggestion: 'Add try-catch blocks for async operations',
        });
    }
    // Check for hardcoded values
    const hardcodedPatterns = [
        /['"]http:\/\/localhost:\d+['"]/g,
        /['"]127\.0\.0\.1['"]/g,
        /['"]password['"]/g,
        /['"]secret['"]/g,
    ];
    hardcodedPatterns.forEach(pattern => {
        if (pattern.test(content)) {
            warnings.push({
                message: 'Hardcoded value detected',
                file: file.path,
                suggestion: 'Use environment variables for configuration values',
            });
        }
    });
}
function validatePython(file, errors, warnings) {
    const content = file.content;
    const lines = content.split('\n');
    lines.forEach((line, index) => {
        const lineNumber = index + 1;
        const trimmed = line.trim();
        // Check for print statements (prefer logging)
        if (trimmed.includes('print(') && !content.includes('# debug')) {
            warnings.push({
                message: 'Print statement found',
                file: file.path,
                line: lineNumber,
                suggestion: 'Use logging instead of print statements',
            });
        }
        // Check for bare except clauses
        if (trimmed === 'except:') {
            errors.push({
                severity: 'major',
                message: 'Bare except clause',
                file: file.path,
                line: lineNumber,
                rule: 'bare_except',
            });
        }
    });
}
function validateHTML(file, errors, warnings) {
    const content = file.content;
    // Check for missing alt attributes on images
    const imgTags = content.match(/<img[^>]*>/g) || [];
    imgTags.forEach(tag => {
        if (!tag.includes('alt=')) {
            warnings.push({
                message: 'Image missing alt attribute',
                file: file.path,
                suggestion: 'Add alt attribute for accessibility',
            });
        }
    });
    // Check for inline styles
    if (content.includes('style=')) {
        warnings.push({
            message: 'Inline styles detected',
            file: file.path,
            suggestion: 'Use external CSS files instead of inline styles',
        });
    }
}
function validateCSS(file, errors, warnings) {
    const content = file.content;
    // Check for !important usage
    const importantCount = (content.match(/!important/g) || []).length;
    if (importantCount > 3) {
        warnings.push({
            message: 'Excessive use of !important',
            file: file.path,
            suggestion: 'Refactor CSS to avoid overuse of !important',
        });
    }
    // Check for vendor prefixes without standard property
    const vendorPrefixes = ['-webkit-', '-moz-', '-ms-', '-o-'];
    vendorPrefixes.forEach(prefix => {
        const prefixedProperties = content.match(new RegExp(`${prefix}[a-z-]+:`, 'g')) || [];
        prefixedProperties.forEach(prop => {
            const standardProp = prop.replace(prefix, '');
            if (!content.includes(standardProp)) {
                warnings.push({
                    message: `Missing standard property for ${prop}`,
                    file: file.path,
                    suggestion: `Add standard property ${standardProp} after vendor-prefixed version`,
                });
            }
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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