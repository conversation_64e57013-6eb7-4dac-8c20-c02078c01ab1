/**
 * Validation utilities for user requests and generated content
 */

import { UserRequest, GeneratedFile, CodeError } from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export function validateUserRequest(userRequest: UserRequest): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!userRequest.userDescription) {
    errors.push('User description is required');
  }

  if (userRequest.userDescription && userRequest.userDescription.length < 10) {
    errors.push('User description must be at least 10 characters long');
  }

  if (userRequest.userDescription && userRequest.userDescription.length > 5000) {
    errors.push('User description must be less than 5000 characters');
  }

  // Validate preferences if provided
  if (userRequest.preferences) {
    if (userRequest.preferences.techStack) {
      const validTechStacks = ['react', 'vue', 'angular', 'svelte', 'next.js', 'nuxt.js', 'express', 'fastify', 'nest.js'];
      const invalidTechStacks = userRequest.preferences.techStack.filter(
        tech => !validTechStacks.includes(tech.toLowerCase())
      );
      
      if (invalidTechStacks.length > 0) {
        warnings.push(`Unknown tech stack preferences: ${invalidTechStacks.join(', ')}`);
      }
    }

    if (userRequest.preferences.deploymentTarget) {
      const validTargets = ['aws', 'vercel', 'netlify', 'heroku', 'digitalocean'];
      if (!validTargets.includes(userRequest.preferences.deploymentTarget)) {
        warnings.push(`Unknown deployment target: ${userRequest.preferences.deploymentTarget}`);
      }
    }
  }

  // Check for potentially harmful content
  const harmfulPatterns = [
    /\b(hack|exploit|vulnerability|malware|virus)\b/gi,
    /\b(illegal|piracy|copyright\s+infringement)\b/gi,
    /\b(adult|porn|gambling)\b/gi,
  ];

  for (const pattern of harmfulPatterns) {
    if (pattern.test(userRequest.userDescription)) {
      warnings.push('Description contains potentially inappropriate content');
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

export function validateGeneratedFile(file: GeneratedFile): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!file.path) {
    errors.push('File path is required');
  }

  if (!file.content) {
    errors.push('File content is required');
  }

  if (!file.type) {
    errors.push('File type is required');
  }

  if (!file.language) {
    errors.push('File language is required');
  }

  // Validate file path
  if (file.path) {
    if (file.path.includes('..')) {
      errors.push('File path cannot contain parent directory references (..)');
    }

    if (file.path.startsWith('/')) {
      errors.push('File path cannot be absolute');
    }

    if (!/^[a-zA-Z0-9._/-]+$/.test(file.path)) {
      errors.push('File path contains invalid characters');
    }
  }

  // Validate file type
  const validTypes = ['source', 'config', 'documentation', 'test'];
  if (file.type && !validTypes.includes(file.type)) {
    errors.push(`Invalid file type: ${file.type}`);
  }

  // Validate file extension matches language
  if (file.path && file.language) {
    const extension = file.path.split('.').pop()?.toLowerCase();
    const expectedExtensions: Record<string, string[]> = {
      'typescript': ['ts', 'tsx'],
      'javascript': ['js', 'jsx'],
      'python': ['py'],
      'html': ['html', 'htm'],
      'css': ['css'],
      'json': ['json'],
      'yaml': ['yaml', 'yml'],
      'markdown': ['md'],
      'sql': ['sql'],
    };

    const expected = expectedExtensions[file.language.toLowerCase()];
    if (expected && extension && !expected.includes(extension)) {
      warnings.push(`File extension '${extension}' doesn't match language '${file.language}'`);
    }
  }

  // Check for potentially harmful content in code
  if (file.content) {
    const harmfulPatterns = [
      /eval\s*\(/gi,
      /exec\s*\(/gi,
      /system\s*\(/gi,
      /shell_exec\s*\(/gi,
      /\$\{.*\}/g, // Template injection
      /document\.write\s*\(/gi,
      /innerHTML\s*=/gi,
    ];

    for (const pattern of harmfulPatterns) {
      if (pattern.test(file.content)) {
        warnings.push('File content contains potentially unsafe code patterns');
        break;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

export function validateCodeSyntax(file: GeneratedFile): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!file.content) {
    return { isValid: false, errors: ['No content to validate'], warnings: [] };
  }

  try {
    switch (file.language.toLowerCase()) {
      case 'json':
        JSON.parse(file.content);
        break;
      
      case 'javascript':
      case 'typescript':
        validateJavaScriptSyntax(file.content, errors, warnings);
        break;
      
      case 'html':
        validateHTMLSyntax(file.content, errors, warnings);
        break;
      
      case 'css':
        validateCSSSyntax(file.content, errors, warnings);
        break;
      
      default:
        // Basic validation for other languages
        validateBasicSyntax(file.content, errors, warnings);
    }
  } catch (error) {
    errors.push(`Syntax error: ${error.message}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

function validateJavaScriptSyntax(content: string, errors: string[], warnings: string[]): void {
  // Basic JavaScript/TypeScript validation
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const lineNumber = i + 1;
    
    // Check for common syntax errors
    if (line.includes('function') && !line.includes('(')) {
      errors.push(`Line ${lineNumber}: Function declaration missing parentheses`);
    }
    
    if (line.includes('{') && !line.includes('}') && !lines.slice(i + 1).some(l => l.includes('}'))) {
      warnings.push(`Line ${lineNumber}: Unclosed brace detected`);
    }
    
    // Check for console.log in production code
    if (line.includes('console.log') && !content.includes('// debug')) {
      warnings.push(`Line ${lineNumber}: Console.log statement found (consider removing for production)`);
    }
  }
}

function validateHTMLSyntax(content: string, errors: string[], warnings: string[]): void {
  // Basic HTML validation
  const openTags = content.match(/<[^/][^>]*>/g) || [];
  const closeTags = content.match(/<\/[^>]*>/g) || [];
  
  if (openTags.length !== closeTags.length) {
    warnings.push('Mismatched HTML tags detected');
  }
  
  // Check for required HTML structure
  if (!content.includes('<!DOCTYPE') && !content.includes('<html')) {
    warnings.push('Missing DOCTYPE or html tag');
  }
}

function validateCSSSyntax(content: string, errors: string[], warnings: string[]): void {
  // Basic CSS validation
  const braceCount = (content.match(/\{/g) || []).length - (content.match(/\}/g) || []).length;
  
  if (braceCount !== 0) {
    errors.push('Mismatched CSS braces');
  }
  
  // Check for common CSS issues
  if (content.includes('!important')) {
    warnings.push('Use of !important detected (consider refactoring)');
  }
}

function validateBasicSyntax(content: string, errors: string[], warnings: string[]): void {
  // Basic validation for any code
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNumber = i + 1;
    
    // Check for extremely long lines
    if (line.length > 200) {
      warnings.push(`Line ${lineNumber}: Line is very long (${line.length} characters)`);
    }
    
    // Check for mixed indentation
    if (line.match(/^\t+ +/) || line.match(/^ +\t/)) {
      warnings.push(`Line ${lineNumber}: Mixed tabs and spaces for indentation`);
    }
  }
}

export function validateProjectStructure(files: GeneratedFile[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const filePaths = files.map(f => f.path);
  
  // Check for required files based on project type
  const hasPackageJson = filePaths.some(path => path.endsWith('package.json'));
  const hasReactFiles = files.some(f => f.content.includes('React') || f.path.endsWith('.jsx') || f.path.endsWith('.tsx'));
  
  if (hasReactFiles && !hasPackageJson) {
    warnings.push('React project detected but no package.json found');
  }
  
  // Check for duplicate file paths
  const duplicates = filePaths.filter((path, index) => filePaths.indexOf(path) !== index);
  if (duplicates.length > 0) {
    errors.push(`Duplicate file paths detected: ${duplicates.join(', ')}`);
  }
  
  // Check for proper directory structure
  const hasSourceDir = filePaths.some(path => path.startsWith('src/'));
  const hasConfigFiles = filePaths.some(path => 
    path.endsWith('.config.js') || 
    path.endsWith('.config.ts') || 
    path.endsWith('tsconfig.json')
  );
  
  if (files.length > 5 && !hasSourceDir) {
    warnings.push('Consider organizing files in a src/ directory');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
