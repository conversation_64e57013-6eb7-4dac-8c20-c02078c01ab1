/**
 * Core types and interfaces for the AI Agent Workflow System
 */
export interface UserRequest {
    userDescription: string;
    userId?: string;
    sessionId?: string;
    preferences?: {
        techStack?: string[];
        deploymentTarget?: 'aws' | 'vercel' | 'netlify';
        includeTests?: boolean;
        includeCICD?: boolean;
    };
}
export interface DetailedSpecification {
    projectName: string;
    description: string;
    features: Feature[];
    techStack: TechStack;
    architecture: Architecture;
    dataModels: DataModel[];
    apiEndpoints: APIEndpoint[];
    uiComponents: UIComponent[];
    chatbotFlows?: ChatbotFlow[];
    deploymentConfig: DeploymentConfig;
    metadata: {
        createdAt: string;
        version: string;
        estimatedComplexity: 'low' | 'medium' | 'high';
    };
}
export interface Feature {
    id: string;
    name: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    category: 'frontend' | 'backend' | 'chatbot' | 'infrastructure';
    dependencies: string[];
}
export interface TechStack {
    frontend: {
        framework: string;
        language: string;
        styling: string;
        stateManagement?: string;
    };
    backend: {
        framework: string;
        language: string;
        database: string;
        authentication?: string;
    };
    infrastructure: {
        cloud: string;
        deployment: string;
        monitoring: string;
    };
    chatbot?: {
        platform: string;
        nlp: string;
    };
}
export interface Architecture {
    type: 'monolith' | 'microservices' | 'serverless';
    components: Component[];
    integrations: Integration[];
}
export interface Component {
    id: string;
    name: string;
    type: 'frontend' | 'backend' | 'database' | 'api' | 'chatbot';
    description: string;
    dependencies: string[];
}
export interface Integration {
    id: string;
    source: string;
    target: string;
    type: 'api' | 'database' | 'event' | 'webhook';
    description: string;
}
export interface DataModel {
    name: string;
    fields: Field[];
    relationships: Relationship[];
}
export interface Field {
    name: string;
    type: string;
    required: boolean;
    validation?: string;
}
export interface Relationship {
    type: 'oneToOne' | 'oneToMany' | 'manyToMany';
    target: string;
    foreignKey?: string;
}
export interface APIEndpoint {
    path: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    description: string;
    requestSchema?: any;
    responseSchema?: any;
    authentication: boolean;
}
export interface UIComponent {
    name: string;
    type: 'page' | 'component' | 'layout';
    description: string;
    props?: any;
    children?: string[];
}
export interface ChatbotFlow {
    id: string;
    name: string;
    trigger: string;
    steps: ChatbotStep[];
}
export interface ChatbotStep {
    id: string;
    type: 'message' | 'input' | 'condition' | 'action';
    content: string;
    nextStep?: string;
}
export interface DeploymentConfig {
    environment: 'development' | 'staging' | 'production';
    domains?: string[];
    environmentVariables: Record<string, string>;
    scaling?: {
        minInstances: number;
        maxInstances: number;
    };
}
export interface Task {
    taskId: string;
    taskName: string;
    description: string;
    agent: 'CodeCreator' | 'ReviewRefine' | 'Finalizer';
    dependencies: string[];
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    priority: number;
    estimatedDuration?: number;
    metadata: {
        category: 'frontend' | 'backend' | 'chatbot' | 'infrastructure' | 'documentation';
        complexity: 'low' | 'medium' | 'high';
        files: string[];
    };
}
export interface TaskPlan {
    tasks: Task[];
    totalEstimatedDuration: number;
    dependencies: Record<string, string[]>;
}
export interface CodeArtifact {
    taskId: string;
    files: GeneratedFile[];
    errors: CodeError[];
    warnings: CodeWarning[];
    metadata: {
        generatedAt: string;
        agent: string;
        version: string;
    };
}
export interface GeneratedFile {
    path: string;
    content: string;
    type: 'source' | 'config' | 'documentation' | 'test';
    language: string;
}
export interface CodeError {
    severity: 'critical' | 'major' | 'minor';
    message: string;
    file?: string;
    line?: number;
    column?: number;
    rule?: string;
}
export interface CodeWarning {
    message: string;
    file?: string;
    line?: number;
    suggestion?: string;
}
export interface ReviewResult {
    taskId: string;
    overallQuality: 'excellent' | 'good' | 'fair' | 'poor';
    errors: CodeError[];
    suggestions: Suggestion[];
    securityIssues: SecurityIssue[];
    performanceIssues: PerformanceIssue[];
    updatedSpecDelta?: Partial<DetailedSpecification>;
    updatedTasks?: Task[];
}
export interface Suggestion {
    type: 'improvement' | 'optimization' | 'refactor' | 'feature';
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
}
export interface SecurityIssue {
    severity: 'critical' | 'high' | 'medium' | 'low';
    description: string;
    file?: string;
    line?: number;
    recommendation: string;
}
export interface PerformanceIssue {
    type: 'memory' | 'cpu' | 'network' | 'database';
    description: string;
    impact: 'high' | 'medium' | 'low';
    recommendation: string;
}
export interface WorkflowState {
    sessionId: string;
    currentIteration: number;
    maxIterations: number;
    status: 'initializing' | 'enhancing' | 'creating' | 'reviewing' | 'finalizing' | 'completed' | 'failed';
    specification?: DetailedSpecification;
    taskPlan?: TaskPlan;
    artifacts: CodeArtifact[];
    reviews: ReviewResult[];
    finalOutput?: FinalOutput;
    createdAt: string;
    updatedAt: string;
}
export interface FinalOutput {
    projectBundle: {
        s3Key: string;
        downloadUrl: string;
    };
    readme: {
        s3Key: string;
        content: string;
    };
    deploymentScript: {
        s3Key: string;
        type: 'cdk' | 'cloudformation' | 'terraform';
    };
    documentation: {
        apiDocs?: string;
        architectureDiagram?: string;
        userGuide?: string;
    };
}
export interface DescriptionEnhancerEvent {
    userRequest: UserRequest;
    sessionId: string;
}
export interface CodeCreatorEvent {
    sessionId: string;
    specification: DetailedSpecification;
    task: Task;
    iteration: number;
}
export interface ReviewRefineEvent {
    sessionId: string;
    specification: DetailedSpecification;
    taskPlan: TaskPlan;
    artifacts: CodeArtifact[];
    iteration: number;
}
export interface FinalizerEvent {
    sessionId: string;
    specification: DetailedSpecification;
    artifacts: CodeArtifact[];
    reviews: ReviewResult[];
}
export interface StepFunctionInput {
    sessionId: string;
    userRequest: UserRequest;
}
export interface StepFunctionOutput {
    sessionId: string;
    status: 'success' | 'failure';
    finalOutput?: FinalOutput;
    error?: string;
}
