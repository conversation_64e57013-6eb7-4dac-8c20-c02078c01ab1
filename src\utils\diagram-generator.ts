/**
 * Architecture diagram generation utilities
 */

import { DetailedSpecification, CodeArtifact } from '../types';

export async function generateMermaidDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): Promise<string> {
  
  const diagramType = determineDiagramType(specification);
  
  switch (diagramType) {
    case 'system':
      return generateSystemArchitectureDiagram(specification, artifacts);
    case 'component':
      return generateComponentDiagram(specification, artifacts);
    case 'sequence':
      return generateSequenceDiagram(specification, artifacts);
    case 'database':
      return generateDatabaseDiagram(specification);
    default:
      return generateSystemArchitectureDiagram(specification, artifacts);
  }
}

function determineDiagramType(specification: DetailedSpecification): string {
  // Determine the most appropriate diagram type based on the specification
  if (specification.dataModels.length > 3) {
    return 'database';
  }
  
  if (specification.architecture.components.length > 5) {
    return 'component';
  }
  
  if (specification.apiEndpoints.length > 10) {
    return 'sequence';
  }
  
  return 'system';
}

function generateSystemArchitectureDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): string {
  
  let diagram = `graph TB\n`;
  diagram += `    %% ${specification.projectName} - System Architecture\n\n`;
  
  // Add user/client
  diagram += `    User[👤 User]\n`;
  
  // Add frontend components
  if (specification.techStack.frontend) {
    diagram += `    Frontend[🖥️ Frontend<br/>${specification.techStack.frontend.framework}]\n`;
    diagram += `    User --> Frontend\n`;
  }
  
  // Add API Gateway if serverless
  if (specification.architecture.type === 'serverless') {
    diagram += `    API[🚪 API Gateway]\n`;
    diagram += `    Frontend --> API\n`;
  }
  
  // Add backend components
  if (specification.techStack.backend) {
    diagram += `    Backend[⚙️ Backend<br/>${specification.techStack.backend.framework}]\n`;
    
    if (specification.architecture.type === 'serverless') {
      diagram += `    API --> Backend\n`;
    } else {
      diagram += `    Frontend --> Backend\n`;
    }
  }
  
  // Add database
  if (specification.techStack.backend?.database) {
    diagram += `    DB[(🗄️ Database<br/>${specification.techStack.backend.database})]\n`;
    diagram += `    Backend --> DB\n`;
  }
  
  // Add external services
  const externalServices = extractExternalServices(specification, artifacts);
  externalServices.forEach((service, index) => {
    diagram += `    Ext${index}[🌐 ${service}]\n`;
    diagram += `    Backend --> Ext${index}\n`;
  });
  
  // Add chatbot if present
  if (specification.chatbotFlows && specification.chatbotFlows.length > 0) {
    diagram += `    Chatbot[🤖 Chatbot]\n`;
    diagram += `    Frontend --> Chatbot\n`;
    diagram += `    Chatbot --> Backend\n`;
  }
  
  // Add monitoring and logging
  if (specification.techStack.infrastructure?.monitoring) {
    diagram += `    Monitor[📊 Monitoring<br/>${specification.techStack.infrastructure.monitoring}]\n`;
    diagram += `    Backend -.-> Monitor\n`;
    diagram += `    Frontend -.-> Monitor\n`;
  }
  
  // Add styling
  diagram += `\n    %% Styling\n`;
  diagram += `    classDef frontend fill:#e1f5fe\n`;
  diagram += `    classDef backend fill:#f3e5f5\n`;
  diagram += `    classDef database fill:#e8f5e8\n`;
  diagram += `    classDef external fill:#fff3e0\n`;
  diagram += `    classDef monitoring fill:#fce4ec\n`;
  
  diagram += `    class Frontend frontend\n`;
  diagram += `    class Backend backend\n`;
  diagram += `    class DB database\n`;
  diagram += `    class Monitor monitoring\n`;
  
  externalServices.forEach((_, index) => {
    diagram += `    class Ext${index} external\n`;
  });
  
  return diagram;
}

function generateComponentDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): string {
  
  let diagram = `graph TD\n`;
  diagram += `    %% ${specification.projectName} - Component Architecture\n\n`;
  
  // Group components by type
  const frontendComponents = specification.architecture.components.filter(c => c.type === 'frontend');
  const backendComponents = specification.architecture.components.filter(c => c.type === 'backend');
  const apiComponents = specification.architecture.components.filter(c => c.type === 'api');
  
  // Add frontend components
  if (frontendComponents.length > 0) {
    diagram += `    subgraph Frontend["🖥️ Frontend Layer"]\n`;
    frontendComponents.forEach(component => {
      diagram += `        ${component.id}[${component.name}]\n`;
    });
    diagram += `    end\n\n`;
  }
  
  // Add API components
  if (apiComponents.length > 0) {
    diagram += `    subgraph API["🚪 API Layer"]\n`;
    apiComponents.forEach(component => {
      diagram += `        ${component.id}[${component.name}]\n`;
    });
    diagram += `    end\n\n`;
  }
  
  // Add backend components
  if (backendComponents.length > 0) {
    diagram += `    subgraph Backend["⚙️ Backend Layer"]\n`;
    backendComponents.forEach(component => {
      diagram += `        ${component.id}[${component.name}]\n`;
    });
    diagram += `    end\n\n`;
  }
  
  // Add relationships
  specification.architecture.integrations.forEach(integration => {
    diagram += `    ${integration.source} --> ${integration.target}\n`;
  });
  
  return diagram;
}

function generateSequenceDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): string {
  
  let diagram = `sequenceDiagram\n`;
  diagram += `    participant U as 👤 User\n`;
  diagram += `    participant F as 🖥️ Frontend\n`;
  diagram += `    participant A as 🚪 API\n`;
  diagram += `    participant B as ⚙️ Backend\n`;
  diagram += `    participant D as 🗄️ Database\n\n`;
  
  // Generate sequence for main user flows
  const mainEndpoints = specification.apiEndpoints.slice(0, 5); // Limit to first 5 endpoints
  
  mainEndpoints.forEach(endpoint => {
    const action = endpoint.method === 'GET' ? 'Request' : 'Submit';
    const response = endpoint.method === 'GET' ? 'Data' : 'Confirmation';
    
    diagram += `    U->>F: ${action} ${endpoint.path}\n`;
    diagram += `    F->>A: ${endpoint.method} ${endpoint.path}\n`;
    diagram += `    A->>B: Process Request\n`;
    
    if (endpoint.method !== 'GET') {
      diagram += `    B->>D: Store/Update Data\n`;
      diagram += `    D-->>B: Success\n`;
    } else {
      diagram += `    B->>D: Query Data\n`;
      diagram += `    D-->>B: Return Data\n`;
    }
    
    diagram += `    B-->>A: ${response}\n`;
    diagram += `    A-->>F: ${response}\n`;
    diagram += `    F-->>U: Display ${response}\n\n`;
  });
  
  return diagram;
}

function generateDatabaseDiagram(specification: DetailedSpecification): string {
  let diagram = `erDiagram\n`;
  diagram += `    %% ${specification.projectName} - Database Schema\n\n`;
  
  specification.dataModels.forEach(model => {
    diagram += `    ${model.name} {\n`;
    
    model.fields.forEach(field => {
      const type = mapFieldTypeToERD(field.type);
      const constraint = field.required ? 'NOT NULL' : '';
      diagram += `        ${type} ${field.name} ${constraint}\n`;
    });
    
    diagram += `    }\n\n`;
  });
  
  // Add relationships
  specification.dataModels.forEach(model => {
    model.relationships.forEach(rel => {
      const relationshipType = mapRelationshipType(rel.type);
      diagram += `    ${model.name} ${relationshipType} ${rel.target} : "${rel.type}"\n`;
    });
  });
  
  return diagram;
}

function extractExternalServices(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): string[] {
  const services = new Set<string>();
  
  // Check for common external services in the specification
  if (specification.techStack.backend?.authentication === 'OAuth') {
    services.add('OAuth Provider');
  }
  
  // Check artifacts for external service usage
  artifacts.forEach(artifact => {
    artifact.files.forEach(file => {
      const content = file.content.toLowerCase();
      
      // Check for common external services
      if (content.includes('stripe')) services.add('Stripe');
      if (content.includes('sendgrid') || content.includes('mailgun')) services.add('Email Service');
      if (content.includes('twilio')) services.add('SMS Service');
      if (content.includes('aws-sdk')) services.add('AWS Services');
      if (content.includes('firebase')) services.add('Firebase');
      if (content.includes('redis')) services.add('Redis Cache');
      if (content.includes('elasticsearch')) services.add('Elasticsearch');
    });
  });
  
  return Array.from(services);
}

function mapFieldTypeToERD(fieldType: string): string {
  const typeMap: Record<string, string> = {
    'string': 'varchar',
    'number': 'int',
    'boolean': 'boolean',
    'date': 'datetime',
    'array': 'json',
    'object': 'json',
  };
  
  return typeMap[fieldType.toLowerCase()] || 'varchar';
}

function mapRelationshipType(relType: string): string {
  const relationshipMap: Record<string, string> = {
    'oneToOne': '||--||',
    'oneToMany': '||--o{',
    'manyToMany': '}o--o{',
  };
  
  return relationshipMap[relType] || '||--o{';
}

export function generateFlowchartDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[]
): string {
  let diagram = `flowchart TD\n`;
  diagram += `    %% ${specification.projectName} - User Flow\n\n`;
  
  diagram += `    Start([👤 User Starts])\n`;
  diagram += `    Auth{🔐 Authenticated?}\n`;
  diagram += `    Login[📝 Login]\n`;
  diagram += `    Dashboard[📊 Dashboard]\n`;
  diagram += `    Features[⚡ Features]\n`;
  diagram += `    End([✅ Complete])\n\n`;
  
  diagram += `    Start --> Auth\n`;
  diagram += `    Auth -->|No| Login\n`;
  diagram += `    Auth -->|Yes| Dashboard\n`;
  diagram += `    Login --> Dashboard\n`;
  diagram += `    Dashboard --> Features\n`;
  diagram += `    Features --> End\n`;
  
  return diagram;
}

export function generateDeploymentDiagram(specification: DetailedSpecification): string {
  let diagram = `graph TB\n`;
  diagram += `    %% ${specification.projectName} - Deployment Architecture\n\n`;
  
  if (specification.architecture.type === 'serverless') {
    diagram += `    subgraph AWS["☁️ AWS Cloud"]\n`;
    diagram += `        CF[📦 CloudFront]\n`;
    diagram += `        S3[🪣 S3 Bucket]\n`;
    diagram += `        API[🚪 API Gateway]\n`;
    diagram += `        Lambda[⚡ Lambda Functions]\n`;
    diagram += `        RDS[(🗄️ RDS Database)]\n`;
    diagram += `        CW[📊 CloudWatch]\n`;
    diagram += `    end\n\n`;
    
    diagram += `    User[👤 User] --> CF\n`;
    diagram += `    CF --> S3\n`;
    diagram += `    CF --> API\n`;
    diagram += `    API --> Lambda\n`;
    diagram += `    Lambda --> RDS\n`;
    diagram += `    Lambda -.-> CW\n`;
  } else {
    diagram += `    subgraph Cloud["☁️ Cloud Infrastructure"]\n`;
    diagram += `        LB[⚖️ Load Balancer]\n`;
    diagram += `        App[🖥️ Application Servers]\n`;
    diagram += `        DB[(🗄️ Database)]\n`;
    diagram += `        Cache[⚡ Cache]\n`;
    diagram += `    end\n\n`;
    
    diagram += `    User[👤 User] --> LB\n`;
    diagram += `    LB --> App\n`;
    diagram += `    App --> DB\n`;
    diagram += `    App --> Cache\n`;
  }
  
  return diagram;
}
