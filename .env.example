# AI Agent Workflow System Environment Configuration

# Required: AI Model API Configuration (at least one required)
GEMINI_API_KEY=your-gemini-api-key-here
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# Required: AWS Configuration
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1

# Optional: Notification Configuration
NOTIFICATION_EMAIL=<EMAIL>

# Optional: Logging Configuration
LOG_LEVEL=info

# Optional: Environment
ENVIRONMENT=development

# Optional: CDK Configuration
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=us-east-1

# Optional: Custom Configuration
MAX_ITERATIONS=5
DEFAULT_TIMEOUT_MINUTES=15
ARTIFACTS_RETENTION_DAYS=30
