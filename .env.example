# AI Agent Workflow System Environment Configuration

# Required: AI Model API Configuration
GEMINI_API_KEY=AIzaSyCIT_xAUHosbsgvMCm0kVkhPxNLogS_Cv0
OPENROUTER_API_KEY=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

# Required: AWS Configuration
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1

# Optional: Notification Configuration
NOTIFICATION_EMAIL=<EMAIL>

# Optional: Logging Configuration
LOG_LEVEL=info

# Optional: Environment
ENVIRONMENT=development

# Optional: CDK Configuration
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=us-east-1

# Optional: Custom Configuration
MAX_ITERATIONS=5
DEFAULT_TIMEOUT_MINUTES=15
ARTIFACTS_RETENTION_DAYS=30
