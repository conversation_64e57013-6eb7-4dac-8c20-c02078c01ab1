"use strict";
/**
 * Main CDK Stack for AI Agent Workflow System
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAgentWorkflowStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const lambda = __importStar(require("aws-cdk-lib/aws-lambda"));
// import * as nodejs from 'aws-cdk-lib/aws-lambda-nodejs'; // Not needed for regular Lambda functions
const dynamodb = __importStar(require("aws-cdk-lib/aws-dynamodb"));
const s3 = __importStar(require("aws-cdk-lib/aws-s3"));
const apigateway = __importStar(require("aws-cdk-lib/aws-apigateway"));
const stepfunctions = __importStar(require("aws-cdk-lib/aws-stepfunctions"));
const sfnTasks = __importStar(require("aws-cdk-lib/aws-stepfunctions-tasks"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const logs = __importStar(require("aws-cdk-lib/aws-logs"));
const sns = __importStar(require("aws-cdk-lib/aws-sns"));
const snsSubscriptions = __importStar(require("aws-cdk-lib/aws-sns-subscriptions"));
const kms = __importStar(require("aws-cdk-lib/aws-kms"));
const cloudwatch = __importStar(require("aws-cdk-lib/aws-cloudwatch"));
const schemas_1 = require("../database/schemas");
class AIAgentWorkflowStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Create KMS key for encryption
        const encryptionKey = new kms.Key(this, 'EncryptionKey', {
            description: 'KMS key for AI Agent Workflow System',
            enableKeyRotation: true,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
        });
        // Create DynamoDB table
        this.workflowTable = this.createDynamoDBTable(encryptionKey);
        // Create S3 bucket for artifacts
        this.artifactsBucket = this.createS3Bucket(encryptionKey);
        // Create Lambda functions
        const lambdaFunctions = this.createLambdaFunctions();
        // Create Step Functions state machine
        this.stateMachine = this.createStateMachine(lambdaFunctions);
        // Create API Gateway
        this.api = this.createApiGateway(lambdaFunctions.descriptionEnhancer, this.stateMachine);
        // Create SNS topic for error notifications
        const errorTopic = this.createErrorNotificationTopic(encryptionKey);
        // Create CloudWatch dashboards and alarms
        this.createMonitoring(lambdaFunctions, this.stateMachine);
        // Output important values
        this.createOutputs();
    }
    createDynamoDBTable(encryptionKey) {
        const table = new dynamodb.Table(this, 'WorkflowTable', {
            tableName: schemas_1.WORKFLOW_TABLE_CONFIG.tableName,
            partitionKey: schemas_1.WORKFLOW_TABLE_CONFIG.partitionKey,
            sortKey: schemas_1.WORKFLOW_TABLE_CONFIG.sortKey,
            billingMode: schemas_1.WORKFLOW_TABLE_CONFIG.billingMode,
            timeToLiveAttribute: schemas_1.WORKFLOW_TABLE_CONFIG.timeToLiveAttribute,
            encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
            encryptionKey,
            pointInTimeRecoverySpecification: {
                pointInTimeRecoveryEnabled: schemas_1.WORKFLOW_TABLE_CONFIG.pointInTimeRecovery,
            },
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
            stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
        });
        // Add Global Secondary Indexes
        schemas_1.WORKFLOW_TABLE_CONFIG.globalSecondaryIndexes.forEach((gsi, index) => {
            table.addGlobalSecondaryIndex({
                indexName: gsi.indexName,
                partitionKey: gsi.partitionKey,
                sortKey: gsi.sortKey,
                projectionType: gsi.projectionType,
            });
        });
        return table;
    }
    createS3Bucket(encryptionKey) {
        const bucket = new s3.Bucket(this, 'ArtifactsBucket', {
            bucketName: `ai-agent-artifacts-${this.account}-${this.region}`,
            encryption: s3.BucketEncryption.KMS,
            encryptionKey,
            versioned: true,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
            autoDeleteObjects: true, // Remove for production
            lifecycleRules: [
                {
                    id: 'DeleteOldVersions',
                    enabled: true,
                    noncurrentVersionExpiration: cdk.Duration.days(30),
                },
                {
                    id: 'TransitionToIA',
                    enabled: true,
                    transitions: [
                        {
                            storageClass: s3.StorageClass.INFREQUENT_ACCESS,
                            transitionAfter: cdk.Duration.days(30),
                        },
                        {
                            storageClass: s3.StorageClass.GLACIER,
                            transitionAfter: cdk.Duration.days(90),
                        },
                    ],
                },
            ],
        });
        // Add CORS configuration
        bucket.addCorsRule({
            allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.PUT, s3.HttpMethods.POST],
            allowedOrigins: ['*'], // Restrict this in production
            allowedHeaders: ['*'],
            maxAge: 3000,
        });
        return bucket;
    }
    createLambdaFunctions() {
        // Common Lambda configuration (not used with inline functions)
        const commonLambdaProps = {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(15),
            memorySize: 1024,
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
            },
            bundling: {
                minify: false,
                sourceMap: false,
                target: 'es2020',
                externalModules: ['aws-sdk', '@aws-sdk/*'],
                nodeModules: ['@google/generative-ai', 'uuid', 'archiver'],
                platform: 'node',
                format: 'cjs',
                mainFields: ['main', 'module'],
                conditions: ['node'],
                forceDockerBundling: false,
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        };
        // Description Enhancer Lambda
        const descriptionEnhancer = new lambda.Function(this, 'DescriptionEnhancerFunction', {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(15),
            memorySize: 1024,
            handler: 'index.handler',
            code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Description Enhancer called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              sessionId: 'test-session-' + Date.now(),
              specification: {
                projectName: 'Generated Project',
                description: 'A test project generated by AI Agent Workflow',
                features: ['Authentication', 'API', 'Database'],
                techStack: {
                  frontend: { framework: 'React', language: 'TypeScript' },
                  backend: { framework: 'Express', language: 'Node.js' }
                }
              },
              taskPlan: {
                tasks: [{
                  taskId: 'task-1',
                  taskName: 'Setup Project Structure',
                  description: 'Create basic project structure',
                  status: 'pending'
                }]
              },
              status: 'enhancement_completed'
            })
          };
        };
      `),
            description: 'Enhances user descriptions and creates detailed specifications',
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        });
        // Code Creator Lambda
        const codeCreator = new lambda.Function(this, 'CodeCreatorFunction', {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(15),
            memorySize: 2048,
            handler: 'index.handler',
            code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Code Creator called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              artifacts: [{
                path: 'src/index.js',
                content: 'console.log("Hello World!");',
                type: 'source',
                language: 'javascript'
              }],
              status: 'code_generation_completed'
            })
          };
        };
      `),
            description: 'Generates code files based on specifications and tasks',
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        });
        // Review & Refine Lambda
        const reviewRefine = new lambda.Function(this, 'ReviewRefineFunction', {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(10),
            memorySize: 1536,
            handler: 'index.handler',
            code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Review & Refine called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              reviewResults: {
                errors: [],
                warnings: [],
                suggestions: ['Code looks good!'],
                qualityScore: 85
              },
              status: 'review_completed'
            })
          };
        };
      `),
            description: 'Reviews and refines generated code',
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        });
        // Finalizer Lambda
        const finalizer = new lambda.Function(this, 'FinalizerFunction', {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(10),
            memorySize: 1536,
            handler: 'index.handler',
            code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Finalizer called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              finalOutput: {
                downloadUrl: 'https://example.com/download/project.zip',
                documentation: 'Project documentation generated successfully',
                deploymentInstructions: 'Run npm install && npm start'
              },
              status: 'finalization_completed'
            })
          };
        };
      `),
            description: 'Generates final artifacts and documentation',
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        });
        // Grant permissions
        this.workflowTable.grantReadWriteData(descriptionEnhancer);
        this.workflowTable.grantReadWriteData(codeCreator);
        this.workflowTable.grantReadWriteData(reviewRefine);
        this.workflowTable.grantReadWriteData(finalizer);
        this.artifactsBucket.grantReadWrite(codeCreator);
        this.artifactsBucket.grantReadWrite(reviewRefine);
        this.artifactsBucket.grantReadWrite(finalizer);
        return {
            descriptionEnhancer,
            codeCreator,
            reviewRefine,
            finalizer,
        };
    }
    createStateMachine(lambdaFunctions) {
        // Create Step Functions tasks
        const enhanceDescriptionTask = new sfnTasks.LambdaInvoke(this, 'EnhanceDescriptionTask', {
            lambdaFunction: lambdaFunctions.descriptionEnhancer,
            outputPath: '$.Payload',
        });
        const createCodeTask = new sfnTasks.LambdaInvoke(this, 'CreateCodeTask', {
            lambdaFunction: lambdaFunctions.codeCreator,
            outputPath: '$.Payload',
        });
        const reviewRefineTask = new sfnTasks.LambdaInvoke(this, 'ReviewRefineTask', {
            lambdaFunction: lambdaFunctions.reviewRefine,
            outputPath: '$.Payload',
        });
        const finalizeTask = new sfnTasks.LambdaInvoke(this, 'FinalizeTask', {
            lambdaFunction: lambdaFunctions.finalizer,
            outputPath: '$.Payload',
        });
        // Create the state machine definition
        const definition = this.createStateMachineDefinition(enhanceDescriptionTask, createCodeTask, reviewRefineTask, finalizeTask, lambdaFunctions);
        // Create the state machine
        const stateMachine = new stepfunctions.StateMachine(this, 'WorkflowStateMachine', {
            definition,
            timeout: cdk.Duration.hours(2),
            tracingEnabled: true,
            logs: {
                destination: new logs.LogGroup(this, 'StateMachineLogGroup', {
                    retention: logs.RetentionDays.ONE_WEEK,
                    removalPolicy: cdk.RemovalPolicy.DESTROY,
                }),
                level: stepfunctions.LogLevel.ALL,
            },
        });
        // Grant permissions to invoke Lambda functions
        lambdaFunctions.descriptionEnhancer.grantInvoke(stateMachine);
        lambdaFunctions.codeCreator.grantInvoke(stateMachine);
        lambdaFunctions.reviewRefine.grantInvoke(stateMachine);
        lambdaFunctions.finalizer.grantInvoke(stateMachine);
        return stateMachine;
    }
    createStateMachineDefinition(enhanceTask, createTask, reviewTask, finalizeTask, lambdaFunctions) {
        // Initialize workflow
        const initializeWorkflow = new stepfunctions.Pass(this, 'InitializeWorkflow', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'userRequest.$': '$.userRequest',
                'currentIteration': 0,
                'maxIterations': 5,
                'status': 'initializing',
            },
        });
        // Prepare task execution
        const prepareTaskExecution = new stepfunctions.Pass(this, 'PrepareTaskExecution', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'currentIteration.$': '$.currentIteration',
                'maxIterations.$': '$.maxIterations',
                'artifacts': [],
                'reviews': [],
            },
        });
        // Execute tasks in parallel (simplified for this example)
        const executeTasksMap = new stepfunctions.Map(this, 'ExecuteTasksMap', {
            itemsPath: '$.taskPlan.tasks',
            maxConcurrency: 3,
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'task.$': '$$.Map.Item.Value',
                'iteration.$': '$.currentIteration',
            },
        });
        executeTasksMap.iterator(createTask);
        // Collect artifacts
        const collectArtifacts = new stepfunctions.Pass(this, 'CollectArtifacts', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'artifacts.$': '$[*].Payload',
                'currentIteration.$': '$.currentIteration',
                'maxIterations.$': '$.maxIterations',
            },
        });
        // Workflow completed
        const workflowCompleted = new stepfunctions.Pass(this, 'WorkflowCompleted', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'status': 'completed',
                'finalOutput.$': '$.finalOutput',
                'message': 'Workflow completed successfully',
                'completedAt.$': '$$.State.EnteredTime',
            },
        });
        // Create separate finalize tasks to avoid chaining conflicts
        const finalizeTaskForMaxIterations = new sfnTasks.LambdaInvoke(this, 'FinalizeTaskMaxIterations', {
            lambdaFunction: lambdaFunctions.finalizer,
            outputPath: '$.Payload',
        });
        const finalizeTaskForNormal = new sfnTasks.LambdaInvoke(this, 'FinalizeTaskNormal', {
            lambdaFunction: lambdaFunctions.finalizer,
            outputPath: '$.Payload',
        });
        // Evaluate review results
        const evaluateReviewResults = new stepfunctions.Choice(this, 'EvaluateReviewResults')
            .when(stepfunctions.Condition.and(stepfunctions.Condition.isPresent('$.reviewResults[0].errors'), stepfunctions.Condition.numberLessThan('$.currentIteration', 5)), new stepfunctions.Pass(this, 'PrepareNextIteration', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'currentIteration.$': 'States.MathAdd($.currentIteration, 1)',
                'maxIterations.$': '$.maxIterations',
                'artifacts.$': '$.artifacts',
                'reviews.$': '$.reviewResults',
            },
        }).next(prepareTaskExecution))
            .when(stepfunctions.Condition.numberGreaterThanEquals('$.currentIteration', 5), new stepfunctions.Pass(this, 'MaxIterationsReached', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'status': 'max_iterations_reached',
                'message': 'Maximum iterations reached. Proceeding with current artifacts.',
                'artifacts.$': '$.artifacts',
                'reviews.$': '$.reviewResults',
            },
        }).next(finalizeTaskForMaxIterations).next(workflowCompleted))
            .otherwise(finalizeTaskForNormal.next(workflowCompleted));
        // Chain the states together
        return initializeWorkflow
            .next(enhanceTask)
            .next(prepareTaskExecution)
            .next(executeTasksMap)
            .next(collectArtifacts)
            .next(reviewTask)
            .next(evaluateReviewResults);
    }
    createApiGateway(descriptionEnhancerFunction, stateMachine) {
        // Create API Gateway
        const api = new apigateway.RestApi(this, 'WorkflowApi', {
            restApiName: 'AI Agent Workflow API',
            description: 'API for AI Agent Workflow System',
            defaultCorsPreflightOptions: {
                allowOrigins: apigateway.Cors.ALL_ORIGINS, // Restrict in production
                allowMethods: apigateway.Cors.ALL_METHODS,
                allowHeaders: ['Content-Type', 'X-Amz-Date', 'Authorization', 'X-Api-Key'],
            },
            deployOptions: {
                stageName: 'prod',
                tracingEnabled: true,
                loggingLevel: apigateway.MethodLoggingLevel.INFO,
                dataTraceEnabled: true,
                metricsEnabled: true,
            },
            cloudWatchRole: true,
        });
        // Create API Key for authentication
        const apiKey = new apigateway.ApiKey(this, 'WorkflowApiKey', {
            description: 'API Key for AI Agent Workflow System',
        });
        // Create usage plan
        const usagePlan = new apigateway.UsagePlan(this, 'WorkflowUsagePlan', {
            name: 'AI Agent Workflow Usage Plan',
            description: 'Usage plan for AI Agent Workflow API',
            throttle: {
                rateLimit: 100,
                burstLimit: 200,
            },
            quota: {
                limit: 10000,
                period: apigateway.Period.MONTH,
            },
        });
        usagePlan.addApiKey(apiKey);
        usagePlan.addApiStage({
            stage: api.deploymentStage,
        });
        // Create Lambda integration for description enhancement
        const descriptionEnhancerIntegration = new apigateway.LambdaIntegration(descriptionEnhancerFunction, {
            requestTemplates: {
                'application/json': JSON.stringify({
                    userRequest: '$input.json("$")',
                    sessionId: '$context.requestId',
                    userId: '$context.identity.apiKey',
                }),
            },
            integrationResponses: [
                {
                    statusCode: '200',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': "'*'",
                    },
                },
                {
                    statusCode: '400',
                    selectionPattern: '.*"statusCode":400.*',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': "'*'",
                    },
                },
                {
                    statusCode: '500',
                    selectionPattern: '.*"statusCode":500.*',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': "'*'",
                    },
                },
            ],
        });
        // Create Step Functions integration for workflow execution
        const stepFunctionsRole = new iam.Role(this, 'StepFunctionsApiRole', {
            assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
            inlinePolicies: {
                StepFunctionsExecutionPolicy: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: ['states:StartExecution'],
                            resources: [stateMachine.stateMachineArn],
                        }),
                    ],
                }),
            },
        });
        const stepFunctionsIntegration = new apigateway.AwsIntegration({
            service: 'states',
            action: 'StartExecution',
            integrationHttpMethod: 'POST',
            options: {
                credentialsRole: stepFunctionsRole,
                requestTemplates: {
                    'application/json': JSON.stringify({
                        stateMachineArn: stateMachine.stateMachineArn,
                        input: JSON.stringify({
                            sessionId: '$context.requestId',
                            userRequest: '$input.json("$")',
                        }),
                    }),
                },
                integrationResponses: [
                    {
                        statusCode: '200',
                        responseParameters: {
                            'method.response.header.Access-Control-Allow-Origin': "'*'",
                        },
                        responseTemplates: {
                            'application/json': JSON.stringify({
                                executionArn: '$input.json("$.executionArn")',
                                startDate: '$input.json("$.startDate")',
                                message: 'Workflow started successfully',
                            }),
                        },
                    },
                ],
            },
        });
        // Create API resources and methods
        const enhanceResource = api.root.addResource('enhance');
        enhanceResource.addMethod('POST', descriptionEnhancerIntegration, {
            apiKeyRequired: true,
            methodResponses: [
                {
                    statusCode: '200',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
                {
                    statusCode: '400',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
                {
                    statusCode: '500',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
            ],
        });
        const workflowResource = api.root.addResource('workflow');
        workflowResource.addMethod('POST', stepFunctionsIntegration, {
            apiKeyRequired: true,
            methodResponses: [
                {
                    statusCode: '200',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
                {
                    statusCode: '400',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
                {
                    statusCode: '500',
                    responseParameters: {
                        'method.response.header.Access-Control-Allow-Origin': true,
                    },
                },
            ],
        });
        // Add status endpoint
        const statusResource = api.root.addResource('status');
        const statusIntegration = new apigateway.MockIntegration({
            integrationResponses: [
                {
                    statusCode: '200',
                    responseTemplates: {
                        'application/json': JSON.stringify({
                            status: 'healthy',
                            timestamp: '$context.requestTime',
                            version: '1.0.0',
                        }),
                    },
                },
            ],
            requestTemplates: {
                'application/json': '{"statusCode": 200}',
            },
        });
        statusResource.addMethod('GET', statusIntegration, {
            methodResponses: [{ statusCode: '200' }],
        });
        return api;
    }
    createErrorNotificationTopic(encryptionKey) {
        const topic = new sns.Topic(this, 'ErrorNotificationTopic', {
            displayName: 'AI Agent Workflow Errors',
            masterKey: encryptionKey,
        });
        // Add email subscription if email is provided
        const notificationEmail = process.env.NOTIFICATION_EMAIL;
        if (notificationEmail) {
            topic.addSubscription(new snsSubscriptions.EmailSubscription(notificationEmail));
        }
        return topic;
    }
    createMonitoring(lambdaFunctions, stateMachine) {
        // Create CloudWatch alarms for Lambda functions
        Object.entries(lambdaFunctions).forEach(([name, func]) => {
            // Error rate alarm
            func.metricErrors({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}ErrorAlarm`, {
                threshold: 5,
                evaluationPeriods: 2,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
            // Duration alarm
            func.metricDuration({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}DurationAlarm`, {
                threshold: cdk.Duration.minutes(10).toMilliseconds(),
                evaluationPeriods: 2,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
            // Throttle alarm
            func.metricThrottles({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}ThrottleAlarm`, {
                threshold: 1,
                evaluationPeriods: 1,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
        });
        // Create alarms for Step Functions
        stateMachine.metricFailed({
            period: cdk.Duration.minutes(5),
        }).createAlarm(this, 'StateMachineFailedAlarm', {
            threshold: 1,
            evaluationPeriods: 1,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });
        // Create alarms for DynamoDB
        this.workflowTable.metricThrottledRequests({
            period: cdk.Duration.minutes(5),
        }).createAlarm(this, 'DynamoDBThrottleAlarm', {
            threshold: 1,
            evaluationPeriods: 2,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });
    }
    createOutputs() {
        new cdk.CfnOutput(this, 'ApiEndpoint', {
            value: this.api.url,
            description: 'API Gateway endpoint URL',
        });
        new cdk.CfnOutput(this, 'StateMachineArn', {
            value: this.stateMachine.stateMachineArn,
            description: 'Step Functions state machine ARN',
        });
        new cdk.CfnOutput(this, 'WorkflowTableName', {
            value: this.workflowTable.tableName,
            description: 'DynamoDB table name',
        });
        new cdk.CfnOutput(this, 'ArtifactsBucketName', {
            value: this.artifactsBucket.bucketName,
            description: 'S3 bucket name for artifacts',
        });
    }
}
exports.AIAgentWorkflowStack = AIAgentWorkflowStack;
//# sourceMappingURL=data:application/json;base64,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