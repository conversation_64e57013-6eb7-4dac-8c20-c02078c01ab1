"use strict";
/**
 * Main CDK Stack for AI Agent Workflow System
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAgentWorkflowStack = void 0;
const cdk = __importStar(require("aws-cdk-lib"));
const lambda = __importStar(require("aws-cdk-lib/aws-lambda"));
const nodejs = __importStar(require("aws-cdk-lib/aws-lambda-nodejs"));
const dynamodb = __importStar(require("aws-cdk-lib/aws-dynamodb"));
const s3 = __importStar(require("aws-cdk-lib/aws-s3"));
const apigateway = __importStar(require("aws-cdk-lib/aws-apigateway"));
const stepfunctions = __importStar(require("aws-cdk-lib/aws-stepfunctions"));
const sfnTasks = __importStar(require("aws-cdk-lib/aws-stepfunctions-tasks"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const logs = __importStar(require("aws-cdk-lib/aws-logs"));
const sns = __importStar(require("aws-cdk-lib/aws-sns"));
const snsSubscriptions = __importStar(require("aws-cdk-lib/aws-sns-subscriptions"));
const kms = __importStar(require("aws-cdk-lib/aws-kms"));
const cloudwatch = __importStar(require("aws-cdk-lib/aws-cloudwatch"));
const schemas_1 = require("../database/schemas");
class AIAgentWorkflowStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        // Create KMS key for encryption
        const encryptionKey = new kms.Key(this, 'EncryptionKey', {
            description: 'KMS key for AI Agent Workflow System',
            enableKeyRotation: true,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
        });
        // Create DynamoDB table
        this.workflowTable = this.createDynamoDBTable(encryptionKey);
        // Create S3 bucket for artifacts
        this.artifactsBucket = this.createS3Bucket(encryptionKey);
        // Create Lambda functions
        const lambdaFunctions = this.createLambdaFunctions();
        // Create Step Functions state machine
        this.stateMachine = this.createStateMachine(lambdaFunctions);
        // Create API Gateway
        this.api = this.createApiGateway(lambdaFunctions.descriptionEnhancer, this.stateMachine);
        // Create SNS topic for error notifications
        const errorTopic = this.createErrorNotificationTopic(encryptionKey);
        // Create CloudWatch dashboards and alarms
        this.createMonitoring(lambdaFunctions, this.stateMachine);
        // Output important values
        this.createOutputs();
    }
    createDynamoDBTable(encryptionKey) {
        const table = new dynamodb.Table(this, 'WorkflowTable', {
            tableName: schemas_1.WORKFLOW_TABLE_CONFIG.tableName,
            partitionKey: schemas_1.WORKFLOW_TABLE_CONFIG.partitionKey,
            sortKey: schemas_1.WORKFLOW_TABLE_CONFIG.sortKey,
            billingMode: schemas_1.WORKFLOW_TABLE_CONFIG.billingMode,
            timeToLiveAttribute: schemas_1.WORKFLOW_TABLE_CONFIG.timeToLiveAttribute,
            encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
            encryptionKey,
            pointInTimeRecovery: schemas_1.WORKFLOW_TABLE_CONFIG.pointInTimeRecovery,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
            stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
        });
        // Add Global Secondary Indexes
        schemas_1.WORKFLOW_TABLE_CONFIG.globalSecondaryIndexes.forEach((gsi, index) => {
            table.addGlobalSecondaryIndex({
                indexName: gsi.indexName,
                partitionKey: gsi.partitionKey,
                sortKey: gsi.sortKey,
                projectionType: gsi.projectionType,
            });
        });
        return table;
    }
    createS3Bucket(encryptionKey) {
        const bucket = new s3.Bucket(this, 'ArtifactsBucket', {
            bucketName: `ai-agent-artifacts-${this.account}-${this.region}`,
            encryption: s3.BucketEncryption.KMS,
            encryptionKey,
            versioned: true,
            blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
            autoDeleteObjects: true, // Remove for production
            lifecycleRules: [
                {
                    id: 'DeleteOldVersions',
                    enabled: true,
                    noncurrentVersionExpiration: cdk.Duration.days(30),
                },
                {
                    id: 'TransitionToIA',
                    enabled: true,
                    transitions: [
                        {
                            storageClass: s3.StorageClass.INFREQUENT_ACCESS,
                            transitionAfter: cdk.Duration.days(30),
                        },
                        {
                            storageClass: s3.StorageClass.GLACIER,
                            transitionAfter: cdk.Duration.days(90),
                        },
                    ],
                },
            ],
        });
        // Add CORS configuration
        bucket.addCorsRule({
            allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.PUT, s3.HttpMethods.POST],
            allowedOrigins: ['*'], // Restrict this in production
            allowedHeaders: ['*'],
            maxAge: 3000,
        });
        return bucket;
    }
    createLambdaFunctions() {
        // Common Lambda configuration
        const commonLambdaProps = {
            runtime: lambda.Runtime.NODEJS_20_X,
            timeout: cdk.Duration.minutes(15),
            memorySize: 1024,
            environment: {
                WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
                ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
                LOG_LEVEL: 'info',
            },
            bundling: {
                minify: true,
                sourceMap: true,
                target: 'es2020',
                externalModules: ['aws-sdk'],
            },
            tracing: lambda.Tracing.ACTIVE,
            logRetention: logs.RetentionDays.ONE_WEEK,
        };
        // Description Enhancer Lambda
        const descriptionEnhancer = new nodejs.NodejsFunction(this, 'DescriptionEnhancerFunction', {
            ...commonLambdaProps,
            entry: 'src/agents/description-enhancer/handler.ts',
            handler: 'handler',
            description: 'Enhances user descriptions and creates detailed specifications',
            environment: {
                ...commonLambdaProps.environment,
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
        });
        // Code Creator Lambda
        const codeCreator = new nodejs.NodejsFunction(this, 'CodeCreatorFunction', {
            ...commonLambdaProps,
            entry: 'src/agents/code-creator/handler.ts',
            handler: 'handler',
            description: 'Generates code files based on specifications and tasks',
            timeout: cdk.Duration.minutes(15),
            memorySize: 2048,
            environment: {
                ...commonLambdaProps.environment,
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
        });
        // Review & Refine Lambda
        const reviewRefine = new nodejs.NodejsFunction(this, 'ReviewRefineFunction', {
            ...commonLambdaProps,
            entry: 'src/agents/review-refine/handler.ts',
            handler: 'handler',
            description: 'Reviews and refines generated code',
            timeout: cdk.Duration.minutes(10),
            memorySize: 1536,
            environment: {
                ...commonLambdaProps.environment,
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
        });
        // Finalizer Lambda
        const finalizer = new nodejs.NodejsFunction(this, 'FinalizerFunction', {
            ...commonLambdaProps,
            entry: 'src/agents/finalizer/handler.ts',
            handler: 'handler',
            description: 'Generates final artifacts and documentation',
            timeout: cdk.Duration.minutes(10),
            memorySize: 1536,
            environment: {
                ...commonLambdaProps.environment,
                GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
                OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
                DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
            },
        });
        // Grant permissions
        this.workflowTable.grantReadWriteData(descriptionEnhancer);
        this.workflowTable.grantReadWriteData(codeCreator);
        this.workflowTable.grantReadWriteData(reviewRefine);
        this.workflowTable.grantReadWriteData(finalizer);
        this.artifactsBucket.grantReadWrite(codeCreator);
        this.artifactsBucket.grantReadWrite(reviewRefine);
        this.artifactsBucket.grantReadWrite(finalizer);
        return {
            descriptionEnhancer,
            codeCreator,
            reviewRefine,
            finalizer,
        };
    }
    createStateMachine(lambdaFunctions) {
        // Create Step Functions tasks
        const enhanceDescriptionTask = new sfnTasks.LambdaInvoke(this, 'EnhanceDescriptionTask', {
            lambdaFunction: lambdaFunctions.descriptionEnhancer,
            outputPath: '$.Payload',
        });
        const createCodeTask = new sfnTasks.LambdaInvoke(this, 'CreateCodeTask', {
            lambdaFunction: lambdaFunctions.codeCreator,
            outputPath: '$.Payload',
        });
        const reviewRefineTask = new sfnTasks.LambdaInvoke(this, 'ReviewRefineTask', {
            lambdaFunction: lambdaFunctions.reviewRefine,
            outputPath: '$.Payload',
        });
        const finalizeTask = new sfnTasks.LambdaInvoke(this, 'FinalizeTask', {
            lambdaFunction: lambdaFunctions.finalizer,
            outputPath: '$.Payload',
        });
        // Create the state machine definition
        const definition = this.createStateMachineDefinition(enhanceDescriptionTask, createCodeTask, reviewRefineTask, finalizeTask);
        // Create the state machine
        const stateMachine = new stepfunctions.StateMachine(this, 'WorkflowStateMachine', {
            definition,
            timeout: cdk.Duration.hours(2),
            tracingEnabled: true,
            logs: {
                destination: new logs.LogGroup(this, 'StateMachineLogGroup', {
                    retention: logs.RetentionDays.ONE_WEEK,
                    removalPolicy: cdk.RemovalPolicy.DESTROY,
                }),
                level: stepfunctions.LogLevel.ALL,
            },
        });
        // Grant permissions to invoke Lambda functions
        lambdaFunctions.descriptionEnhancer.grantInvoke(stateMachine);
        lambdaFunctions.codeCreator.grantInvoke(stateMachine);
        lambdaFunctions.reviewRefine.grantInvoke(stateMachine);
        lambdaFunctions.finalizer.grantInvoke(stateMachine);
        return stateMachine;
    }
    createStateMachineDefinition(enhanceTask, createTask, reviewTask, finalizeTask) {
        // Initialize workflow
        const initializeWorkflow = new stepfunctions.Pass(this, 'InitializeWorkflow', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'userRequest.$': '$.userRequest',
                'currentIteration': 0,
                'maxIterations': 5,
                'status': 'initializing',
            },
        });
        // Prepare task execution
        const prepareTaskExecution = new stepfunctions.Pass(this, 'PrepareTaskExecution', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'currentIteration.$': '$.currentIteration',
                'maxIterations.$': '$.maxIterations',
                'artifacts': [],
                'reviews': [],
            },
        });
        // Execute tasks in parallel (simplified for this example)
        const executeTasksMap = new stepfunctions.Map(this, 'ExecuteTasksMap', {
            itemsPath: '$.taskPlan.tasks',
            maxConcurrency: 3,
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'task.$': '$$.Map.Item.Value',
                'iteration.$': '$.currentIteration',
            },
        });
        executeTasksMap.iterator(createTask);
        // Collect artifacts
        const collectArtifacts = new stepfunctions.Pass(this, 'CollectArtifacts', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'artifacts.$': '$[*].Payload',
                'currentIteration.$': '$.currentIteration',
                'maxIterations.$': '$.maxIterations',
            },
        });
        // Evaluate review results
        const evaluateReviewResults = new stepfunctions.Choice(this, 'EvaluateReviewResults')
            .when(stepfunctions.Condition.and(stepfunctions.Condition.isPresent('$.reviewResults[0].errors'), stepfunctions.Condition.numberLessThan('$.currentIteration', 5)), new stepfunctions.Pass(this, 'PrepareNextIteration', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'specification.$': '$.specification',
                'taskPlan.$': '$.taskPlan',
                'currentIteration.$': 'States.MathAdd($.currentIteration, 1)',
                'maxIterations.$': '$.maxIterations',
                'artifacts.$': '$.artifacts',
                'reviews.$': '$.reviewResults',
            },
        }).next(prepareTaskExecution))
            .when(stepfunctions.Condition.numberGreaterThanEquals('$.currentIteration', 5), new stepfunctions.Pass(this, 'MaxIterationsReached', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'status': 'max_iterations_reached',
                'message': 'Maximum iterations reached. Proceeding with current artifacts.',
                'artifacts.$': '$.artifacts',
                'reviews.$': '$.reviewResults',
            },
        }).next(finalizeTask))
            .otherwise(finalizeTask);
        // Workflow completed
        const workflowCompleted = new stepfunctions.Pass(this, 'WorkflowCompleted', {
            parameters: {
                'sessionId.$': '$.sessionId',
                'status': 'completed',
                'finalOutput.$': '$.finalOutput',
                'message': 'Workflow completed successfully',
                'completedAt.$': '$$.State.EnteredTime',
            },
        });
        // Chain the states together
        return initializeWorkflow
            .next(enhanceTask)
            .next(prepareTaskExecution)
            .next(executeTasksMap)
            .next(collectArtifacts)
            .next(reviewTask)
            .next(evaluateReviewResults)
            .next(workflowCompleted);
    }
    createApiGateway(descriptionEnhancerFunction, stateMachine) {
        // Create API Gateway
        const api = new apigateway.RestApi(this, 'WorkflowApi', {
            restApiName: 'AI Agent Workflow API',
            description: 'API for AI Agent Workflow System',
            defaultCorsPreflightOptions: {
                allowOrigins: apigateway.Cors.ALL_ORIGINS, // Restrict in production
                allowMethods: apigateway.Cors.ALL_METHODS,
                allowHeaders: ['Content-Type', 'X-Amz-Date', 'Authorization', 'X-Api-Key'],
            },
            deployOptions: {
                stageName: 'prod',
                tracingEnabled: true,
                loggingLevel: apigateway.MethodLoggingLevel.INFO,
                dataTraceEnabled: true,
                metricsEnabled: true,
            },
            cloudWatchRole: true,
        });
        // Create API Key for authentication
        const apiKey = new apigateway.ApiKey(this, 'WorkflowApiKey', {
            description: 'API Key for AI Agent Workflow System',
        });
        // Create usage plan
        const usagePlan = new apigateway.UsagePlan(this, 'WorkflowUsagePlan', {
            name: 'AI Agent Workflow Usage Plan',
            description: 'Usage plan for AI Agent Workflow API',
            throttle: {
                rateLimit: 100,
                burstLimit: 200,
            },
            quota: {
                limit: 10000,
                period: apigateway.Period.MONTH,
            },
        });
        usagePlan.addApiKey(apiKey);
        usagePlan.addApiStage({
            stage: api.deploymentStage,
        });
        // Create Lambda integration for description enhancement
        const descriptionEnhancerIntegration = new apigateway.LambdaIntegration(descriptionEnhancerFunction, {
            requestTemplates: {
                'application/json': JSON.stringify({
                    userRequest: '$input.json("$")',
                    sessionId: '$context.requestId',
                    userId: '$context.identity.apiKey',
                }),
            },
            integrationResponses: [
                {
                    statusCode: '200',
                    responseHeaders: {
                        'Access-Control-Allow-Origin': "'*'",
                    },
                },
                {
                    statusCode: '400',
                    selectionPattern: '.*"statusCode":400.*',
                    responseHeaders: {
                        'Access-Control-Allow-Origin': "'*'",
                    },
                },
                {
                    statusCode: '500',
                    selectionPattern: '.*"statusCode":500.*',
                    responseHeaders: {
                        'Access-Control-Allow-Origin': "'*'",
                    },
                },
            ],
        });
        // Create Step Functions integration for workflow execution
        const stepFunctionsRole = new iam.Role(this, 'StepFunctionsApiRole', {
            assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
            inlinePolicies: {
                StepFunctionsExecutionPolicy: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: ['states:StartExecution'],
                            resources: [stateMachine.stateMachineArn],
                        }),
                    ],
                }),
            },
        });
        const stepFunctionsIntegration = new apigateway.AwsIntegration({
            service: 'states',
            action: 'StartExecution',
            integrationHttpMethod: 'POST',
            options: {
                credentialsRole: stepFunctionsRole,
                requestTemplates: {
                    'application/json': JSON.stringify({
                        stateMachineArn: stateMachine.stateMachineArn,
                        input: JSON.stringify({
                            sessionId: '$context.requestId',
                            userRequest: '$input.json("$")',
                        }),
                    }),
                },
                integrationResponses: [
                    {
                        statusCode: '200',
                        responseHeaders: {
                            'Access-Control-Allow-Origin': "'*'",
                        },
                        responseTemplates: {
                            'application/json': JSON.stringify({
                                executionArn: '$input.json("$.executionArn")',
                                startDate: '$input.json("$.startDate")',
                                message: 'Workflow started successfully',
                            }),
                        },
                    },
                ],
            },
        });
        // Create API resources and methods
        const enhanceResource = api.root.addResource('enhance');
        enhanceResource.addMethod('POST', descriptionEnhancerIntegration, {
            apiKeyRequired: true,
            methodResponses: [
                { statusCode: '200' },
                { statusCode: '400' },
                { statusCode: '500' },
            ],
        });
        const workflowResource = api.root.addResource('workflow');
        workflowResource.addMethod('POST', stepFunctionsIntegration, {
            apiKeyRequired: true,
            methodResponses: [
                { statusCode: '200' },
                { statusCode: '400' },
                { statusCode: '500' },
            ],
        });
        // Add status endpoint
        const statusResource = api.root.addResource('status');
        const statusIntegration = new apigateway.MockIntegration({
            integrationResponses: [
                {
                    statusCode: '200',
                    responseTemplates: {
                        'application/json': JSON.stringify({
                            status: 'healthy',
                            timestamp: '$context.requestTime',
                            version: '1.0.0',
                        }),
                    },
                },
            ],
            requestTemplates: {
                'application/json': '{"statusCode": 200}',
            },
        });
        statusResource.addMethod('GET', statusIntegration, {
            methodResponses: [{ statusCode: '200' }],
        });
        return api;
    }
    createErrorNotificationTopic(encryptionKey) {
        const topic = new sns.Topic(this, 'ErrorNotificationTopic', {
            displayName: 'AI Agent Workflow Errors',
            masterKey: encryptionKey,
        });
        // Add email subscription if email is provided
        const notificationEmail = process.env.NOTIFICATION_EMAIL;
        if (notificationEmail) {
            topic.addSubscription(new snsSubscriptions.EmailSubscription(notificationEmail));
        }
        return topic;
    }
    createMonitoring(lambdaFunctions, stateMachine) {
        // Create CloudWatch alarms for Lambda functions
        Object.entries(lambdaFunctions).forEach(([name, func]) => {
            // Error rate alarm
            func.metricErrors({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}ErrorAlarm`, {
                threshold: 5,
                evaluationPeriods: 2,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
            // Duration alarm
            func.metricDuration({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}DurationAlarm`, {
                threshold: cdk.Duration.minutes(10).toMilliseconds(),
                evaluationPeriods: 2,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
            // Throttle alarm
            func.metricThrottles({
                period: cdk.Duration.minutes(5),
            }).createAlarm(this, `${name}ThrottleAlarm`, {
                threshold: 1,
                evaluationPeriods: 1,
                treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            });
        });
        // Create alarms for Step Functions
        stateMachine.metricFailed({
            period: cdk.Duration.minutes(5),
        }).createAlarm(this, 'StateMachineFailedAlarm', {
            threshold: 1,
            evaluationPeriods: 1,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });
        // Create alarms for DynamoDB
        this.workflowTable.metricThrottledRequests({
            period: cdk.Duration.minutes(5),
        }).createAlarm(this, 'DynamoDBThrottleAlarm', {
            threshold: 1,
            evaluationPeriods: 2,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        });
    }
    createOutputs() {
        new cdk.CfnOutput(this, 'ApiEndpoint', {
            value: this.api.url,
            description: 'API Gateway endpoint URL',
        });
        new cdk.CfnOutput(this, 'StateMachineArn', {
            value: this.stateMachine.stateMachineArn,
            description: 'Step Functions state machine ARN',
        });
        new cdk.CfnOutput(this, 'WorkflowTableName', {
            value: this.workflowTable.tableName,
            description: 'DynamoDB table name',
        });
        new cdk.CfnOutput(this, 'ArtifactsBucketName', {
            value: this.artifactsBucket.bucketName,
            description: 'S3 bucket name for artifacts',
        });
    }
}
exports.AIAgentWorkflowStack = AIAgentWorkflowStack;
//# sourceMappingURL=data:application/json;base64,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