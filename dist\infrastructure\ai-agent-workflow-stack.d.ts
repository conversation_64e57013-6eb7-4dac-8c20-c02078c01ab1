/**
 * Main CDK Stack for AI Agent Workflow System
 */
import * as cdk from 'aws-cdk-lib';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import { Construct } from 'constructs';
export declare class AIAgentWorkflowStack extends cdk.Stack {
    readonly workflowTable: dynamodb.Table;
    readonly artifactsBucket: s3.Bucket;
    readonly api: apigateway.RestApi;
    readonly stateMachine: stepfunctions.StateMachine;
    constructor(scope: Construct, id: string, props?: cdk.StackProps);
    private createDynamoDBTable;
    private createS3Bucket;
    private createLambdaFunctions;
    private createStateMachine;
    private createStateMachineDefinition;
    private createApiGateway;
    private createErrorNotificationTopic;
    private createMonitoring;
    private createOutputs;
}
