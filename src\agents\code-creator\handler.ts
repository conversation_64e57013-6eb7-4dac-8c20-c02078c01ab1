/**
 * Code Creator Agent
 * Generates code files based on specifications and individual tasks
 */

import { Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import { 
  CodeCreatorEvent, 
  CodeArtifact, 
  GeneratedFile, 
  CodeError, 
  CodeWarning,
  Task,
  DetailedSpecification 
} from '../../types';
import { createArtifactRecord, createTaskRecord } from '../../database/schemas';
import { invokeLLM } from '../../utils/llm-client';
import { logger } from '../../utils/logger';
import { validateGeneratedCode } from '../../utils/code-validator';

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));
const s3Client = new S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME!;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME!;

export const handler = async (
  event: CodeCreatorEvent,
  context: Context
): Promise<CodeArtifact> => {
  const requestId = context.awsRequestId;
  logger.info('Code Creator Agent started', { requestId, event });

  try {
    const { sessionId, specification, task, iteration } = event;

    // Update task status to in_progress
    await updateTaskStatus(sessionId, task.taskId, 'in_progress');

    // Generate code for the specific task
    const codeArtifact = await generateCodeForTask(
      specification, 
      task, 
      iteration, 
      requestId
    );

    // Validate generated code
    const validationResult = await validateGeneratedCode(codeArtifact.files);
    codeArtifact.errors.push(...validationResult.errors);
    codeArtifact.warnings.push(...validationResult.warnings);

    // Store artifacts in S3
    const s3Key = await storeArtifactsInS3(sessionId, task.taskId, codeArtifact);

    // Save artifact record to DynamoDB
    const artifactRecord = createArtifactRecord(sessionId, task.taskId, codeArtifact, s3Key);
    await dynamoClient.send(new PutCommand({
      TableName: TABLE_NAME,
      Item: artifactRecord,
    }));

    // Update task status based on errors
    const finalStatus = codeArtifact.errors.length > 0 ? 'failed' : 'completed';
    await updateTaskStatus(sessionId, task.taskId, finalStatus);

    logger.info('Code generation completed', { 
      requestId, 
      sessionId, 
      taskId: task.taskId,
      filesGenerated: codeArtifact.files.length,
      errorsCount: codeArtifact.errors.length,
      warningsCount: codeArtifact.warnings.length
    });

    return codeArtifact;

  } catch (error) {
    logger.error('Code Creator Agent failed', { requestId, error });
    
    // Update task status to failed
    if (event.task?.taskId) {
      await updateTaskStatus(event.sessionId, event.task.taskId, 'failed');
    }

    throw error;
  }
};

async function generateCodeForTask(
  specification: DetailedSpecification,
  task: Task,
  iteration: number,
  requestId: string
): Promise<CodeArtifact> {
  
  const codeGenerationPrompt = `
You are an expert software developer tasked with generating production-ready code for a specific task.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

CURRENT TASK:
${JSON.stringify(task, null, 2)}

ITERATION: ${iteration}

Your task is to generate complete, production-ready code files for this specific task. Consider:

1. QUALITY REQUIREMENTS:
   - Write clean, maintainable, and well-documented code
   - Follow best practices and design patterns
   - Include proper error handling and validation
   - Add comprehensive comments and documentation
   - Ensure type safety (TypeScript where applicable)

2. SECURITY CONSIDERATIONS:
   - Implement proper input validation
   - Use secure authentication and authorization
   - Protect against common vulnerabilities (XSS, SQL injection, etc.)
   - Follow OWASP guidelines

3. PERFORMANCE & SCALABILITY:
   - Optimize for performance
   - Consider caching strategies
   - Implement proper database indexing
   - Use efficient algorithms and data structures

4. TESTING:
   - Include unit tests for critical functions
   - Add integration tests where appropriate
   - Ensure good test coverage

5. DEPLOYMENT & INFRASTRUCTURE:
   - Include necessary configuration files
   - Add Docker files if needed
   - Include CI/CD pipeline configurations
   - Add monitoring and logging

Generate the following for this task:
- All necessary source code files
- Configuration files
- Test files
- Documentation files
- Any other required files

Return your response as a JSON object with the following structure:
{
  "taskId": "string",
  "files": [
    {
      "path": "string",
      "content": "string",
      "type": "source|config|documentation|test",
      "language": "string"
    }
  ],
  "errors": [],
  "warnings": [],
  "metadata": {
    "generatedAt": "string",
    "agent": "CodeCreator",
    "version": "string"
  }
}

Focus specifically on the requirements of the current task while ensuring compatibility with the overall project specification.
`;

  try {
    const response = await invokeLLM(codeGenerationPrompt, {
      temperature: 0.3,
      maxTokens: 8000,
      requestId,
    });

    const result = JSON.parse(response);
    
    // Validate and enhance the generated code
    const codeArtifact: CodeArtifact = {
      taskId: task.taskId,
      files: result.files || [],
      errors: result.errors || [],
      warnings: result.warnings || [],
      metadata: {
        generatedAt: new Date().toISOString(),
        agent: 'CodeCreator',
        version: '1.0.0',
        ...result.metadata,
      },
    };

    // Post-process generated files
    codeArtifact.files = await postProcessGeneratedFiles(codeArtifact.files, specification, task);

    return codeArtifact;

  } catch (error) {
    logger.error('Code generation failed', { requestId, taskId: task.taskId, error });
    
    return {
      taskId: task.taskId,
      files: [],
      errors: [{
        severity: 'critical',
        message: `Code generation failed: ${error instanceof Error ? error.message : String(error)}`,
        rule: 'generation_error',
      }],
      warnings: [],
      metadata: {
        generatedAt: new Date().toISOString(),
        agent: 'CodeCreator',
        version: '1.0.0',
      },
    };
  }
}

async function postProcessGeneratedFiles(
  files: GeneratedFile[],
  specification: DetailedSpecification,
  task: Task
): Promise<GeneratedFile[]> {
  
  return files.map(file => {
    // Add file headers and metadata
    let content = file.content;
    
    if (file.type === 'source' && (file.language === 'typescript' || file.language === 'javascript')) {
      const header = `/**
 * ${file.path}
 * Generated by AI Agent Workflow System
 * Task: ${task.taskName}
 * Project: ${specification.projectName}
 * Generated: ${new Date().toISOString()}
 */

`;
      content = header + content;
    }

    // Add proper imports and dependencies
    if (file.language === 'typescript' && file.type === 'source') {
      content = addTypeScriptImports(content, specification);
    }

    return {
      ...file,
      content,
    };
  });
}

function addTypeScriptImports(content: string, specification: DetailedSpecification): string {
  // Add common imports based on the tech stack
  const imports: string[] = [];
  
  if (specification.techStack.frontend.framework === 'React') {
    if (content.includes('React') && !content.includes("import React")) {
      imports.push("import React from 'react';");
    }
  }
  
  if (specification.techStack.backend.framework === 'Express.js') {
    if (content.includes('express') && !content.includes("import express")) {
      imports.push("import express from 'express';");
    }
  }

  if (imports.length > 0) {
    return imports.join('\n') + '\n\n' + content;
  }

  return content;
}

async function storeArtifactsInS3(
  sessionId: string, 
  taskId: string, 
  artifact: CodeArtifact
): Promise<string> {
  
  const s3Key = `sessions/${sessionId}/tasks/${taskId}/artifacts/${Date.now()}.json`;
  
  const artifactData = {
    ...artifact,
    storedAt: new Date().toISOString(),
  };

  await s3Client.send(new PutObjectCommand({
    Bucket: ARTIFACTS_BUCKET,
    Key: s3Key,
    Body: JSON.stringify(artifactData, null, 2),
    ContentType: 'application/json',
    Metadata: {
      sessionId,
      taskId,
      agent: 'CodeCreator',
    },
  }));

  // Store individual files as well
  for (const file of artifact.files) {
    const fileKey = `sessions/${sessionId}/tasks/${taskId}/files/${file.path}`;
    await s3Client.send(new PutObjectCommand({
      Bucket: ARTIFACTS_BUCKET,
      Key: fileKey,
      Body: file.content,
      ContentType: getContentType(file.language),
      Metadata: {
        sessionId,
        taskId,
        fileType: file.type,
        language: file.language,
      },
    }));
  }

  return s3Key;
}

function getContentType(language: string): string {
  const contentTypes: Record<string, string> = {
    'typescript': 'text/typescript',
    'javascript': 'text/javascript',
    'html': 'text/html',
    'css': 'text/css',
    'json': 'application/json',
    'yaml': 'text/yaml',
    'markdown': 'text/markdown',
    'python': 'text/x-python',
    'sql': 'text/sql',
  };

  return contentTypes[language] || 'text/plain';
}

async function updateTaskStatus(
  sessionId: string, 
  taskId: string, 
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
): Promise<void> {
  
  await dynamoClient.send(new UpdateCommand({
    TableName: TABLE_NAME,
    Key: {
      PK: `SESSION#${sessionId}`,
      SK: `TASK#${taskId}`,
    },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, completedAt = :completedAt',
    ExpressionAttributeNames: {
      '#status': 'status',
    },
    ExpressionAttributeValues: {
      ':status': status,
      ':updatedAt': new Date().toISOString(),
      ':completedAt': (status === 'completed' || status === 'failed') ? new Date().toISOString() : null,
    },
  }));
}
