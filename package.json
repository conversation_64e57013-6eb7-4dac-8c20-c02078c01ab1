{"name": "ai-agent-workflow-serverless", "version": "1.0.0", "description": "Serverless multi-agent workflow for creating websites, backends, and chatbots", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy", "destroy": "cdk destroy", "synth": "cdk synth", "bootstrap": "cdk bootstrap", "lint": "eslint . --ext .ts", "format": "prettier --write ."}, "keywords": ["aws", "lambda", "step-functions", "ai-agents", "serverless", "cdk"], "author": "AI Agent Workflow System", "license": "MIT", "devDependencies": {"@types/aws-lambda": "^8.10.130", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "aws-cdk": "^2.108.0", "eslint": "^8.53.0", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.454.0", "@aws-sdk/client-s3": "^3.454.0", "@aws-sdk/client-stepfunctions": "^3.454.0", "@aws-sdk/lib-dynamodb": "^3.454.0", "@google/generative-ai": "^0.15.0", "aws-cdk-lib": "^2.108.0", "aws-lambda": "^1.0.7", "constructs": "^10.3.0", "uuid": "^9.0.1", "@types/uuid": "^9.0.7", "archiver": "^6.0.1", "@types/archiver": "^6.0.2"}}