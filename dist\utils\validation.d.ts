/**
 * Validation utilities for user requests and generated content
 */
import { UserRequest, GeneratedFile } from '../types';
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
export declare function validateUserRequest(userRequest: UserRequest): ValidationResult;
export declare function validateGeneratedFile(file: GeneratedFile): ValidationResult;
export declare function validateCodeSyntax(file: GeneratedFile): ValidationResult;
export declare function validateProjectStructure(files: GeneratedFile[]): ValidationResult;
