/**
 * Centralized logging utility
 */
export interface LogContext {
    requestId?: string;
    sessionId?: string;
    taskId?: string;
    agent?: string;
    [key: string]: any;
}
export declare class Logger {
    private serviceName;
    constructor(serviceName?: string);
    private formatMessage;
    info(message: string, context?: LogContext): void;
    warn(message: string, context?: LogContext): void;
    error(message: string, context?: LogContext): void;
    debug(message: string, context?: LogContext): void;
    trace(message: string, context?: LogContext): void;
}
export declare const logger: Logger;
export declare function createLogger(serviceName: string): Logger;
export declare function logExecutionTime<T>(operation: string, fn: () => Promise<T>, context?: LogContext): Promise<T>;
export declare function logWithMetrics(message: string, metrics: Record<string, number>, context?: LogContext): void;
export declare function createRequestLogger(requestId: string): {
    info: (message: string, context?: LogContext) => void;
    warn: (message: string, context?: LogContext) => void;
    error: (message: string, context?: LogContext) => void;
    debug: (message: string, context?: LogContext) => void;
};
