/**
 * Description Enhancer Agent
 * Takes user input and creates detailed specifications and task plans
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import { 
  UserRequest, 
  DetailedSpecification, 
  TaskPlan, 
  WorkflowState,
  DescriptionEnhancerEvent 
} from '../../types';
import { createWorkflowStateRecord, createUserSessionRecord } from '../../database/schemas';
import { invokeLLM } from '../../utils/llm-client';
import { logger } from '../../utils/logger';
import { validateUserRequest } from '../../utils/validation';

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME!;

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const requestId = context.awsRequestId;
  logger.info('Description Enhancer Agent started', { requestId, event });

  try {
    // Parse and validate input
    const body = JSON.parse(event.body || '{}');
    const userRequest: UserRequest = body.userRequest;
    const sessionId = body.sessionId || uuidv4();
    const userId = body.userId || 'anonymous';

    // Validate user request
    const validationResult = validateUserRequest(userRequest);
    if (!validationResult.isValid) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
          error: 'Invalid user request',
          details: validationResult.errors,
        }),
      };
    }

    // Create user session record
    const userSessionRecord = createUserSessionRecord(userId, sessionId, userRequest);
    await dynamoClient.send(new PutCommand({
      TableName: TABLE_NAME,
      Item: userSessionRecord,
    }));

    // Initialize workflow state
    const workflowState: WorkflowState = {
      sessionId,
      currentIteration: 0,
      maxIterations: 5,
      status: 'enhancing',
      artifacts: [],
      reviews: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Enhance the description using LLM
    const enhancementResult = await enhanceDescription(userRequest, requestId);
    
    workflowState.specification = enhancementResult.specification;
    workflowState.taskPlan = enhancementResult.taskPlan;
    workflowState.status = 'creating';

    // Save workflow state to DynamoDB
    const workflowRecord = createWorkflowStateRecord(sessionId, workflowState);
    await dynamoClient.send(new PutCommand({
      TableName: TABLE_NAME,
      Item: workflowRecord,
    }));

    logger.info('Description enhancement completed', { 
      requestId, 
      sessionId, 
      tasksCount: enhancementResult.taskPlan.tasks.length 
    });

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        sessionId,
        specification: enhancementResult.specification,
        taskPlan: enhancementResult.taskPlan,
        status: 'enhancement_completed',
        message: 'Description enhanced successfully. Code generation will begin shortly.',
      }),
    };

  } catch (error) {
    logger.error('Description Enhancer Agent failed', { requestId, error });
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to enhance description',
        requestId,
      }),
    };
  }
};

async function enhanceDescription(
  userRequest: UserRequest, 
  requestId: string
): Promise<{ specification: DetailedSpecification; taskPlan: TaskPlan }> {
  
  const enhancementPrompt = `
You are an expert software architect and project manager. Your task is to transform a user's high-level description into a comprehensive, detailed specification and task plan for building a production-ready application.

User Request:
${JSON.stringify(userRequest, null, 2)}

Please analyze this request and create:

1. A DETAILED SPECIFICATION that includes:
   - Project name and comprehensive description
   - Complete feature list with priorities
   - Recommended tech stack (frontend, backend, database, infrastructure)
   - System architecture and components
   - Data models with relationships
   - API endpoints specification
   - UI components structure
   - Chatbot flows (if applicable)
   - Deployment configuration

2. A COMPREHENSIVE TASK PLAN that breaks down the work into specific, actionable tasks:
   - Each task should be atomic and completable by a code generation agent
   - Include proper dependencies between tasks
   - Prioritize tasks logically (infrastructure → backend → frontend → integration)
   - Estimate complexity and duration
   - Categorize tasks by type (frontend, backend, chatbot, infrastructure, documentation)

Requirements:
- Make the specification production-ready with proper error handling, security, and scalability considerations
- Include modern best practices and patterns
- Consider CI/CD, testing, monitoring, and documentation
- Ensure the task plan is executable and well-structured
- Include specific file names and code structure in task descriptions

Return your response as a JSON object with 'specification' and 'taskPlan' properties that match the TypeScript interfaces provided.
`;

  try {
    const response = await invokeLLM(enhancementPrompt, {
      temperature: 0.7,
      maxTokens: 4000,
      requestId,
    });

    const result = JSON.parse(response);
    
    // Validate and enhance the response
    const specification = validateAndEnhanceSpecification(result.specification, userRequest);
    const taskPlan = validateAndEnhanceTaskPlan(result.taskPlan, specification);

    return { specification, taskPlan };

  } catch (error) {
    logger.error('LLM enhancement failed', { requestId, error });
    throw new Error('Failed to enhance description with LLM');
  }
}

function validateAndEnhanceSpecification(
  spec: any, 
  userRequest: UserRequest
): DetailedSpecification {
  // Add validation and enhancement logic
  const enhanced: DetailedSpecification = {
    projectName: spec.projectName || generateProjectName(userRequest.userDescription),
    description: spec.description || userRequest.userDescription,
    features: spec.features || [],
    techStack: spec.techStack || getDefaultTechStack(userRequest),
    architecture: spec.architecture || getDefaultArchitecture(),
    dataModels: spec.dataModels || [],
    apiEndpoints: spec.apiEndpoints || [],
    uiComponents: spec.uiComponents || [],
    chatbotFlows: spec.chatbotFlows,
    deploymentConfig: spec.deploymentConfig || getDefaultDeploymentConfig(),
    metadata: {
      createdAt: new Date().toISOString(),
      version: '1.0.0',
      estimatedComplexity: estimateComplexity(spec),
    },
  };

  return enhanced;
}

function validateAndEnhanceTaskPlan(
  taskPlan: any, 
  specification: DetailedSpecification
): TaskPlan {
  const tasks = (taskPlan.tasks || []).map((task: any, index: number) => ({
    taskId: task.taskId || uuidv4(),
    taskName: task.taskName || `Task ${index + 1}`,
    description: task.description || '',
    agent: task.agent || 'CodeCreator',
    dependencies: task.dependencies || [],
    status: 'pending',
    priority: task.priority || index + 1,
    estimatedDuration: task.estimatedDuration || 30,
    metadata: {
      category: task.metadata?.category || 'backend',
      complexity: task.metadata?.complexity || 'medium',
      files: task.metadata?.files || [],
    },
  }));

  return {
    tasks,
    totalEstimatedDuration: tasks.reduce((sum: number, task: any) => sum + (task.estimatedDuration || 0), 0),
    dependencies: tasks.reduce((deps: Record<string, string[]>, task: any) => {
      deps[task.taskId] = task.dependencies;
      return deps;
    }, {} as Record<string, string[]>),
  };
}

function generateProjectName(description: string): string {
  // Simple project name generation logic
  const words = description.toLowerCase().split(' ').slice(0, 3);
  return words.join('-') + '-app';
}

function getDefaultTechStack(userRequest: UserRequest): any {
  return {
    frontend: {
      framework: userRequest.preferences?.techStack?.includes('react') ? 'React' : 'Next.js',
      language: 'TypeScript',
      styling: 'Tailwind CSS',
      stateManagement: 'Zustand',
    },
    backend: {
      framework: 'Express.js',
      language: 'Node.js',
      database: 'PostgreSQL',
      authentication: 'JWT',
    },
    infrastructure: {
      cloud: 'AWS',
      deployment: 'Serverless',
      monitoring: 'CloudWatch',
    },
  };
}

function getDefaultArchitecture(): any {
  return {
    type: 'serverless',
    components: [],
    integrations: [],
  };
}

function getDefaultDeploymentConfig(): any {
  return {
    environment: 'development',
    environmentVariables: {},
    scaling: {
      minInstances: 1,
      maxInstances: 10,
    },
  };
}

function estimateComplexity(spec: any): 'low' | 'medium' | 'high' {
  const featureCount = (spec.features || []).length;
  const modelCount = (spec.dataModels || []).length;
  const endpointCount = (spec.apiEndpoints || []).length;
  
  const totalComplexity = featureCount + modelCount + endpointCount;
  
  if (totalComplexity < 10) return 'low';
  if (totalComplexity < 25) return 'medium';
  return 'high';
}
