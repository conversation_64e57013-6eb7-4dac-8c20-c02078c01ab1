/**
 * Review & Refine Agent
 * Analyzes code, finds errors, and suggests improvements
 */

import { Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { 
  ReviewRefineEvent, 
  ReviewResult, 
  CodeArtifact, 
  CodeError, 
  Suggestion,
  SecurityIssue,
  PerformanceIssue,
  DetailedSpecification,
  TaskPlan,
  Task
} from '../../types';
import { createReviewRecord } from '../../database/schemas';
import { invokeLLM } from '../../utils/llm-client';
import { logger } from '../../utils/logger';
import { runStaticAnalysis } from '../../utils/static-analysis';
import { runSecurityScan } from '../../utils/security-scanner';

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));
const s3Client = new S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME!;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME!;

export const handler = async (
  event: ReviewRefineEvent,
  context: Context
): Promise<ReviewResult[]> => {
  const requestId = context.awsRequestId;
  logger.info('Review & Refine Agent started', { requestId, event });

  try {
    const { sessionId, specification, taskPlan, artifacts, iteration } = event;

    // Fetch all artifacts from S3
    const allArtifacts = await fetchAllArtifacts(sessionId, artifacts);

    // Perform comprehensive review
    const reviewResults: ReviewResult[] = [];

    for (const artifact of allArtifacts) {
      const review = await performComprehensiveReview(
        artifact,
        specification,
        taskPlan,
        allArtifacts,
        iteration,
        requestId
      );

      reviewResults.push(review);

      // Save review record to DynamoDB
      const reviewRecord = createReviewRecord(
        sessionId, 
        artifact.taskId, 
        review, 
        'ReviewRefineAgent'
      );
      await dynamoClient.send(new PutCommand({
        TableName: TABLE_NAME,
        Item: reviewRecord,
      }));
    }

    // Perform cross-artifact analysis
    const crossArtifactReview = await performCrossArtifactAnalysis(
      allArtifacts,
      specification,
      taskPlan,
      requestId
    );

    if (crossArtifactReview) {
      reviewResults.push(crossArtifactReview);
      
      const crossReviewRecord = createReviewRecord(
        sessionId, 
        'cross-artifact-analysis', 
        crossArtifactReview, 
        'ReviewRefineAgent'
      );
      await dynamoClient.send(new PutCommand({
        TableName: TABLE_NAME,
        Item: crossReviewRecord,
      }));
    }

    logger.info('Review & Refine completed', { 
      requestId, 
      sessionId, 
      reviewsCount: reviewResults.length,
      totalErrors: reviewResults.reduce((sum, r) => sum + r.errors.length, 0),
      totalSuggestions: reviewResults.reduce((sum, r) => sum + r.suggestions.length, 0)
    });

    return reviewResults;

  } catch (error) {
    logger.error('Review & Refine Agent failed', { requestId, error });
    throw error;
  }
};

async function fetchAllArtifacts(
  sessionId: string, 
  artifactReferences: CodeArtifact[]
): Promise<CodeArtifact[]> {
  
  const artifacts: CodeArtifact[] = [];

  // Query DynamoDB for all artifacts in this session
  const queryResult = await dynamoClient.send(new QueryCommand({
    TableName: TABLE_NAME,
    KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
    ExpressionAttributeValues: {
      ':pk': `SESSION#${sessionId}`,
      ':sk': 'ARTIFACT#',
    },
  }));

  // Fetch artifact details from S3
  for (const item of queryResult.Items || []) {
    try {
      const s3Response = await s3Client.send(new GetObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: item.s3Key,
      }));

      const artifactData = JSON.parse(await s3Response.Body?.transformToString() || '{}');
      artifacts.push(artifactData);
    } catch (error) {
      logger.error('Failed to fetch artifact from S3', { s3Key: item.s3Key, error });
    }
  }

  return artifacts;
}

async function performComprehensiveReview(
  artifact: CodeArtifact,
  specification: DetailedSpecification,
  taskPlan: TaskPlan,
  allArtifacts: CodeArtifact[],
  iteration: number,
  requestId: string
): Promise<ReviewResult> {
  
  // Run static analysis
  const staticAnalysisResults = await runStaticAnalysis(artifact.files);
  
  // Run security scan
  const securityResults = await runSecurityScan(artifact.files);
  
  // Perform AI-powered code review
  const aiReviewResults = await performAICodeReview(
    artifact,
    specification,
    taskPlan,
    allArtifacts,
    iteration,
    requestId
  );

  // Combine all results
  const review: ReviewResult = {
    taskId: artifact.taskId,
    overallQuality: determineOverallQuality(
      staticAnalysisResults.errors,
      securityResults.issues,
      aiReviewResults.errors
    ),
    errors: [
      ...staticAnalysisResults.errors,
      ...securityResults.errors,
      ...aiReviewResults.errors,
    ],
    suggestions: [
      ...staticAnalysisResults.suggestions,
      ...aiReviewResults.suggestions,
    ],
    securityIssues: securityResults.issues,
    performanceIssues: aiReviewResults.performanceIssues,
    updatedSpecDelta: aiReviewResults.updatedSpecDelta,
    updatedTasks: aiReviewResults.updatedTasks,
  };

  return review;
}

async function performAICodeReview(
  artifact: CodeArtifact,
  specification: DetailedSpecification,
  taskPlan: TaskPlan,
  allArtifacts: CodeArtifact[],
  iteration: number,
  requestId: string
): Promise<{
  errors: CodeError[];
  suggestions: Suggestion[];
  performanceIssues: PerformanceIssue[];
  updatedSpecDelta?: Partial<DetailedSpecification>;
  updatedTasks?: Task[];
}> {
  
  const reviewPrompt = `
You are an expert code reviewer and software architect. Your task is to perform a comprehensive review of the generated code and provide detailed feedback.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

TASK PLAN:
${JSON.stringify(taskPlan, null, 2)}

CURRENT ARTIFACT:
${JSON.stringify(artifact, null, 2)}

ALL ARTIFACTS CONTEXT:
${JSON.stringify(allArtifacts.map(a => ({ taskId: a.taskId, fileCount: a.files.length, errors: a.errors.length })), null, 2)}

ITERATION: ${iteration}

Please perform a thorough review focusing on:

1. CODE QUALITY:
   - Adherence to best practices and design patterns
   - Code readability and maintainability
   - Proper error handling and validation
   - Type safety and null checks
   - Documentation and comments

2. ARCHITECTURE & DESIGN:
   - Consistency with the overall specification
   - Proper separation of concerns
   - Scalability and extensibility
   - Integration with other components

3. SECURITY:
   - Input validation and sanitization
   - Authentication and authorization
   - Protection against common vulnerabilities
   - Secure data handling

4. PERFORMANCE:
   - Efficient algorithms and data structures
   - Proper caching strategies
   - Database query optimization
   - Resource usage optimization

5. TESTING:
   - Test coverage and quality
   - Edge case handling
   - Integration test scenarios

6. DEPLOYMENT & OPERATIONS:
   - Configuration management
   - Monitoring and logging
   - Error tracking and alerting

Based on your review, provide:

1. ERRORS: Critical issues that must be fixed
2. SUGGESTIONS: Improvements and optimizations
3. PERFORMANCE ISSUES: Performance-related concerns
4. UPDATED SPECIFICATION: Any changes needed to the original specification
5. UPDATED TASKS: New or modified tasks based on your findings

Return your response as a JSON object with the following structure:
{
  "errors": [
    {
      "severity": "critical|major|minor",
      "message": "string",
      "file": "string",
      "line": number,
      "rule": "string"
    }
  ],
  "suggestions": [
    {
      "type": "improvement|optimization|refactor|feature",
      "description": "string",
      "impact": "high|medium|low",
      "effort": "high|medium|low"
    }
  ],
  "performanceIssues": [
    {
      "type": "memory|cpu|network|database",
      "description": "string",
      "impact": "high|medium|low",
      "recommendation": "string"
    }
  ],
  "updatedSpecDelta": {
    // Only include fields that need to be updated
  },
  "updatedTasks": [
    // New or modified tasks
  ]
}

Be thorough but constructive in your feedback. Focus on actionable improvements.
`;

  try {
    const response = await invokeLLM(reviewPrompt, {
      temperature: 0.2,
      maxTokens: 6000,
      requestId,
    });

    const result = JSON.parse(response);
    
    return {
      errors: result.errors || [],
      suggestions: result.suggestions || [],
      performanceIssues: result.performanceIssues || [],
      updatedSpecDelta: result.updatedSpecDelta,
      updatedTasks: result.updatedTasks,
    };

  } catch (error) {
    logger.error('AI code review failed', { requestId, taskId: artifact.taskId, error });
    
    return {
      errors: [{
        severity: 'major',
        message: `AI code review failed: ${error instanceof Error ? error.message : String(error)}`,
        rule: 'review_error',
      }],
      suggestions: [],
      performanceIssues: [],
    };
  }
}

async function performCrossArtifactAnalysis(
  allArtifacts: CodeArtifact[],
  specification: DetailedSpecification,
  taskPlan: TaskPlan,
  requestId: string
): Promise<ReviewResult | null> {
  
  if (allArtifacts.length < 2) {
    return null; // No cross-artifact analysis needed for single artifact
  }

  const crossAnalysisPrompt = `
You are performing a cross-artifact analysis to ensure consistency and integration across all generated code components.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ALL ARTIFACTS:
${JSON.stringify(allArtifacts.map(a => ({
  taskId: a.taskId,
  files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
  errors: a.errors,
})), null, 2)}

Analyze the artifacts for:

1. INTEGRATION CONSISTENCY:
   - API contracts between frontend and backend
   - Data model consistency across components
   - Import/export compatibility
   - Configuration alignment

2. ARCHITECTURAL COHERENCE:
   - Consistent design patterns
   - Proper dependency management
   - Service boundaries and interfaces
   - Data flow consistency

3. CROSS-CUTTING CONCERNS:
   - Error handling strategies
   - Logging and monitoring
   - Security implementations
   - Performance optimizations

Provide feedback on integration issues and overall system coherence.

Return your response as a JSON object following the same structure as individual artifact reviews.
`;

  try {
    const response = await invokeLLM(crossAnalysisPrompt, {
      temperature: 0.2,
      maxTokens: 4000,
      requestId,
    });

    const result = JSON.parse(response);
    
    return {
      taskId: 'cross-artifact-analysis',
      overallQuality: result.overallQuality || 'good',
      errors: result.errors || [],
      suggestions: result.suggestions || [],
      securityIssues: result.securityIssues || [],
      performanceIssues: result.performanceIssues || [],
      updatedSpecDelta: result.updatedSpecDelta,
      updatedTasks: result.updatedTasks,
    };

  } catch (error) {
    logger.error('Cross-artifact analysis failed', { requestId, error });
    return null;
  }
}

function determineOverallQuality(
  staticErrors: CodeError[],
  securityIssues: SecurityIssue[],
  aiErrors: CodeError[]
): 'excellent' | 'good' | 'fair' | 'poor' {
  
  const criticalIssues = [
    ...staticErrors.filter(e => e.severity === 'critical'),
    ...securityIssues.filter(s => s.severity === 'critical'),
    ...aiErrors.filter(e => e.severity === 'critical'),
  ].length;

  const majorIssues = [
    ...staticErrors.filter(e => e.severity === 'major'),
    ...securityIssues.filter(s => s.severity === 'high'),
    ...aiErrors.filter(e => e.severity === 'major'),
  ].length;

  if (criticalIssues > 0) return 'poor';
  if (majorIssues > 3) return 'fair';
  if (majorIssues > 0) return 'good';
  return 'excellent';
}
