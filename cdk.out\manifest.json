{"version": "44.0.0", "artifacts": {"AIAgentWorkflowStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "AIAgentWorkflowStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "AIAgentWorkflowStack": {"type": "aws:cloudformation:stack", "environment": "aws://337909760884/us-east-1", "properties": {"templateFile": "AIAgentWorkflowStack.template.json", "terminationProtection": false, "tags": {"CostCenter": "Development", "Environment": "development", "ManagedBy": "CDK", "Owner": "AIAgentWorkflowSystem", "Project": "AIAgentWorkflow", "Repository": "ai-agent-workflow-serverless"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-deploy-role-337909760884-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-cfn-exec-role-337909760884-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-337909760884-us-east-1/6e50ed17aa74ca9ddb38fd7d207a2af9eeb47dbca8d5f0aa239d61ddcdd47fce.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["AIAgentWorkflowStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::337909760884:role/cdk-hnb659fds-lookup-role-337909760884-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["AIAgentWorkflowStack.assets"], "metadata": {"/AIAgentWorkflowStack": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "CostCenter", "Value": "Development"}, {"Key": "Environment", "Value": "development"}, {"Key": "ManagedBy", "Value": "CDK"}, {"Key": "Owner", "Value": "AIAgentWorkflowSystem"}, {"Key": "Project", "Value": "AIAgentWorkflow"}, {"Key": "Repository", "Value": "ai-agent-workflow-serverless"}]}], "/AIAgentWorkflowStack/EncryptionKey/Resource": [{"type": "aws:cdk:logicalId", "data": "EncryptionKey1B843E66"}], "/AIAgentWorkflowStack/WorkflowTable": [{"type": "aws:cdk:hasPhysicalName", "data": {"Ref": "WorkflowTable0AE28607"}}], "/AIAgentWorkflowStack/WorkflowTable/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowTable0AE28607"}], "/AIAgentWorkflowStack/ArtifactsBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "ArtifactsBucket2AAC5544"}], "/AIAgentWorkflowStack/ArtifactsBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "ArtifactsBucketPolicy852CB646"}], "/AIAgentWorkflowStack/ArtifactsBucket/AutoDeleteObjectsCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "ArtifactsBucketAutoDeleteObjectsCustomResource0E3B4320"}], "/AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider": [{"type": "aws:cdk:is-custom-resource-handler-customResourceProvider", "data": true}], "/AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"}], "/AIAgentWorkflowStack/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F"}], "/AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "DescriptionEnhancerFunctionServiceRole186883E6"}], "/AIAgentWorkflowStack/DescriptionEnhancerFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "DescriptionEnhancerFunctionServiceRoleDefaultPolicyACC2A16A"}], "/AIAgentWorkflowStack/DescriptionEnhancerFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "DescriptionEnhancerFunction61F18C4F"}], "/AIAgentWorkflowStack/DescriptionEnhancerFunction/LogRetention/Resource": [{"type": "aws:cdk:logicalId", "data": "DescriptionEnhancerFunctionLogRetentionA35D1A77"}], "/AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB"}], "/AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB"}], "/AIAgentWorkflowStack/LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8a/Resource": [{"type": "aws:cdk:logicalId", "data": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A"}], "/AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "CodeCreatorFunctionServiceRole9BB85716"}], "/AIAgentWorkflowStack/CodeCreatorFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "CodeCreatorFunctionServiceRoleDefaultPolicyCEF966DD"}], "/AIAgentWorkflowStack/CodeCreatorFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "CodeCreatorFunction11132470"}], "/AIAgentWorkflowStack/CodeCreatorFunction/LogRetention/Resource": [{"type": "aws:cdk:logicalId", "data": "CodeCreatorFunctionLogRetentionCB5EF515"}], "/AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ReviewRefineFunctionServiceRole3AA53DF4"}], "/AIAgentWorkflowStack/ReviewRefineFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "ReviewRefineFunctionServiceRoleDefaultPolicy0BD1FFFC"}], "/AIAgentWorkflowStack/ReviewRefineFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "ReviewRefineFunction5325599E"}], "/AIAgentWorkflowStack/ReviewRefineFunction/LogRetention/Resource": [{"type": "aws:cdk:logicalId", "data": "ReviewRefineFunctionLogRetentionD96E5990"}], "/AIAgentWorkflowStack/FinalizerFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "FinalizerFunctionServiceRole7963A895"}], "/AIAgentWorkflowStack/FinalizerFunction/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "FinalizerFunctionServiceRoleDefaultPolicyA64092A9"}], "/AIAgentWorkflowStack/FinalizerFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "FinalizerFunctionCCBC19F3"}], "/AIAgentWorkflowStack/FinalizerFunction/LogRetention/Resource": [{"type": "aws:cdk:logicalId", "data": "FinalizerFunctionLogRetention0FC5B818"}], "/AIAgentWorkflowStack/StateMachineLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "StateMachineLogGroup15B91BCB"}], "/AIAgentWorkflowStack/WorkflowStateMachine/Role/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowStateMachineRoleE0545793"}], "/AIAgentWorkflowStack/WorkflowStateMachine/Role/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowStateMachineRoleDefaultPolicyDAB74B4D"}], "/AIAgentWorkflowStack/WorkflowStateMachine/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowStateMachine67D94DDA"}], "/AIAgentWorkflowStack/WorkflowApi/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiB9CC683F"}], "/AIAgentWorkflowStack/WorkflowApi/CloudWatchRole/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiCloudWatchRole89411D9E"}], "/AIAgentWorkflowStack/WorkflowApi/Account": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiAccount9EF7FB09"}], "/AIAgentWorkflowStack/WorkflowApi/Deployment/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiDeployment5BD8B8389e8b614aa6266f1c20e9216b9cc6a009"}], "/AIAgentWorkflowStack/WorkflowApi/DeploymentStage.prod/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiDeploymentStageprod72BAE0C9"}], "/AIAgentWorkflowStack/WorkflowApi/Endpoint": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiEndpointC0DC4592"}], "/AIAgentWorkflowStack/WorkflowApi/Default/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiOPTIONS79A098FC"}], "/AIAgentWorkflowStack/WorkflowApi/Default/enhance/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApienhanceCF77A6D6"}], "/AIAgentWorkflowStack/WorkflowApi/Default/enhance/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApienhanceOPTIONS729F011E"}], "/AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance": [{"type": "aws:cdk:logicalId", "data": "WorkflowApienhancePOSTApiPermissionAIAgentWorkflowStackWorkflowApi55692A50POSTenhanceCE03D6B0"}], "/AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/ApiPermission.Test.AIAgentWorkflowStackWorkflowApi55692A50.POST..enhance": [{"type": "aws:cdk:logicalId", "data": "WorkflowApienhancePOSTApiPermissionTestAIAgentWorkflowStackWorkflowApi55692A50POSTenhanceA0A0F77A"}], "/AIAgentWorkflowStack/WorkflowApi/Default/enhance/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApienhancePOST285C704C"}], "/AIAgentWorkflowStack/WorkflowApi/Default/workflow/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiworkflow843BCBF5"}], "/AIAgentWorkflowStack/WorkflowApi/Default/workflow/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiworkflowOPTIONS5F08821B"}], "/AIAgentWorkflowStack/WorkflowApi/Default/workflow/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiworkflowPOST56F4BBCB"}], "/AIAgentWorkflowStack/WorkflowApi/Default/status/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApistatus89EB6A98"}], "/AIAgentWorkflowStack/WorkflowApi/Default/status/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApistatusOPTIONSC24293F2"}], "/AIAgentWorkflowStack/WorkflowApi/Default/status/GET/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApistatusGET50C9E4F5"}], "/AIAgentWorkflowStack/WorkflowApiKey/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowApiKeyD3ED92A1"}], "/AIAgentWorkflowStack/WorkflowUsagePlan/Resource": [{"type": "aws:cdk:logicalId", "data": "WorkflowUsagePlanCB7C6F4A"}], "/AIAgentWorkflowStack/WorkflowUsagePlan/UsagePlanKeyResource:AIAgentWorkflowStackWorkflowApiKey9C182D45": [{"type": "aws:cdk:logicalId", "data": "WorkflowUsagePlanUsagePlanKeyResourceAIAgentWorkflowStackWorkflowApiKey9C182D459B14483E"}], "/AIAgentWorkflowStack/StepFunctionsApiRole/Resource": [{"type": "aws:cdk:logicalId", "data": "StepFunctionsApiRole3B314676"}], "/AIAgentWorkflowStack/ErrorNotificationTopic/Resource": [{"type": "aws:cdk:logicalId", "data": "ErrorNotificationTopic0DACB6F3"}], "/AIAgentWorkflowStack/descriptionEnhancerErrorAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "descriptionEnhancerErrorAlarmB44CE132"}], "/AIAgentWorkflowStack/descriptionEnhancerDurationAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "descriptionEnhancerDurationAlarm85FCFAD9"}], "/AIAgentWorkflowStack/descriptionEnhancerThrottleAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "descriptionEnhancerThrottleAlarmDA4A9020"}], "/AIAgentWorkflowStack/codeCreatorErrorAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "codeCreatorErrorAlarm54EDFA6E"}], "/AIAgentWorkflowStack/codeCreatorDurationAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "codeCreatorDurationAlarm62B698E9"}], "/AIAgentWorkflowStack/codeCreatorThrottleAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "codeCreatorThrottleAlarm894F3E5C"}], "/AIAgentWorkflowStack/reviewRefineErrorAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "reviewRefineErrorAlarmAC4A9E11"}], "/AIAgentWorkflowStack/reviewRefineDurationAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "reviewRefineDurationAlarm2B9ACC7F"}], "/AIAgentWorkflowStack/reviewRefineThrottleAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "reviewRefineThrottleAlarm43B3D31E"}], "/AIAgentWorkflowStack/finalizerErrorAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "finalizerErrorAlarmB06CD5D7"}], "/AIAgentWorkflowStack/finalizerDurationAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "finalizerDurationAlarm00CE24F0"}], "/AIAgentWorkflowStack/finalizerThrottleAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "finalizerThrottleAlarm2689022E"}], "/AIAgentWorkflowStack/StateMachineFailedAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "StateMachineFailedAlarm79113CC8"}], "/AIAgentWorkflowStack/DynamoDBThrottleAlarm/Resource": [{"type": "aws:cdk:logicalId", "data": "DynamoDBThrottleAlarmF21EBBE2"}], "/AIAgentWorkflowStack/ApiEndpoint": [{"type": "aws:cdk:logicalId", "data": "ApiEndpoint"}], "/AIAgentWorkflowStack/StateMachineArn": [{"type": "aws:cdk:logicalId", "data": "StateMachineArn"}], "/AIAgentWorkflowStack/WorkflowTableName": [{"type": "aws:cdk:logicalId", "data": "WorkflowTableName"}], "/AIAgentWorkflowStack/ArtifactsBucketName": [{"type": "aws:cdk:logicalId", "data": "ArtifactsBucketName"}], "/AIAgentWorkflowStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/AIAgentWorkflowStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/AIAgentWorkflowStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "AIAgentWorkflowStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1019.2"}