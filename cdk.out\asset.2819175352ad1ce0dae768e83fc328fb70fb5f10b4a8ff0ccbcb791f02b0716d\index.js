"use strict";var h=Object.create,d=Object.defineProperty,P=Object.getOwnPropertyDescriptor,C=Object.getOwnPropertyNames,b=Object.getPrototypeOf,S=Object.prototype.hasOwnProperty,E=(e,o)=>{for(var n in o)d(e,n,{get:o[n],enumerable:!0})},p=(e,o,n,t)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of C(o))!S.call(e,r)&&r!==n&&d(e,r,{get:()=>o[r],enumerable:!(t=P(o,r))||t.enumerable});return e},G=(e,o,n)=>(n=e!=null?h(b(e)):{},p(o||!e||!e.__esModule?d(n,"default",{value:e,enumerable:!0}):n,e)),x=e=>p(d({},"__esModule",{value:!0}),e),O={};E(O,{disableSleepForTesting:()=>I,handler:()=>q}),module.exports=x(O);var i=G(require("@aws-sdk/client-cloudwatch-logs")),w=!1;function I(){w=!0}async function R(e,o,n){await n(async()=>{try{let t={logGroupName:e},r=new i.CreateLogGroupCommand(t);await o.send(r)}catch(t){if(t.name==="ResourceAlreadyExistsException")return;throw t}})}async function k(e,o,n){await n(async()=>{try{let t={logGroupName:e},r=new i.DeleteLogGroupCommand(t);await o.send(r)}catch(t){if(t.name==="ResourceNotFoundException")return;throw t}})}async function y(e,o,n,t){await n(async()=>{if(t){let r={logGroupName:e,retentionInDays:t},s=new i.PutRetentionPolicyCommand(r);await o.send(s)}else{let r={logGroupName:e},s=new i.DeleteRetentionPolicyCommand(r);await o.send(s)}})}async function q(e,o){try{console.log(JSON.stringify({...e,ResponseURL:"..."}));let t=e.ResourceProperties.LogGroupName,r=e.ResourceProperties.LogGroupRegion,s=L(e.ResourceProperties.SdkRetry?.maxRetries)??10,a=N(s),m={logger:console,region:r},c=new i.CloudWatchLogsClient(m);if((e.RequestType==="Create"||e.RequestType==="Update")&&(await R(t,c,a),await y(t,c,a,L(e.ResourceProperties.RetentionInDays)),e.RequestType==="Create")){let g=new i.CloudWatchLogsClient({logger:console,region:process.env.AWS_REGION});await R(`/aws/lambda/${o.functionName}`,g,a),await y(`/aws/lambda/${o.functionName}`,g,a,1)}e.RequestType==="Delete"&&e.ResourceProperties.RemovalPolicy==="destroy"&&await k(t,c,a),await n("SUCCESS","OK",t)}catch(t){console.log(t),await n("FAILED",t.message,e.ResourceProperties.LogGroupName)}function n(t,r,s){let a=JSON.stringify({Status:t,Reason:r,PhysicalResourceId:s,StackId:e.StackId,RequestId:e.RequestId,LogicalResourceId:e.LogicalResourceId,Data:{LogGroupName:e.ResourceProperties.LogGroupName}});console.log("Responding",a);let m=require("url").parse(e.ResponseURL),c={hostname:m.hostname,path:m.path,method:"PUT",headers:{"content-type":"","content-length":Buffer.byteLength(a,"utf8")}};return new Promise((g,l)=>{try{let u=require("https").request(c,g);u.on("error",l),u.write(a),u.end()}catch(u){l(u)}})}}function L(e,o=10){if(e!==void 0)return parseInt(e,o)}function N(e,o=1e3,n=6e4){return async t=>{let r=0;do try{return await t()}catch(s){if(f("OperationAbortedException",s)||f("ThrottlingException",s))if(r<e){r++,await D(W(r,o,n));continue}else throw new Error("Out of attempts to change log group");throw s}while(!0)}}function f(e,o){return o.name===e||o.message.includes(e)}function W(e,o,n){return Math.min(Math.round(Math.random()*o*2**e),n)}async function D(e){w&&(e=0),await new Promise(o=>setTimeout(o,e))}
