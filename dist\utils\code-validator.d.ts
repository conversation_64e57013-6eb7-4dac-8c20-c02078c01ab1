/**
 * Code validation utilities for static analysis
 */
import { GeneratedFile, CodeError, CodeWarning } from '../types';
export interface CodeValidationResult {
    errors: CodeError[];
    warnings: CodeWarning[];
    metrics: {
        linesOfCode: number;
        complexity: number;
        maintainabilityIndex: number;
    };
}
export declare function validateGeneratedCode(files: GeneratedFile[]): Promise<CodeValidationResult>;
