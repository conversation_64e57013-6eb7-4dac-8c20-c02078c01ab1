"use strict";
/**
 * Security scanning utilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSecurityScan = runSecurityScan;
exports.generateSecurityReport = generateSecurityReport;
async function runSecurityScan(files) {
    const issues = [];
    const errors = [];
    let totalScore = 100;
    for (const file of files) {
        const fileResults = scanFile(file);
        issues.push(...fileResults.issues);
        errors.push(...fileResults.errors);
        totalScore -= fileResults.penalty;
    }
    // Additional cross-file security checks
    const crossFileResults = performCrossFileSecurityAnalysis(files);
    issues.push(...crossFileResults.issues);
    errors.push(...crossFileResults.errors);
    totalScore -= crossFileResults.penalty;
    return {
        issues,
        errors,
        score: Math.max(0, totalScore),
    };
}
function scanFile(file) {
    const issues = [];
    const errors = [];
    let penalty = 0;
    const content = file.content;
    const lines = content.split('\n');
    // Check for common security vulnerabilities
    const securityChecks = [
        {
            pattern: /eval\s*\(/gi,
            severity: 'critical',
            description: 'Use of eval() can lead to code injection vulnerabilities',
            recommendation: 'Avoid eval(). Use JSON.parse() for JSON data or safer alternatives',
            penalty: 20,
        },
        {
            pattern: /innerHTML\s*=/gi,
            severity: 'high',
            description: 'innerHTML can lead to XSS vulnerabilities',
            recommendation: 'Use textContent or sanitize HTML content before setting innerHTML',
            penalty: 15,
        },
        {
            pattern: /document\.write\s*\(/gi,
            severity: 'high',
            description: 'document.write can be exploited for XSS attacks',
            recommendation: 'Use modern DOM manipulation methods instead',
            penalty: 15,
        },
        {
            pattern: /exec\s*\(/gi,
            severity: 'critical',
            description: 'exec() can lead to command injection vulnerabilities',
            recommendation: 'Avoid exec(). Use safer alternatives for command execution',
            penalty: 25,
        },
        {
            pattern: /\$\{[^}]*\}/g,
            severity: 'medium',
            description: 'Template literals with user input can lead to injection attacks',
            recommendation: 'Sanitize user input before using in template literals',
            penalty: 10,
        },
        {
            pattern: /password\s*=\s*['"][^'"]*['"]/gi,
            severity: 'critical',
            description: 'Hardcoded passwords detected',
            recommendation: 'Use environment variables or secure credential storage',
            penalty: 30,
        },
        {
            pattern: /api[_-]?key\s*=\s*['"][^'"]*['"]/gi,
            severity: 'high',
            description: 'Hardcoded API keys detected',
            recommendation: 'Use environment variables for API keys',
            penalty: 20,
        },
        {
            pattern: /secret\s*=\s*['"][^'"]*['"]/gi,
            severity: 'high',
            description: 'Hardcoded secrets detected',
            recommendation: 'Use secure secret management systems',
            penalty: 20,
        },
        {
            pattern: /http:\/\/[^'">\s]+/gi,
            severity: 'medium',
            description: 'Insecure HTTP URLs detected',
            recommendation: 'Use HTTPS for all external communications',
            penalty: 5,
        },
        {
            pattern: /SELECT\s+\*\s+FROM\s+\w+\s+WHERE\s+.*=\s*['"]?\$\{/gi,
            severity: 'critical',
            description: 'Potential SQL injection vulnerability',
            recommendation: 'Use parameterized queries or prepared statements',
            penalty: 25,
        },
    ];
    securityChecks.forEach(check => {
        const matches = content.match(check.pattern);
        if (matches) {
            matches.forEach(match => {
                const lineNumber = findLineNumber(content, match);
                issues.push({
                    severity: check.severity,
                    description: check.description,
                    file: file.path,
                    line: lineNumber,
                    recommendation: check.recommendation,
                });
                if (check.severity === 'critical') {
                    errors.push({
                        severity: 'critical',
                        message: check.description,
                        file: file.path,
                        line: lineNumber,
                        rule: 'security_vulnerability',
                    });
                }
                penalty += check.penalty;
            });
        }
    });
    // Language-specific security checks
    switch (file.language.toLowerCase()) {
        case 'javascript':
        case 'typescript':
            scanJavaScriptSecurity(file, issues, errors);
            break;
        case 'python':
            scanPythonSecurity(file, issues, errors);
            break;
        case 'html':
            scanHTMLSecurity(file, issues, errors);
            break;
    }
    return { issues, errors, penalty };
}
function scanJavaScriptSecurity(file, issues, errors) {
    const content = file.content;
    // Check for unsafe DOM manipulation
    if (content.includes('dangerouslySetInnerHTML')) {
        issues.push({
            severity: 'high',
            description: 'dangerouslySetInnerHTML can lead to XSS if not properly sanitized',
            file: file.path,
            recommendation: 'Ensure content is properly sanitized before using dangerouslySetInnerHTML',
        });
    }
    // Check for localStorage usage without encryption
    if (content.includes('localStorage.setItem') && !content.includes('encrypt')) {
        issues.push({
            severity: 'medium',
            description: 'Sensitive data stored in localStorage without encryption',
            file: file.path,
            recommendation: 'Encrypt sensitive data before storing in localStorage',
        });
    }
    // Check for CORS issues
    if (content.includes('Access-Control-Allow-Origin: *')) {
        issues.push({
            severity: 'medium',
            description: 'Overly permissive CORS policy',
            file: file.path,
            recommendation: 'Restrict CORS to specific domains instead of using wildcard',
        });
    }
    // Check for missing CSRF protection
    if (content.includes('POST') && !content.includes('csrf') && !content.includes('token')) {
        issues.push({
            severity: 'medium',
            description: 'POST requests without CSRF protection',
            file: file.path,
            recommendation: 'Implement CSRF tokens for state-changing operations',
        });
    }
}
function scanPythonSecurity(file, issues, errors) {
    const content = file.content;
    // Check for pickle usage
    if (content.includes('pickle.loads') || content.includes('pickle.load')) {
        issues.push({
            severity: 'high',
            description: 'pickle.loads can execute arbitrary code',
            file: file.path,
            recommendation: 'Use safer serialization formats like JSON',
        });
    }
    // Check for shell command injection
    if (content.includes('os.system') || content.includes('subprocess.call')) {
        issues.push({
            severity: 'high',
            description: 'Shell command execution can lead to command injection',
            file: file.path,
            recommendation: 'Use subprocess with shell=False and validate inputs',
        });
    }
    // Check for SQL injection in Python
    if (content.includes('execute(') && content.includes('%s') && !content.includes('parameterized')) {
        issues.push({
            severity: 'critical',
            description: 'Potential SQL injection vulnerability',
            file: file.path,
            recommendation: 'Use parameterized queries instead of string formatting',
        });
    }
}
function scanHTMLSecurity(file, issues, errors) {
    const content = file.content;
    // Check for missing CSP
    if (content.includes('<html>') && !content.includes('Content-Security-Policy')) {
        issues.push({
            severity: 'medium',
            description: 'Missing Content Security Policy',
            file: file.path,
            recommendation: 'Add CSP headers to prevent XSS attacks',
        });
    }
    // Check for inline scripts
    if (content.includes('<script>') && !content.includes('nonce=')) {
        issues.push({
            severity: 'medium',
            description: 'Inline scripts without nonce can be blocked by CSP',
            file: file.path,
            recommendation: 'Use external script files or add nonce attributes',
        });
    }
    // Check for missing HTTPS
    if (content.includes('http://') && !content.includes('localhost')) {
        issues.push({
            severity: 'medium',
            description: 'HTTP links in production can be insecure',
            file: file.path,
            recommendation: 'Use HTTPS for all external links',
        });
    }
}
function performCrossFileSecurityAnalysis(files) {
    const issues = [];
    const errors = [];
    let penalty = 0;
    // Check for missing authentication
    const hasAuthFiles = files.some(f => f.path.includes('auth') ||
        f.content.includes('authenticate') ||
        f.content.includes('jwt'));
    const hasAPIFiles = files.some(f => f.content.includes('app.get') ||
        f.content.includes('app.post') ||
        f.content.includes('router.'));
    if (hasAPIFiles && !hasAuthFiles) {
        issues.push({
            severity: 'high',
            description: 'API endpoints detected without authentication mechanism',
            recommendation: 'Implement authentication for API endpoints',
        });
        penalty += 15;
    }
    // Check for missing input validation
    const hasValidationFiles = files.some(f => f.content.includes('validate') ||
        f.content.includes('joi') ||
        f.content.includes('yup'));
    if (hasAPIFiles && !hasValidationFiles) {
        issues.push({
            severity: 'medium',
            description: 'API endpoints without input validation',
            recommendation: 'Implement input validation for all API endpoints',
        });
        penalty += 10;
    }
    // Check for missing rate limiting
    const hasRateLimiting = files.some(f => f.content.includes('rate-limit') ||
        f.content.includes('rateLimit'));
    if (hasAPIFiles && !hasRateLimiting) {
        issues.push({
            severity: 'medium',
            description: 'API endpoints without rate limiting',
            recommendation: 'Implement rate limiting to prevent abuse',
        });
        penalty += 10;
    }
    // Check for missing error handling
    const hasErrorHandling = files.some(f => f.content.includes('try') && f.content.includes('catch'));
    if (hasAPIFiles && !hasErrorHandling) {
        issues.push({
            severity: 'medium',
            description: 'Missing comprehensive error handling',
            recommendation: 'Implement proper error handling and logging',
        });
        penalty += 10;
    }
    return { issues, errors, penalty };
}
function findLineNumber(content, searchString) {
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes(searchString)) {
            return i + 1;
        }
    }
    return 1;
}
function generateSecurityReport(scanResult) {
    const { issues, score } = scanResult;
    let report = `# Security Scan Report\n\n`;
    report += `**Overall Security Score: ${score}/100**\n\n`;
    if (issues.length === 0) {
        report += `✅ No security issues detected!\n\n`;
        return report;
    }
    const criticalIssues = issues.filter(i => i.severity === 'critical');
    const highIssues = issues.filter(i => i.severity === 'high');
    const mediumIssues = issues.filter(i => i.severity === 'medium');
    const lowIssues = issues.filter(i => i.severity === 'low');
    if (criticalIssues.length > 0) {
        report += `## 🚨 Critical Issues (${criticalIssues.length})\n\n`;
        criticalIssues.forEach(issue => {
            report += `- **${issue.description}**\n`;
            if (issue.file)
                report += `  - File: ${issue.file}\n`;
            if (issue.line)
                report += `  - Line: ${issue.line}\n`;
            report += `  - Recommendation: ${issue.recommendation}\n\n`;
        });
    }
    if (highIssues.length > 0) {
        report += `## ⚠️ High Priority Issues (${highIssues.length})\n\n`;
        highIssues.forEach(issue => {
            report += `- **${issue.description}**\n`;
            if (issue.file)
                report += `  - File: ${issue.file}\n`;
            if (issue.line)
                report += `  - Line: ${issue.line}\n`;
            report += `  - Recommendation: ${issue.recommendation}\n\n`;
        });
    }
    if (mediumIssues.length > 0) {
        report += `## ⚡ Medium Priority Issues (${mediumIssues.length})\n\n`;
        mediumIssues.forEach(issue => {
            report += `- **${issue.description}**\n`;
            if (issue.file)
                report += `  - File: ${issue.file}\n`;
            if (issue.line)
                report += `  - Line: ${issue.line}\n`;
            report += `  - Recommendation: ${issue.recommendation}\n\n`;
        });
    }
    if (lowIssues.length > 0) {
        report += `## 💡 Low Priority Issues (${lowIssues.length})\n\n`;
        lowIssues.forEach(issue => {
            report += `- **${issue.description}**\n`;
            if (issue.file)
                report += `  - File: ${issue.file}\n`;
            if (issue.line)
                report += `  - Line: ${issue.line}\n`;
            report += `  - Recommendation: ${issue.recommendation}\n\n`;
        });
    }
    return report;
}
//# sourceMappingURL=data:application/json;base64,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