"use strict";
/**
 * Static analysis utilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.runStaticAnalysis = runStaticAnalysis;
async function runStaticAnalysis(files) {
    const errors = [];
    const suggestions = [];
    let totalComplexity = 0;
    let totalMaintainability = 0;
    for (const file of files) {
        const fileAnalysis = analyzeFile(file);
        errors.push(...fileAnalysis.errors);
        suggestions.push(...fileAnalysis.suggestions);
        totalComplexity += fileAnalysis.complexity;
        totalMaintainability += fileAnalysis.maintainability;
    }
    const avgMaintainability = files.length > 0 ? totalMaintainability / files.length : 0;
    const testCoverage = calculateTestCoverage(files);
    return {
        errors,
        suggestions,
        metrics: {
            complexity: totalComplexity,
            maintainability: avgMaintainability,
            testCoverage,
        },
    };
}
function analyzeFile(file) {
    const errors = [];
    const suggestions = [];
    // Calculate complexity
    const complexity = calculateCyclomaticComplexity(file.content);
    const maintainability = calculateMaintainabilityScore(file);
    // Analyze based on file type
    switch (file.language.toLowerCase()) {
        case 'typescript':
        case 'javascript':
            analyzeJavaScriptFile(file, errors, suggestions);
            break;
        case 'python':
            analyzePythonFile(file, errors, suggestions);
            break;
        case 'html':
            analyzeHTMLFile(file, errors, suggestions);
            break;
        case 'css':
            analyzeCSSFile(file, errors, suggestions);
            break;
    }
    // General code quality checks
    analyzeCodeQuality(file, errors, suggestions);
    return { errors, suggestions, complexity, maintainability };
}
function calculateCyclomaticComplexity(content) {
    let complexity = 1; // Base complexity
    const complexityPatterns = [
        /\bif\b/g,
        /\belse\s+if\b/g,
        /\bwhile\b/g,
        /\bfor\b/g,
        /\bdo\b/g,
        /\bswitch\b/g,
        /\bcase\b/g,
        /\bcatch\b/g,
        /\b&&\b/g,
        /\b\|\|\b/g,
        /\?\s*:/g, // Ternary operator
        /\bbreak\b/g,
        /\bcontinue\b/g,
    ];
    for (const pattern of complexityPatterns) {
        const matches = content.match(pattern);
        if (matches) {
            complexity += matches.length;
        }
    }
    return complexity;
}
function calculateMaintainabilityScore(file) {
    const lines = file.content.split('\n');
    const linesOfCode = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('/*');
    }).length;
    const complexity = calculateCyclomaticComplexity(file.content);
    const commentRatio = calculateCommentRatio(file.content);
    // Simplified maintainability calculation
    let score = 100;
    // Penalize high complexity
    if (complexity > 10)
        score -= (complexity - 10) * 2;
    // Penalize long files
    if (linesOfCode > 200)
        score -= (linesOfCode - 200) * 0.1;
    // Reward good commenting
    score += commentRatio * 10;
    return Math.max(0, Math.min(100, score));
}
function calculateCommentRatio(content) {
    const lines = content.split('\n');
    const commentLines = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*');
    }).length;
    return lines.length > 0 ? commentLines / lines.length : 0;
}
function calculateTestCoverage(files) {
    const sourceFiles = files.filter(f => f.type === 'source');
    const testFiles = files.filter(f => f.type === 'test' || f.path.includes('.test.') || f.path.includes('.spec.'));
    if (sourceFiles.length === 0)
        return 0;
    // Simple heuristic: test coverage based on ratio of test files to source files
    const coverage = Math.min(100, (testFiles.length / sourceFiles.length) * 100);
    return coverage;
}
function analyzeJavaScriptFile(file, errors, suggestions) {
    const content = file.content;
    const lines = content.split('\n');
    // Check for function length
    const functions = content.match(/function\s+\w+\s*\([^)]*\)\s*\{/g) || [];
    functions.forEach(() => {
        // This is a simplified check - in reality, you'd parse the AST
        const avgFunctionLength = content.length / Math.max(functions.length, 1);
        if (avgFunctionLength > 500) {
            suggestions.push({
                type: 'refactor',
                description: 'Consider breaking down large functions into smaller ones',
                impact: 'medium',
                effort: 'medium',
            });
        }
    });
    // Check for missing JSDoc
    const exportedFunctions = content.match(/export\s+(function|const\s+\w+\s*=)/g) || [];
    const jsdocComments = content.match(/\/\*\*[\s\S]*?\*\//g) || [];
    if (exportedFunctions.length > jsdocComments.length) {
        suggestions.push({
            type: 'improvement',
            description: 'Add JSDoc comments to exported functions',
            impact: 'low',
            effort: 'low',
        });
    }
    // Check for error handling
    const asyncFunctions = content.match(/async\s+function|async\s+\(/g) || [];
    const tryBlocks = content.match(/try\s*\{/g) || [];
    if (asyncFunctions.length > tryBlocks.length) {
        errors.push({
            severity: 'major',
            message: 'Async functions should include error handling',
            file: file.path,
            rule: 'missing_error_handling',
        });
    }
    // Check for security issues
    if (content.includes('eval(')) {
        errors.push({
            severity: 'critical',
            message: 'Use of eval() is dangerous and should be avoided',
            file: file.path,
            rule: 'security_eval',
        });
    }
    if (content.includes('innerHTML') && !content.includes('sanitize')) {
        errors.push({
            severity: 'major',
            message: 'innerHTML usage without sanitization can lead to XSS',
            file: file.path,
            rule: 'security_xss',
        });
    }
}
function analyzePythonFile(file, errors, suggestions) {
    const content = file.content;
    // Check for proper imports
    if (content.includes('import *')) {
        suggestions.push({
            type: 'improvement',
            description: 'Avoid wildcard imports, import specific modules instead',
            impact: 'low',
            effort: 'low',
        });
    }
    // Check for proper exception handling
    if (content.includes('except:') && !content.includes('except Exception:')) {
        errors.push({
            severity: 'major',
            message: 'Bare except clauses should be avoided',
            file: file.path,
            rule: 'bare_except',
        });
    }
    // Check for docstrings
    const functionDefs = content.match(/def\s+\w+\s*\(/g) || [];
    const docstrings = content.match(/""".+?"""/gs) || [];
    if (functionDefs.length > docstrings.length) {
        suggestions.push({
            type: 'improvement',
            description: 'Add docstrings to functions for better documentation',
            impact: 'low',
            effort: 'low',
        });
    }
}
function analyzeHTMLFile(file, errors, suggestions) {
    const content = file.content;
    // Check for accessibility
    const imgTags = content.match(/<img[^>]*>/g) || [];
    const imgsWithoutAlt = imgTags.filter(tag => !tag.includes('alt='));
    if (imgsWithoutAlt.length > 0) {
        errors.push({
            severity: 'minor',
            message: 'Images should have alt attributes for accessibility',
            file: file.path,
            rule: 'accessibility_alt',
        });
    }
    // Check for semantic HTML
    if (!content.includes('<main>') && !content.includes('<section>') && !content.includes('<article>')) {
        suggestions.push({
            type: 'improvement',
            description: 'Use semantic HTML elements for better structure',
            impact: 'medium',
            effort: 'low',
        });
    }
    // Check for meta tags
    if (content.includes('<html>') && !content.includes('<meta name="viewport"')) {
        suggestions.push({
            type: 'improvement',
            description: 'Add viewport meta tag for responsive design',
            impact: 'medium',
            effort: 'low',
        });
    }
}
function analyzeCSSFile(file, errors, suggestions) {
    const content = file.content;
    // Check for vendor prefixes
    const vendorPrefixes = ['-webkit-', '-moz-', '-ms-', '-o-'];
    let hasVendorPrefixes = false;
    vendorPrefixes.forEach(prefix => {
        if (content.includes(prefix)) {
            hasVendorPrefixes = true;
        }
    });
    if (hasVendorPrefixes) {
        suggestions.push({
            type: 'optimization',
            description: 'Consider using autoprefixer instead of manual vendor prefixes',
            impact: 'low',
            effort: 'low',
        });
    }
    // Check for magic numbers
    const magicNumbers = content.match(/:\s*\d+px/g) || [];
    if (magicNumbers.length > 10) {
        suggestions.push({
            type: 'refactor',
            description: 'Consider using CSS variables for repeated values',
            impact: 'medium',
            effort: 'medium',
        });
    }
    // Check for !important overuse
    const importantCount = (content.match(/!important/g) || []).length;
    if (importantCount > 5) {
        suggestions.push({
            type: 'refactor',
            description: 'Reduce use of !important by improving CSS specificity',
            impact: 'medium',
            effort: 'high',
        });
    }
}
function analyzeCodeQuality(file, errors, suggestions) {
    const lines = file.content.split('\n');
    // Check for very long lines
    lines.forEach((line, index) => {
        if (line.length > 120) {
            suggestions.push({
                type: 'improvement',
                description: `Line ${index + 1} is too long (${line.length} characters)`,
                impact: 'low',
                effort: 'low',
            });
        }
    });
    // Check for TODO/FIXME comments
    const todoComments = file.content.match(/\/\/\s*(TODO|FIXME|HACK)/gi) || [];
    if (todoComments.length > 0) {
        suggestions.push({
            type: 'improvement',
            description: `Found ${todoComments.length} TODO/FIXME comments that should be addressed`,
            impact: 'low',
            effort: 'medium',
        });
    }
    // Check file size
    if (file.content.length > 10000) {
        suggestions.push({
            type: 'refactor',
            description: 'File is quite large, consider splitting into smaller modules',
            impact: 'medium',
            effort: 'high',
        });
    }
}
//# sourceMappingURL=data:application/json;base64,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