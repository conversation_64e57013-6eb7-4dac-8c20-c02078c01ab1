{"Comment": "AI Agent Workflow for Website/Chatbot Generation", "StartAt": "InitializeWorkflow", "States": {"InitializeWorkflow": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "userRequest.$": "$.userRequest", "currentIteration": 0, "maxIterations": 5, "status": "initializing"}, "Next": "EnhanceDescription"}, "EnhanceDescription": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${DescriptionEnhancerFunction}", "Payload": {"sessionId.$": "$.sessionId", "userRequest.$": "$.userRequest"}}, "ResultPath": "$.enhancementResult", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}], "Next": "PrepareTaskExecution"}, "PrepareTaskExecution": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "specification.$": "$.enhancementResult.Payload.specification", "taskPlan.$": "$.enhancementResult.Payload.taskPlan", "currentIteration.$": "$.currentIteration", "maxIterations.$": "$.maxIterations", "artifacts": [], "reviews": []}, "Next": "ExecuteTasksMap"}, "ExecuteTasksMap": {"Type": "Map", "ItemsPath": "$.taskPlan.tasks", "MaxConcurrency": 3, "Parameters": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "task.$": "$$.Map.Item.Value", "iteration.$": "$.currentIteration"}, "Iterator": {"StartAt": "CheckTaskDependencies", "States": {"CheckTaskDependencies": {"Type": "Choice", "Choices": [{"Variable": "$.task.dependencies", "IsPresent": true, "Next": "WaitForDependencies"}], "Default": "ExecuteCodeCreator"}, "WaitForDependencies": {"Type": "Wait", "Seconds": 5, "Next": "ExecuteCodeCreator"}, "ExecuteCodeCreator": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${CodeCreatorFunction}", "Payload.$": "$"}, "ResultPath": "$.codeResult", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 2, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "TaskFailed", "ResultPath": "$.taskError"}], "End": true}, "TaskFailed": {"Type": "Pass", "Parameters": {"taskId.$": "$.task.taskId", "status": "failed", "error.$": "$.taskError"}, "End": true}}}, "ResultPath": "$.taskResults", "Next": "CollectArtifacts"}, "CollectArtifacts": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "taskPlan.$": "$.taskPlan", "artifacts.$": "$.taskResults[*].codeResult.Payload", "currentIteration.$": "$.currentIteration", "maxIterations.$": "$.maxIterations"}, "Next": "ReviewAndRefine"}, "ReviewAndRefine": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${ReviewRefineFunction}", "Payload": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "taskPlan.$": "$.taskPlan", "artifacts.$": "$.artifacts", "iteration.$": "$.currentIteration"}}, "ResultPath": "$.reviewResults", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}], "Next": "EvaluateReviewResults"}, "EvaluateReviewResults": {"Type": "Choice", "Choices": [{"And": [{"Variable": "$.reviewResults.Payload[0].errors", "IsPresent": true}, {"Variable": "$.currentIteration", "NumericLessThan": 5}], "Next": "PrepareNextIteration"}, {"Variable": "$.currentIteration", "NumericGreaterThanEquals": 5, "Next": "MaxIterationsReached"}], "Default": "FinalizeProject"}, "PrepareNextIteration": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "taskPlan.$": "$.taskPlan", "currentIteration.$": "States.MathAdd($.currentIteration, 1)", "maxIterations.$": "$.maxIterations", "artifacts.$": "$.artifacts", "reviews.$": "$.reviewResults.Payload"}, "Next": "UpdateTasksBasedOnReview"}, "UpdateTasksBasedOnReview": {"Type": "Choice", "Choices": [{"Variable": "$.reviews[0].updatedTasks", "IsPresent": true, "Next": "ApplyTaskUpdates"}], "Default": "ExecuteTasksMap"}, "ApplyTaskUpdates": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "taskPlan": {"tasks.$": "$.reviews[0].updatedTasks", "totalEstimatedDuration.$": "$.taskPlan.totalEstimatedDuration", "dependencies.$": "$.taskPlan.dependencies"}, "currentIteration.$": "$.currentIteration", "maxIterations.$": "$.maxIterations", "artifacts": [], "reviews.$": "$.reviews"}, "Next": "ExecuteTasksMap"}, "MaxIterationsReached": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "status": "max_iterations_reached", "message": "Maximum iterations reached. Proceeding with current artifacts.", "artifacts.$": "$.artifacts", "reviews.$": "$.reviewResults.Payload"}, "Next": "FinalizeProject"}, "FinalizeProject": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${FinalizerFunction}", "Payload": {"sessionId.$": "$.sessionId", "specification.$": "$.specification", "artifacts.$": "$.artifacts", "reviews.$": "$.reviewResults.Payload"}}, "ResultPath": "$.finalOutput", "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException"], "IntervalSeconds": 2, "MaxAttempts": 3, "BackoffRate": 2.0}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "HandleError", "ResultPath": "$.error"}], "Next": "WorkflowCompleted"}, "WorkflowCompleted": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "status": "completed", "finalOutput.$": "$.finalOutput.Payload", "message": "Workflow completed successfully", "completedAt.$": "$$.State.EnteredTime"}, "End": true}, "HandleError": {"Type": "Pass", "Parameters": {"sessionId.$": "$.sessionId", "status": "failed", "error.$": "$.error", "message": "Workflow failed due to an error", "failedAt.$": "$$.State.EnteredTime"}, "Next": "NotifyError"}, "NotifyError": {"Type": "Task", "Resource": "arn:aws:states:::sns:publish", "Parameters": {"TopicArn": "${ErrorNotificationTopic}", "Message": {"sessionId.$": "$.sessionId", "error.$": "$.error", "timestamp.$": "$$.State.EnteredTime"}}, "End": true}}}