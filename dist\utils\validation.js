"use strict";
/**
 * Validation utilities for user requests and generated content
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserRequest = validateUserRequest;
exports.validateGeneratedFile = validateGeneratedFile;
exports.validateCodeSyntax = validateCodeSyntax;
exports.validateProjectStructure = validateProjectStructure;
function validateUserRequest(userRequest) {
    const errors = [];
    const warnings = [];
    // Check required fields
    if (!userRequest.userDescription) {
        errors.push('User description is required');
    }
    if (userRequest.userDescription && userRequest.userDescription.length < 10) {
        errors.push('User description must be at least 10 characters long');
    }
    if (userRequest.userDescription && userRequest.userDescription.length > 5000) {
        errors.push('User description must be less than 5000 characters');
    }
    // Validate preferences if provided
    if (userRequest.preferences) {
        if (userRequest.preferences.techStack) {
            const validTechStacks = ['react', 'vue', 'angular', 'svelte', 'next.js', 'nuxt.js', 'express', 'fastify', 'nest.js'];
            const invalidTechStacks = userRequest.preferences.techStack.filter(tech => !validTechStacks.includes(tech.toLowerCase()));
            if (invalidTechStacks.length > 0) {
                warnings.push(`Unknown tech stack preferences: ${invalidTechStacks.join(', ')}`);
            }
        }
        if (userRequest.preferences.deploymentTarget) {
            const validTargets = ['aws', 'vercel', 'netlify', 'heroku', 'digitalocean'];
            if (!validTargets.includes(userRequest.preferences.deploymentTarget)) {
                warnings.push(`Unknown deployment target: ${userRequest.preferences.deploymentTarget}`);
            }
        }
    }
    // Check for potentially harmful content
    const harmfulPatterns = [
        /\b(hack|exploit|vulnerability|malware|virus)\b/gi,
        /\b(illegal|piracy|copyright\s+infringement)\b/gi,
        /\b(adult|porn|gambling)\b/gi,
    ];
    for (const pattern of harmfulPatterns) {
        if (pattern.test(userRequest.userDescription)) {
            warnings.push('Description contains potentially inappropriate content');
            break;
        }
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
}
function validateGeneratedFile(file) {
    const errors = [];
    const warnings = [];
    // Check required fields
    if (!file.path) {
        errors.push('File path is required');
    }
    if (!file.content) {
        errors.push('File content is required');
    }
    if (!file.type) {
        errors.push('File type is required');
    }
    if (!file.language) {
        errors.push('File language is required');
    }
    // Validate file path
    if (file.path) {
        if (file.path.includes('..')) {
            errors.push('File path cannot contain parent directory references (..)');
        }
        if (file.path.startsWith('/')) {
            errors.push('File path cannot be absolute');
        }
        if (!/^[a-zA-Z0-9._/-]+$/.test(file.path)) {
            errors.push('File path contains invalid characters');
        }
    }
    // Validate file type
    const validTypes = ['source', 'config', 'documentation', 'test'];
    if (file.type && !validTypes.includes(file.type)) {
        errors.push(`Invalid file type: ${file.type}`);
    }
    // Validate file extension matches language
    if (file.path && file.language) {
        const extension = file.path.split('.').pop()?.toLowerCase();
        const expectedExtensions = {
            'typescript': ['ts', 'tsx'],
            'javascript': ['js', 'jsx'],
            'python': ['py'],
            'html': ['html', 'htm'],
            'css': ['css'],
            'json': ['json'],
            'yaml': ['yaml', 'yml'],
            'markdown': ['md'],
            'sql': ['sql'],
        };
        const expected = expectedExtensions[file.language.toLowerCase()];
        if (expected && extension && !expected.includes(extension)) {
            warnings.push(`File extension '${extension}' doesn't match language '${file.language}'`);
        }
    }
    // Check for potentially harmful content in code
    if (file.content) {
        const harmfulPatterns = [
            /eval\s*\(/gi,
            /exec\s*\(/gi,
            /system\s*\(/gi,
            /shell_exec\s*\(/gi,
            /\$\{.*\}/g, // Template injection
            /document\.write\s*\(/gi,
            /innerHTML\s*=/gi,
        ];
        for (const pattern of harmfulPatterns) {
            if (pattern.test(file.content)) {
                warnings.push('File content contains potentially unsafe code patterns');
                break;
            }
        }
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
}
function validateCodeSyntax(file) {
    const errors = [];
    const warnings = [];
    if (!file.content) {
        return { isValid: false, errors: ['No content to validate'], warnings: [] };
    }
    try {
        switch (file.language.toLowerCase()) {
            case 'json':
                JSON.parse(file.content);
                break;
            case 'javascript':
            case 'typescript':
                validateJavaScriptSyntax(file.content, errors, warnings);
                break;
            case 'html':
                validateHTMLSyntax(file.content, errors, warnings);
                break;
            case 'css':
                validateCSSSyntax(file.content, errors, warnings);
                break;
            default:
                // Basic validation for other languages
                validateBasicSyntax(file.content, errors, warnings);
        }
    }
    catch (error) {
        errors.push(`Syntax error: ${error.message}`);
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
}
function validateJavaScriptSyntax(content, errors, warnings) {
    // Basic JavaScript/TypeScript validation
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        const lineNumber = i + 1;
        // Check for common syntax errors
        if (line.includes('function') && !line.includes('(')) {
            errors.push(`Line ${lineNumber}: Function declaration missing parentheses`);
        }
        if (line.includes('{') && !line.includes('}') && !lines.slice(i + 1).some(l => l.includes('}'))) {
            warnings.push(`Line ${lineNumber}: Unclosed brace detected`);
        }
        // Check for console.log in production code
        if (line.includes('console.log') && !content.includes('// debug')) {
            warnings.push(`Line ${lineNumber}: Console.log statement found (consider removing for production)`);
        }
    }
}
function validateHTMLSyntax(content, errors, warnings) {
    // Basic HTML validation
    const openTags = content.match(/<[^/][^>]*>/g) || [];
    const closeTags = content.match(/<\/[^>]*>/g) || [];
    if (openTags.length !== closeTags.length) {
        warnings.push('Mismatched HTML tags detected');
    }
    // Check for required HTML structure
    if (!content.includes('<!DOCTYPE') && !content.includes('<html')) {
        warnings.push('Missing DOCTYPE or html tag');
    }
}
function validateCSSSyntax(content, errors, warnings) {
    // Basic CSS validation
    const braceCount = (content.match(/\{/g) || []).length - (content.match(/\}/g) || []).length;
    if (braceCount !== 0) {
        errors.push('Mismatched CSS braces');
    }
    // Check for common CSS issues
    if (content.includes('!important')) {
        warnings.push('Use of !important detected (consider refactoring)');
    }
}
function validateBasicSyntax(content, errors, warnings) {
    // Basic validation for any code
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineNumber = i + 1;
        // Check for extremely long lines
        if (line.length > 200) {
            warnings.push(`Line ${lineNumber}: Line is very long (${line.length} characters)`);
        }
        // Check for mixed indentation
        if (line.match(/^\t+ +/) || line.match(/^ +\t/)) {
            warnings.push(`Line ${lineNumber}: Mixed tabs and spaces for indentation`);
        }
    }
}
function validateProjectStructure(files) {
    const errors = [];
    const warnings = [];
    const filePaths = files.map(f => f.path);
    // Check for required files based on project type
    const hasPackageJson = filePaths.some(path => path.endsWith('package.json'));
    const hasReactFiles = files.some(f => f.content.includes('React') || f.path.endsWith('.jsx') || f.path.endsWith('.tsx'));
    if (hasReactFiles && !hasPackageJson) {
        warnings.push('React project detected but no package.json found');
    }
    // Check for duplicate file paths
    const duplicates = filePaths.filter((path, index) => filePaths.indexOf(path) !== index);
    if (duplicates.length > 0) {
        errors.push(`Duplicate file paths detected: ${duplicates.join(', ')}`);
    }
    // Check for proper directory structure
    const hasSourceDir = filePaths.some(path => path.startsWith('src/'));
    const hasConfigFiles = filePaths.some(path => path.endsWith('.config.js') ||
        path.endsWith('.config.ts') ||
        path.endsWith('tsconfig.json'));
    if (files.length > 5 && !hasSourceDir) {
        warnings.push('Consider organizing files in a src/ directory');
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
    };
}
//# sourceMappingURL=data:application/json;base64,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