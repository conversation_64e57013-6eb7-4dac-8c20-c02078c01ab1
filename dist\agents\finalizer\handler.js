"use strict";
/**
 * Finalizer Agent
 * Generates README, deployment scripts, and final artifacts
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const s3_request_presigner_1 = require("@aws-sdk/s3-request-presigner");
const archiver = __importStar(require("archiver"));
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const diagram_generator_1 = require("../../utils/diagram-generator");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Finalizer Agent started', { requestId, event });
    try {
        const { sessionId, specification, artifacts, reviews } = event;
        // Generate comprehensive README
        const readme = await generateComprehensiveReadme(specification, artifacts, reviews, requestId);
        // Generate deployment scripts
        const deploymentScript = await generateDeploymentScript(specification, artifacts, requestId);
        // Generate API documentation
        const apiDocs = await generateAPIDocumentation(specification, artifacts, requestId);
        // Generate architecture diagram
        const architectureDiagram = await generateArchitectureDiagram(specification, artifacts, requestId);
        // Create project bundle
        const projectBundle = await createProjectBundle(sessionId, specification, artifacts, readme, deploymentScript, apiDocs, architectureDiagram);
        // Store final artifacts
        const finalOutput = await storeFinalArtifacts(sessionId, projectBundle, readme, deploymentScript, apiDocs, architectureDiagram);
        // Update workflow state to completed
        await updateWorkflowState(sessionId, 'completed', finalOutput);
        logger_1.logger.info('Finalization completed', {
            requestId,
            sessionId,
            bundleSize: projectBundle.size,
            filesCount: artifacts.reduce((sum, a) => sum + a.files.length, 0)
        });
        return finalOutput;
    }
    catch (error) {
        logger_1.logger.error('Finalizer Agent failed', { requestId, error });
        // Update workflow state to failed
        await updateWorkflowState(event.sessionId, 'failed');
        throw error;
    }
};
exports.handler = handler;
async function generateComprehensiveReadme(specification, artifacts, reviews, requestId) {
    const readmePrompt = `
You are a technical documentation expert. Generate a comprehensive, professional README.md file for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
        errorsCount: a.errors.length
    })), null, 2)}

REVIEW SUMMARY:
${JSON.stringify(reviews.map(r => ({
        taskId: r.taskId,
        quality: r.overallQuality,
        errorsCount: r.errors.length,
        suggestionsCount: r.suggestions.length
    })), null, 2)}

Create a comprehensive README.md that includes:

1. PROJECT OVERVIEW
   - Clear project description
   - Key features and capabilities
   - Technology stack
   - Architecture overview

2. GETTING STARTED
   - Prerequisites
   - Installation instructions
   - Quick start guide
   - Configuration setup

3. ARCHITECTURE
   - System architecture description
   - Component relationships
   - Data flow
   - Integration points

4. API DOCUMENTATION
   - Available endpoints
   - Request/response examples
   - Authentication requirements

5. DEPLOYMENT
   - Deployment options
   - Environment setup
   - CI/CD pipeline
   - Monitoring and logging

6. DEVELOPMENT
   - Development setup
   - Testing instructions
   - Contributing guidelines
   - Code structure

7. TROUBLESHOOTING
   - Common issues and solutions
   - Debugging tips
   - Performance optimization

8. ADDITIONAL RESOURCES
   - Links to documentation
   - Support information
   - License information

Make the README professional, comprehensive, and easy to follow. Use proper Markdown formatting with clear sections, code blocks, and examples.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(readmePrompt, {
            temperature: 0.3,
            maxTokens: 6000,
            requestId,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('README generation failed', { requestId, error });
        return generateFallbackReadme(specification);
    }
}
async function generateDeploymentScript(specification, artifacts, requestId) {
    const deploymentPrompt = `
Generate a comprehensive deployment script for this project based on the specification and generated artifacts.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language }))
    })), null, 2)}

Create a deployment script that:

1. Sets up all necessary AWS resources
2. Configures proper IAM roles and policies
3. Sets up networking and security groups
4. Configures databases and storage
5. Sets up monitoring and logging
6. Includes environment variables and secrets management
7. Configures auto-scaling and load balancing
8. Sets up CI/CD pipeline

Choose the most appropriate deployment tool (CDK, CloudFormation, or Terraform) based on the project requirements.

Return the deployment script as a complete, production-ready configuration.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(deploymentPrompt, {
            temperature: 0.2,
            maxTokens: 8000,
            requestId,
        });
        // Determine deployment type based on content
        let type = 'cdk';
        if (response.includes('terraform'))
            type = 'terraform';
        else if (response.includes('AWSTemplateFormatVersion'))
            type = 'cloudformation';
        return { content: response, type };
    }
    catch (error) {
        logger_1.logger.error('Deployment script generation failed', { requestId, error });
        return {
            content: generateFallbackDeploymentScript(specification),
            type: 'cdk'
        };
    }
}
async function generateAPIDocumentation(specification, artifacts, requestId) {
    const apiDocPrompt = `
Generate comprehensive API documentation in OpenAPI 3.0 format for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED CODE ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.filter(f => f.type === 'source').map(f => ({ path: f.path, language: f.language }))
    })), null, 2)}

Create a complete OpenAPI specification that includes:

1. API information and metadata
2. Server configurations
3. All endpoints with detailed descriptions
4. Request/response schemas
5. Authentication and security schemes
6. Error response formats
7. Examples for all endpoints
8. Data models and components

Make sure the documentation is comprehensive and follows OpenAPI 3.0 standards.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(apiDocPrompt, {
            temperature: 0.2,
            maxTokens: 6000,
            requestId,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('API documentation generation failed', { requestId, error });
        return generateFallbackAPIDoc(specification);
    }
}
async function generateArchitectureDiagram(specification, artifacts, requestId) {
    try {
        return await (0, diagram_generator_1.generateMermaidDiagram)(specification, artifacts);
    }
    catch (error) {
        logger_1.logger.error('Architecture diagram generation failed', { requestId, error });
        return generateFallbackDiagram(specification);
    }
}
async function createProjectBundle(sessionId, specification, artifacts, readme, deploymentScript, apiDocs, architectureDiagram) {
    return new Promise((resolve, reject) => {
        const archive = archiver('zip', { zlib: { level: 9 } });
        const chunks = [];
        archive.on('data', (chunk) => chunks.push(chunk));
        archive.on('end', () => {
            const buffer = Buffer.concat(chunks);
            resolve({ buffer, size: buffer.length });
        });
        archive.on('error', reject);
        // Add README
        archive.append(readme, { name: 'README.md' });
        // Add deployment script
        const deploymentFileName = `deployment.${deploymentScript.type === 'terraform' ? 'tf' : 'ts'}`;
        archive.append(deploymentScript.content, { name: deploymentFileName });
        // Add API documentation
        archive.append(apiDocs, { name: 'api-docs.yaml' });
        // Add architecture diagram
        archive.append(architectureDiagram, { name: 'architecture.mmd' });
        // Add all generated files
        for (const artifact of artifacts) {
            for (const file of artifact.files) {
                archive.append(file.content, { name: file.path });
            }
        }
        // Add package.json if not already present
        const hasPackageJson = artifacts.some(a => a.files.some(f => f.path === 'package.json'));
        if (!hasPackageJson && specification.techStack.frontend.framework.includes('React')) {
            const packageJson = generatePackageJson(specification);
            archive.append(packageJson, { name: 'package.json' });
        }
        archive.finalize();
    });
}
async function storeFinalArtifacts(sessionId, projectBundle, readme, deploymentScript, apiDocs, architectureDiagram) {
    const timestamp = Date.now();
    // Store project bundle
    const bundleKey = `sessions/${sessionId}/final/project-bundle-${timestamp}.zip`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: bundleKey,
        Body: projectBundle.buffer,
        ContentType: 'application/zip',
        Metadata: {
            sessionId,
            type: 'project-bundle',
            size: projectBundle.size.toString(),
        },
    }));
    // Store README
    const readmeKey = `sessions/${sessionId}/final/README.md`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: readmeKey,
        Body: readme,
        ContentType: 'text/markdown',
    }));
    // Store deployment script
    const deploymentKey = `sessions/${sessionId}/final/deployment.${deploymentScript.type}`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: deploymentKey,
        Body: deploymentScript.content,
        ContentType: 'text/plain',
    }));
    // Generate download URL
    const downloadUrl = await (0, s3_request_presigner_1.getSignedUrl)(s3Client, new client_s3_1.GetObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: bundleKey,
    }), { expiresIn: 3600 * 24 * 7 } // 7 days
    );
    return {
        projectBundle: {
            s3Key: bundleKey,
            downloadUrl,
        },
        readme: {
            s3Key: readmeKey,
            content: readme,
        },
        deploymentScript: {
            s3Key: deploymentKey,
            type: deploymentScript.type,
        },
        documentation: {
            apiDocs,
            architectureDiagram,
            userGuide: readme,
        },
    };
}
async function updateWorkflowState(sessionId, status, finalOutput) {
    await dynamoClient.send(new lib_dynamodb_1.UpdateCommand({
        TableName: TABLE_NAME,
        Key: {
            PK: `SESSION#${sessionId}`,
            SK: `WORKFLOW#${Date.now()}`,
        },
        UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, finalOutput = :finalOutput',
        ExpressionAttributeNames: {
            '#status': 'status',
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':updatedAt': new Date().toISOString(),
            ':finalOutput': finalOutput ? JSON.stringify(finalOutput) : null,
        },
    }));
}
// Fallback functions
function generateFallbackReadme(specification) {
    return `# ${specification.projectName}

${specification.description}

## Features

${specification.features.map(f => `- ${f.name}: ${f.description}`).join('\n')}

## Tech Stack

- Frontend: ${specification.techStack.frontend.framework}
- Backend: ${specification.techStack.backend.framework}
- Database: ${specification.techStack.backend.database}

## Getting Started

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start the development server: \`npm run dev\`

## Deployment

Follow the deployment script instructions in the deployment files.
`;
}
function generateFallbackDeploymentScript(specification) {
    return `// CDK Deployment Script for ${specification.projectName}
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';

export class ${specification.projectName.replace(/[^a-zA-Z0-9]/g, '')}Stack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    
    // Add your AWS resources here
  }
}`;
}
function generateFallbackAPIDoc(specification) {
    return `openapi: 3.0.0
info:
  title: ${specification.projectName} API
  description: ${specification.description}
  version: 1.0.0
paths: {}`;
}
function generateFallbackDiagram(specification) {
    return `graph TD
    A[Frontend] --> B[Backend]
    B --> C[Database]`;
}
function generatePackageJson(specification) {
    return JSON.stringify({
        name: specification.projectName,
        version: "1.0.0",
        description: specification.description,
        scripts: {
            dev: "npm run dev",
            build: "npm run build",
            start: "npm start"
        },
        dependencies: {},
        devDependencies: {}
    }, null, 2);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaGFuZGxlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy9hZ2VudHMvZmluYWxpemVyL2hhbmRsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7R0FHRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBR0gsOERBQTBEO0FBQzFELHdEQUEwRjtBQUMxRixrREFBa0Y7QUFDbEYsd0VBQTZEO0FBQzdELG1EQUFxQztBQVNyQyx1REFBbUQ7QUFDbkQsK0NBQTRDO0FBQzVDLHFFQUF1RTtBQUV2RSxNQUFNLFlBQVksR0FBRyxxQ0FBc0IsQ0FBQyxJQUFJLENBQUMsSUFBSSxnQ0FBYyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFDekUsTUFBTSxRQUFRLEdBQUcsSUFBSSxvQkFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDO0FBQ2xDLE1BQU0sVUFBVSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUJBQW9CLENBQUM7QUFDcEQsTUFBTSxnQkFBZ0IsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLHFCQUFzQixDQUFDO0FBRXJELE1BQU0sT0FBTyxHQUFHLEtBQUssRUFDMUIsS0FBcUIsRUFDckIsT0FBZ0IsRUFDTSxFQUFFO0lBQ3hCLE1BQU0sU0FBUyxHQUFHLE9BQU8sQ0FBQyxZQUFZLENBQUM7SUFDdkMsZUFBTSxDQUFDLElBQUksQ0FBQyx5QkFBeUIsRUFBRSxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO0lBRTdELElBQUksQ0FBQztRQUNILE1BQU0sRUFBRSxTQUFTLEVBQUUsYUFBYSxFQUFFLFNBQVMsRUFBRSxPQUFPLEVBQUUsR0FBRyxLQUFLLENBQUM7UUFFL0QsZ0NBQWdDO1FBQ2hDLE1BQU0sTUFBTSxHQUFHLE1BQU0sMkJBQTJCLENBQzlDLGFBQWEsRUFDYixTQUFTLEVBQ1QsT0FBTyxFQUNQLFNBQVMsQ0FDVixDQUFDO1FBRUYsOEJBQThCO1FBQzlCLE1BQU0sZ0JBQWdCLEdBQUcsTUFBTSx3QkFBd0IsQ0FDckQsYUFBYSxFQUNiLFNBQVMsRUFDVCxTQUFTLENBQ1YsQ0FBQztRQUVGLDZCQUE2QjtRQUM3QixNQUFNLE9BQU8sR0FBRyxNQUFNLHdCQUF3QixDQUM1QyxhQUFhLEVBQ2IsU0FBUyxFQUNULFNBQVMsQ0FDVixDQUFDO1FBRUYsZ0NBQWdDO1FBQ2hDLE1BQU0sbUJBQW1CLEdBQUcsTUFBTSwyQkFBMkIsQ0FDM0QsYUFBYSxFQUNiLFNBQVMsRUFDVCxTQUFTLENBQ1YsQ0FBQztRQUVGLHdCQUF3QjtRQUN4QixNQUFNLGFBQWEsR0FBRyxNQUFNLG1CQUFtQixDQUM3QyxTQUFTLEVBQ1QsYUFBYSxFQUNiLFNBQVMsRUFDVCxNQUFNLEVBQ04sZ0JBQWdCLEVBQ2hCLE9BQU8sRUFDUCxtQkFBbUIsQ0FDcEIsQ0FBQztRQUVGLHdCQUF3QjtRQUN4QixNQUFNLFdBQVcsR0FBRyxNQUFNLG1CQUFtQixDQUMzQyxTQUFTLEVBQ1QsYUFBYSxFQUNiLE1BQU0sRUFDTixnQkFBZ0IsRUFDaEIsT0FBTyxFQUNQLG1CQUFtQixDQUNwQixDQUFDO1FBRUYscUNBQXFDO1FBQ3JDLE1BQU0sbUJBQW1CLENBQUMsU0FBUyxFQUFFLFdBQVcsRUFBRSxXQUFXLENBQUMsQ0FBQztRQUUvRCxlQUFNLENBQUMsSUFBSSxDQUFDLHdCQUF3QixFQUFFO1lBQ3BDLFNBQVM7WUFDVCxTQUFTO1lBQ1QsVUFBVSxFQUFFLGFBQWEsQ0FBQyxJQUFJO1lBQzlCLFVBQVUsRUFBRSxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxLQUFLLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQztTQUNsRSxDQUFDLENBQUM7UUFFSCxPQUFPLFdBQVcsQ0FBQztJQUVyQixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLGVBQU0sQ0FBQyxLQUFLLENBQUMsd0JBQXdCLEVBQUUsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztRQUU3RCxrQ0FBa0M7UUFDbEMsTUFBTSxtQkFBbUIsQ0FBQyxLQUFLLENBQUMsU0FBUyxFQUFFLFFBQVEsQ0FBQyxDQUFDO1FBRXJELE1BQU0sS0FBSyxDQUFDO0lBQ2QsQ0FBQztBQUNILENBQUMsQ0FBQztBQWhGVyxRQUFBLE9BQU8sV0FnRmxCO0FBRUYsS0FBSyxVQUFVLDJCQUEyQixDQUN4QyxhQUFvQyxFQUNwQyxTQUF5QixFQUN6QixPQUF1QixFQUN2QixTQUFpQjtJQUdqQixNQUFNLFlBQVksR0FBRzs7OztFQUlyQixJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDOzs7RUFHdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUNuQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLE1BQU07UUFDaEIsS0FBSyxFQUFFLENBQUMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFLFFBQVEsRUFBRSxDQUFDLENBQUMsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUMvRSxXQUFXLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxNQUFNO0tBQzdCLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7OztFQUdYLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDakMsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNO1FBQ2hCLE9BQU8sRUFBRSxDQUFDLENBQUMsY0FBYztRQUN6QixXQUFXLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxNQUFNO1FBQzVCLGdCQUFnQixFQUFFLENBQUMsQ0FBQyxXQUFXLENBQUMsTUFBTTtLQUN2QyxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQWtEWixDQUFDO0lBRUEsSUFBSSxDQUFDO1FBQ0gsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLHNCQUFTLEVBQUMsWUFBWSxFQUFFO1lBQzdDLFdBQVcsRUFBRSxHQUFHO1lBQ2hCLFNBQVMsRUFBRSxJQUFJO1lBQ2YsU0FBUztTQUNWLENBQUMsQ0FBQztRQUVILE9BQU8sUUFBUSxDQUFDO0lBRWxCLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsZUFBTSxDQUFDLEtBQUssQ0FBQywwQkFBMEIsRUFBRSxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQy9ELE9BQU8sc0JBQXNCLENBQUMsYUFBYSxDQUFDLENBQUM7SUFDL0MsQ0FBQztBQUNILENBQUM7QUFFRCxLQUFLLFVBQVUsd0JBQXdCLENBQ3JDLGFBQW9DLEVBQ3BDLFNBQXlCLEVBQ3pCLFNBQWlCO0lBR2pCLE1BQU0sZ0JBQWdCLEdBQUc7Ozs7RUFJekIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQzs7O0VBR3RDLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDbkMsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNO1FBQ2hCLEtBQUssRUFBRSxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxRQUFRLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7S0FDaEYsQ0FBQyxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7OztDQWdCWixDQUFDO0lBRUEsSUFBSSxDQUFDO1FBQ0gsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLHNCQUFTLEVBQUMsZ0JBQWdCLEVBQUU7WUFDakQsV0FBVyxFQUFFLEdBQUc7WUFDaEIsU0FBUyxFQUFFLElBQUk7WUFDZixTQUFTO1NBQ1YsQ0FBQyxDQUFDO1FBRUgsNkNBQTZDO1FBQzdDLElBQUksSUFBSSxHQUEyQyxLQUFLLENBQUM7UUFDekQsSUFBSSxRQUFRLENBQUMsUUFBUSxDQUFDLFdBQVcsQ0FBQztZQUFFLElBQUksR0FBRyxXQUFXLENBQUM7YUFDbEQsSUFBSSxRQUFRLENBQUMsUUFBUSxDQUFDLDBCQUEwQixDQUFDO1lBQUUsSUFBSSxHQUFHLGdCQUFnQixDQUFDO1FBRWhGLE9BQU8sRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBRXJDLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsZUFBTSxDQUFDLEtBQUssQ0FBQyxxQ0FBcUMsRUFBRSxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQzFFLE9BQU87WUFDTCxPQUFPLEVBQUUsZ0NBQWdDLENBQUMsYUFBYSxDQUFDO1lBQ3hELElBQUksRUFBRSxLQUFLO1NBQ1osQ0FBQztJQUNKLENBQUM7QUFDSCxDQUFDO0FBRUQsS0FBSyxVQUFVLHdCQUF3QixDQUNyQyxhQUFvQyxFQUNwQyxTQUF5QixFQUN6QixTQUFpQjtJQUdqQixNQUFNLFlBQVksR0FBRzs7OztFQUlyQixJQUFJLENBQUMsU0FBUyxDQUFDLGFBQWEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDOzs7RUFHdEMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUNuQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLE1BQU07UUFDaEIsS0FBSyxFQUFFLENBQUMsQ0FBQyxLQUFLLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxRQUFRLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQyxJQUFJLEVBQUUsUUFBUSxFQUFFLENBQUMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxDQUFDO0tBQ25HLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7O0NBY1osQ0FBQztJQUVBLElBQUksQ0FBQztRQUNILE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxzQkFBUyxFQUFDLFlBQVksRUFBRTtZQUM3QyxXQUFXLEVBQUUsR0FBRztZQUNoQixTQUFTLEVBQUUsSUFBSTtZQUNmLFNBQVM7U0FDVixDQUFDLENBQUM7UUFFSCxPQUFPLFFBQVEsQ0FBQztJQUVsQixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLGVBQU0sQ0FBQyxLQUFLLENBQUMscUNBQXFDLEVBQUUsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztRQUMxRSxPQUFPLHNCQUFzQixDQUFDLGFBQWEsQ0FBQyxDQUFDO0lBQy9DLENBQUM7QUFDSCxDQUFDO0FBRUQsS0FBSyxVQUFVLDJCQUEyQixDQUN4QyxhQUFvQyxFQUNwQyxTQUF5QixFQUN6QixTQUFpQjtJQUdqQixJQUFJLENBQUM7UUFDSCxPQUFPLE1BQU0sSUFBQSwwQ0FBc0IsRUFBQyxhQUFhLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFDaEUsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixlQUFNLENBQUMsS0FBSyxDQUFDLHdDQUF3QyxFQUFFLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7UUFDN0UsT0FBTyx1QkFBdUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztJQUNoRCxDQUFDO0FBQ0gsQ0FBQztBQUVELEtBQUssVUFBVSxtQkFBbUIsQ0FDaEMsU0FBaUIsRUFDakIsYUFBb0MsRUFDcEMsU0FBeUIsRUFDekIsTUFBYyxFQUNkLGdCQUFtRCxFQUNuRCxPQUFlLEVBQ2YsbUJBQTJCO0lBRzNCLE9BQU8sSUFBSSxPQUFPLENBQUMsQ0FBQyxPQUFPLEVBQUUsTUFBTSxFQUFFLEVBQUU7UUFDckMsTUFBTSxPQUFPLEdBQUcsUUFBUSxDQUFDLEtBQUssRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLEtBQUssRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDeEQsTUFBTSxNQUFNLEdBQWEsRUFBRSxDQUFDO1FBRTVCLE9BQU8sQ0FBQyxFQUFFLENBQUMsTUFBTSxFQUFFLENBQUMsS0FBSyxFQUFFLEVBQUUsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUM7UUFDbEQsT0FBTyxDQUFDLEVBQUUsQ0FBQyxLQUFLLEVBQUUsR0FBRyxFQUFFO1lBQ3JCLE1BQU0sTUFBTSxHQUFHLE1BQU0sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDckMsT0FBTyxDQUFDLEVBQUUsTUFBTSxFQUFFLElBQUksRUFBRSxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQztRQUMzQyxDQUFDLENBQUMsQ0FBQztRQUNILE9BQU8sQ0FBQyxFQUFFLENBQUMsT0FBTyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRTVCLGFBQWE7UUFDYixPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxFQUFFLElBQUksRUFBRSxXQUFXLEVBQUUsQ0FBQyxDQUFDO1FBRTlDLHdCQUF3QjtRQUN4QixNQUFNLGtCQUFrQixHQUFHLGNBQWMsZ0JBQWdCLENBQUMsSUFBSSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUUsQ0FBQztRQUMvRixPQUFPLENBQUMsTUFBTSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sRUFBRSxFQUFFLElBQUksRUFBRSxrQkFBa0IsRUFBRSxDQUFDLENBQUM7UUFFdkUsd0JBQXdCO1FBQ3hCLE9BQU8sQ0FBQyxNQUFNLENBQUMsT0FBTyxFQUFFLEVBQUUsSUFBSSxFQUFFLGVBQWUsRUFBRSxDQUFDLENBQUM7UUFFbkQsMkJBQTJCO1FBQzNCLE9BQU8sQ0FBQyxNQUFNLENBQUMsbUJBQW1CLEVBQUUsRUFBRSxJQUFJLEVBQUUsa0JBQWtCLEVBQUUsQ0FBQyxDQUFDO1FBRWxFLDBCQUEwQjtRQUMxQixLQUFLLE1BQU0sUUFBUSxJQUFJLFNBQVMsRUFBRSxDQUFDO1lBQ2pDLEtBQUssTUFBTSxJQUFJLElBQUksUUFBUSxDQUFDLEtBQUssRUFBRSxDQUFDO2dCQUNsQyxPQUFPLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7WUFDcEQsQ0FBQztRQUNILENBQUM7UUFFRCwwQ0FBMEM7UUFDMUMsTUFBTSxjQUFjLEdBQUcsU0FBUyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUN4QyxDQUFDLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLEtBQUssY0FBYyxDQUFDLENBQzdDLENBQUM7UUFFRixJQUFJLENBQUMsY0FBYyxJQUFJLGFBQWEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztZQUNwRixNQUFNLFdBQVcsR0FBRyxtQkFBbUIsQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUN2RCxPQUFPLENBQUMsTUFBTSxDQUFDLFdBQVcsRUFBRSxFQUFFLElBQUksRUFBRSxjQUFjLEVBQUUsQ0FBQyxDQUFDO1FBQ3hELENBQUM7UUFFRCxPQUFPLENBQUMsUUFBUSxFQUFFLENBQUM7SUFDckIsQ0FBQyxDQUFDLENBQUM7QUFDTCxDQUFDO0FBRUQsS0FBSyxVQUFVLG1CQUFtQixDQUNoQyxTQUFpQixFQUNqQixhQUErQyxFQUMvQyxNQUFjLEVBQ2QsZ0JBQW1ELEVBQ25ELE9BQWUsRUFDZixtQkFBMkI7SUFHM0IsTUFBTSxTQUFTLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO0lBRTdCLHVCQUF1QjtJQUN2QixNQUFNLFNBQVMsR0FBRyxZQUFZLFNBQVMseUJBQXlCLFNBQVMsTUFBTSxDQUFDO0lBQ2hGLE1BQU0sUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJLDRCQUFnQixDQUFDO1FBQ3ZDLE1BQU0sRUFBRSxnQkFBZ0I7UUFDeEIsR0FBRyxFQUFFLFNBQVM7UUFDZCxJQUFJLEVBQUUsYUFBYSxDQUFDLE1BQU07UUFDMUIsV0FBVyxFQUFFLGlCQUFpQjtRQUM5QixRQUFRLEVBQUU7WUFDUixTQUFTO1lBQ1QsSUFBSSxFQUFFLGdCQUFnQjtZQUN0QixJQUFJLEVBQUUsYUFBYSxDQUFDLElBQUksQ0FBQyxRQUFRLEVBQUU7U0FDcEM7S0FDRixDQUFDLENBQUMsQ0FBQztJQUVKLGVBQWU7SUFDZixNQUFNLFNBQVMsR0FBRyxZQUFZLFNBQVMsa0JBQWtCLENBQUM7SUFDMUQsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksNEJBQWdCLENBQUM7UUFDdkMsTUFBTSxFQUFFLGdCQUFnQjtRQUN4QixHQUFHLEVBQUUsU0FBUztRQUNkLElBQUksRUFBRSxNQUFNO1FBQ1osV0FBVyxFQUFFLGVBQWU7S0FDN0IsQ0FBQyxDQUFDLENBQUM7SUFFSiwwQkFBMEI7SUFDMUIsTUFBTSxhQUFhLEdBQUcsWUFBWSxTQUFTLHFCQUFxQixnQkFBZ0IsQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUN4RixNQUFNLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSSw0QkFBZ0IsQ0FBQztRQUN2QyxNQUFNLEVBQUUsZ0JBQWdCO1FBQ3hCLEdBQUcsRUFBRSxhQUFhO1FBQ2xCLElBQUksRUFBRSxnQkFBZ0IsQ0FBQyxPQUFPO1FBQzlCLFdBQVcsRUFBRSxZQUFZO0tBQzFCLENBQUMsQ0FBQyxDQUFDO0lBRUosd0JBQXdCO0lBQ3hCLE1BQU0sV0FBVyxHQUFHLE1BQU0sSUFBQSxtQ0FBWSxFQUNwQyxRQUFRLEVBQ1IsSUFBSSw0QkFBZ0IsQ0FBQztRQUNuQixNQUFNLEVBQUUsZ0JBQWdCO1FBQ3hCLEdBQUcsRUFBRSxTQUFTO0tBQ2YsQ0FBQyxFQUNGLEVBQUUsU0FBUyxFQUFFLElBQUksR0FBRyxFQUFFLEdBQUcsQ0FBQyxFQUFFLENBQUMsU0FBUztLQUN2QyxDQUFDO0lBRUYsT0FBTztRQUNMLGFBQWEsRUFBRTtZQUNiLEtBQUssRUFBRSxTQUFTO1lBQ2hCLFdBQVc7U0FDWjtRQUNELE1BQU0sRUFBRTtZQUNOLEtBQUssRUFBRSxTQUFTO1lBQ2hCLE9BQU8sRUFBRSxNQUFNO1NBQ2hCO1FBQ0QsZ0JBQWdCLEVBQUU7WUFDaEIsS0FBSyxFQUFFLGFBQWE7WUFDcEIsSUFBSSxFQUFFLGdCQUFnQixDQUFDLElBQThDO1NBQ3RFO1FBQ0QsYUFBYSxFQUFFO1lBQ2IsT0FBTztZQUNQLG1CQUFtQjtZQUNuQixTQUFTLEVBQUUsTUFBTTtTQUNsQjtLQUNGLENBQUM7QUFDSixDQUFDO0FBRUQsS0FBSyxVQUFVLG1CQUFtQixDQUNoQyxTQUFpQixFQUNqQixNQUE4QixFQUM5QixXQUF5QjtJQUd6QixNQUFNLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSw0QkFBYSxDQUFDO1FBQ3hDLFNBQVMsRUFBRSxVQUFVO1FBQ3JCLEdBQUcsRUFBRTtZQUNILEVBQUUsRUFBRSxXQUFXLFNBQVMsRUFBRTtZQUMxQixFQUFFLEVBQUUsWUFBWSxJQUFJLENBQUMsR0FBRyxFQUFFLEVBQUU7U0FDN0I7UUFDRCxnQkFBZ0IsRUFBRSwyRUFBMkU7UUFDN0Ysd0JBQXdCLEVBQUU7WUFDeEIsU0FBUyxFQUFFLFFBQVE7U0FDcEI7UUFDRCx5QkFBeUIsRUFBRTtZQUN6QixTQUFTLEVBQUUsTUFBTTtZQUNqQixZQUFZLEVBQUUsSUFBSSxJQUFJLEVBQUUsQ0FBQyxXQUFXLEVBQUU7WUFDdEMsY0FBYyxFQUFFLFdBQVcsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUMsSUFBSTtTQUNqRTtLQUNGLENBQUMsQ0FBQyxDQUFDO0FBQ04sQ0FBQztBQUVELHFCQUFxQjtBQUNyQixTQUFTLHNCQUFzQixDQUFDLGFBQW9DO0lBQ2xFLE9BQU8sS0FBSyxhQUFhLENBQUMsV0FBVzs7RUFFckMsYUFBYSxDQUFDLFdBQVc7Ozs7RUFJekIsYUFBYSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQyxJQUFJLEtBQUssQ0FBQyxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQzs7OztjQUkvRCxhQUFhLENBQUMsU0FBUyxDQUFDLFFBQVEsQ0FBQyxTQUFTO2FBQzNDLGFBQWEsQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLFNBQVM7Y0FDeEMsYUFBYSxDQUFDLFNBQVMsQ0FBQyxPQUFPLENBQUMsUUFBUTs7Ozs7Ozs7Ozs7Q0FXckQsQ0FBQztBQUNGLENBQUM7QUFFRCxTQUFTLGdDQUFnQyxDQUFDLGFBQW9DO0lBQzVFLE9BQU8sZ0NBQWdDLGFBQWEsQ0FBQyxXQUFXOzs7O2VBSW5ELGFBQWEsQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUM7Ozs7OztFQU1uRSxDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVMsc0JBQXNCLENBQUMsYUFBb0M7SUFDbEUsT0FBTzs7V0FFRSxhQUFhLENBQUMsV0FBVztpQkFDbkIsYUFBYSxDQUFDLFdBQVc7O1VBRWhDLENBQUM7QUFDWCxDQUFDO0FBRUQsU0FBUyx1QkFBdUIsQ0FBQyxhQUFvQztJQUNuRSxPQUFPOztzQkFFYSxDQUFDO0FBQ3ZCLENBQUM7QUFFRCxTQUFTLG1CQUFtQixDQUFDLGFBQW9DO0lBQy9ELE9BQU8sSUFBSSxDQUFDLFNBQVMsQ0FBQztRQUNwQixJQUFJLEVBQUUsYUFBYSxDQUFDLFdBQVc7UUFDL0IsT0FBTyxFQUFFLE9BQU87UUFDaEIsV0FBVyxFQUFFLGFBQWEsQ0FBQyxXQUFXO1FBQ3RDLE9BQU8sRUFBRTtZQUNQLEdBQUcsRUFBRSxhQUFhO1lBQ2xCLEtBQUssRUFBRSxlQUFlO1lBQ3RCLEtBQUssRUFBRSxXQUFXO1NBQ25CO1FBQ0QsWUFBWSxFQUFFLEVBQUU7UUFDaEIsZUFBZSxFQUFFLEVBQUU7S0FDcEIsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFDZCxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBGaW5hbGl6ZXIgQWdlbnRcbiAqIEdlbmVyYXRlcyBSRUFETUUsIGRlcGxveW1lbnQgc2NyaXB0cywgYW5kIGZpbmFsIGFydGlmYWN0c1xuICovXG5cbmltcG9ydCB7IENvbnRleHQgfSBmcm9tICdhd3MtbGFtYmRhJztcbmltcG9ydCB7IER5bmFtb0RCQ2xpZW50IH0gZnJvbSAnQGF3cy1zZGsvY2xpZW50LWR5bmFtb2RiJztcbmltcG9ydCB7IER5bmFtb0RCRG9jdW1lbnRDbGllbnQsIFB1dENvbW1hbmQsIFVwZGF0ZUNvbW1hbmQgfSBmcm9tICdAYXdzLXNkay9saWItZHluYW1vZGInO1xuaW1wb3J0IHsgUzNDbGllbnQsIFB1dE9iamVjdENvbW1hbmQsIEdldE9iamVjdENvbW1hbmQgfSBmcm9tICdAYXdzLXNkay9jbGllbnQtczMnO1xuaW1wb3J0IHsgZ2V0U2lnbmVkVXJsIH0gZnJvbSAnQGF3cy1zZGsvczMtcmVxdWVzdC1wcmVzaWduZXInO1xuaW1wb3J0ICogYXMgYXJjaGl2ZXIgZnJvbSAnYXJjaGl2ZXInO1xuaW1wb3J0IHsgXG4gIEZpbmFsaXplckV2ZW50LCBcbiAgRmluYWxPdXRwdXQsIFxuICBDb2RlQXJ0aWZhY3QsIFxuICBSZXZpZXdSZXN1bHQsXG4gIERldGFpbGVkU3BlY2lmaWNhdGlvbixcbiAgR2VuZXJhdGVkRmlsZVxufSBmcm9tICcuLi8uLi90eXBlcyc7XG5pbXBvcnQgeyBpbnZva2VMTE0gfSBmcm9tICcuLi8uLi91dGlscy9sbG0tY2xpZW50JztcbmltcG9ydCB7IGxvZ2dlciB9IGZyb20gJy4uLy4uL3V0aWxzL2xvZ2dlcic7XG5pbXBvcnQgeyBnZW5lcmF0ZU1lcm1haWREaWFncmFtIH0gZnJvbSAnLi4vLi4vdXRpbHMvZGlhZ3JhbS1nZW5lcmF0b3InO1xuXG5jb25zdCBkeW5hbW9DbGllbnQgPSBEeW5hbW9EQkRvY3VtZW50Q2xpZW50LmZyb20obmV3IER5bmFtb0RCQ2xpZW50KHt9KSk7XG5jb25zdCBzM0NsaWVudCA9IG5ldyBTM0NsaWVudCh7fSk7XG5jb25zdCBUQUJMRV9OQU1FID0gcHJvY2Vzcy5lbnYuV09SS0ZMT1dfVEFCTEVfTkFNRSE7XG5jb25zdCBBUlRJRkFDVFNfQlVDS0VUID0gcHJvY2Vzcy5lbnYuQVJUSUZBQ1RTX0JVQ0tFVF9OQU1FITtcblxuZXhwb3J0IGNvbnN0IGhhbmRsZXIgPSBhc3luYyAoXG4gIGV2ZW50OiBGaW5hbGl6ZXJFdmVudCxcbiAgY29udGV4dDogQ29udGV4dFxuKTogUHJvbWlzZTxGaW5hbE91dHB1dD4gPT4ge1xuICBjb25zdCByZXF1ZXN0SWQgPSBjb250ZXh0LmF3c1JlcXVlc3RJZDtcbiAgbG9nZ2VyLmluZm8oJ0ZpbmFsaXplciBBZ2VudCBzdGFydGVkJywgeyByZXF1ZXN0SWQsIGV2ZW50IH0pO1xuXG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZXNzaW9uSWQsIHNwZWNpZmljYXRpb24sIGFydGlmYWN0cywgcmV2aWV3cyB9ID0gZXZlbnQ7XG5cbiAgICAvLyBHZW5lcmF0ZSBjb21wcmVoZW5zaXZlIFJFQURNRVxuICAgIGNvbnN0IHJlYWRtZSA9IGF3YWl0IGdlbmVyYXRlQ29tcHJlaGVuc2l2ZVJlYWRtZShcbiAgICAgIHNwZWNpZmljYXRpb24sIFxuICAgICAgYXJ0aWZhY3RzLCBcbiAgICAgIHJldmlld3MsIFxuICAgICAgcmVxdWVzdElkXG4gICAgKTtcblxuICAgIC8vIEdlbmVyYXRlIGRlcGxveW1lbnQgc2NyaXB0c1xuICAgIGNvbnN0IGRlcGxveW1lbnRTY3JpcHQgPSBhd2FpdCBnZW5lcmF0ZURlcGxveW1lbnRTY3JpcHQoXG4gICAgICBzcGVjaWZpY2F0aW9uLCBcbiAgICAgIGFydGlmYWN0cywgXG4gICAgICByZXF1ZXN0SWRcbiAgICApO1xuXG4gICAgLy8gR2VuZXJhdGUgQVBJIGRvY3VtZW50YXRpb25cbiAgICBjb25zdCBhcGlEb2NzID0gYXdhaXQgZ2VuZXJhdGVBUElEb2N1bWVudGF0aW9uKFxuICAgICAgc3BlY2lmaWNhdGlvbiwgXG4gICAgICBhcnRpZmFjdHMsIFxuICAgICAgcmVxdWVzdElkXG4gICAgKTtcblxuICAgIC8vIEdlbmVyYXRlIGFyY2hpdGVjdHVyZSBkaWFncmFtXG4gICAgY29uc3QgYXJjaGl0ZWN0dXJlRGlhZ3JhbSA9IGF3YWl0IGdlbmVyYXRlQXJjaGl0ZWN0dXJlRGlhZ3JhbShcbiAgICAgIHNwZWNpZmljYXRpb24sIFxuICAgICAgYXJ0aWZhY3RzLCBcbiAgICAgIHJlcXVlc3RJZFxuICAgICk7XG5cbiAgICAvLyBDcmVhdGUgcHJvamVjdCBidW5kbGVcbiAgICBjb25zdCBwcm9qZWN0QnVuZGxlID0gYXdhaXQgY3JlYXRlUHJvamVjdEJ1bmRsZShcbiAgICAgIHNlc3Npb25JZCxcbiAgICAgIHNwZWNpZmljYXRpb24sXG4gICAgICBhcnRpZmFjdHMsXG4gICAgICByZWFkbWUsXG4gICAgICBkZXBsb3ltZW50U2NyaXB0LFxuICAgICAgYXBpRG9jcyxcbiAgICAgIGFyY2hpdGVjdHVyZURpYWdyYW1cbiAgICApO1xuXG4gICAgLy8gU3RvcmUgZmluYWwgYXJ0aWZhY3RzXG4gICAgY29uc3QgZmluYWxPdXRwdXQgPSBhd2FpdCBzdG9yZUZpbmFsQXJ0aWZhY3RzKFxuICAgICAgc2Vzc2lvbklkLFxuICAgICAgcHJvamVjdEJ1bmRsZSxcbiAgICAgIHJlYWRtZSxcbiAgICAgIGRlcGxveW1lbnRTY3JpcHQsXG4gICAgICBhcGlEb2NzLFxuICAgICAgYXJjaGl0ZWN0dXJlRGlhZ3JhbVxuICAgICk7XG5cbiAgICAvLyBVcGRhdGUgd29ya2Zsb3cgc3RhdGUgdG8gY29tcGxldGVkXG4gICAgYXdhaXQgdXBkYXRlV29ya2Zsb3dTdGF0ZShzZXNzaW9uSWQsICdjb21wbGV0ZWQnLCBmaW5hbE91dHB1dCk7XG5cbiAgICBsb2dnZXIuaW5mbygnRmluYWxpemF0aW9uIGNvbXBsZXRlZCcsIHsgXG4gICAgICByZXF1ZXN0SWQsIFxuICAgICAgc2Vzc2lvbklkLFxuICAgICAgYnVuZGxlU2l6ZTogcHJvamVjdEJ1bmRsZS5zaXplLFxuICAgICAgZmlsZXNDb3VudDogYXJ0aWZhY3RzLnJlZHVjZSgoc3VtLCBhKSA9PiBzdW0gKyBhLmZpbGVzLmxlbmd0aCwgMClcbiAgICB9KTtcblxuICAgIHJldHVybiBmaW5hbE91dHB1dDtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ2dlci5lcnJvcignRmluYWxpemVyIEFnZW50IGZhaWxlZCcsIHsgcmVxdWVzdElkLCBlcnJvciB9KTtcbiAgICBcbiAgICAvLyBVcGRhdGUgd29ya2Zsb3cgc3RhdGUgdG8gZmFpbGVkXG4gICAgYXdhaXQgdXBkYXRlV29ya2Zsb3dTdGF0ZShldmVudC5zZXNzaW9uSWQsICdmYWlsZWQnKTtcbiAgICBcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVDb21wcmVoZW5zaXZlUmVhZG1lKFxuICBzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIGFydGlmYWN0czogQ29kZUFydGlmYWN0W10sXG4gIHJldmlld3M6IFJldmlld1Jlc3VsdFtdLFxuICByZXF1ZXN0SWQ6IHN0cmluZ1xuKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgXG4gIGNvbnN0IHJlYWRtZVByb21wdCA9IGBcbllvdSBhcmUgYSB0ZWNobmljYWwgZG9jdW1lbnRhdGlvbiBleHBlcnQuIEdlbmVyYXRlIGEgY29tcHJlaGVuc2l2ZSwgcHJvZmVzc2lvbmFsIFJFQURNRS5tZCBmaWxlIGZvciB0aGlzIHByb2plY3QuXG5cblBST0pFQ1QgU1BFQ0lGSUNBVElPTjpcbiR7SlNPTi5zdHJpbmdpZnkoc3BlY2lmaWNhdGlvbiwgbnVsbCwgMil9XG5cbkdFTkVSQVRFRCBBUlRJRkFDVFM6XG4ke0pTT04uc3RyaW5naWZ5KGFydGlmYWN0cy5tYXAoYSA9PiAoe1xuICB0YXNrSWQ6IGEudGFza0lkLFxuICBmaWxlczogYS5maWxlcy5tYXAoZiA9PiAoeyBwYXRoOiBmLnBhdGgsIHR5cGU6IGYudHlwZSwgbGFuZ3VhZ2U6IGYubGFuZ3VhZ2UgfSkpLFxuICBlcnJvcnNDb3VudDogYS5lcnJvcnMubGVuZ3RoXG59KSksIG51bGwsIDIpfVxuXG5SRVZJRVcgU1VNTUFSWTpcbiR7SlNPTi5zdHJpbmdpZnkocmV2aWV3cy5tYXAociA9PiAoe1xuICB0YXNrSWQ6IHIudGFza0lkLFxuICBxdWFsaXR5OiByLm92ZXJhbGxRdWFsaXR5LFxuICBlcnJvcnNDb3VudDogci5lcnJvcnMubGVuZ3RoLFxuICBzdWdnZXN0aW9uc0NvdW50OiByLnN1Z2dlc3Rpb25zLmxlbmd0aFxufSkpLCBudWxsLCAyKX1cblxuQ3JlYXRlIGEgY29tcHJlaGVuc2l2ZSBSRUFETUUubWQgdGhhdCBpbmNsdWRlczpcblxuMS4gUFJPSkVDVCBPVkVSVklFV1xuICAgLSBDbGVhciBwcm9qZWN0IGRlc2NyaXB0aW9uXG4gICAtIEtleSBmZWF0dXJlcyBhbmQgY2FwYWJpbGl0aWVzXG4gICAtIFRlY2hub2xvZ3kgc3RhY2tcbiAgIC0gQXJjaGl0ZWN0dXJlIG92ZXJ2aWV3XG5cbjIuIEdFVFRJTkcgU1RBUlRFRFxuICAgLSBQcmVyZXF1aXNpdGVzXG4gICAtIEluc3RhbGxhdGlvbiBpbnN0cnVjdGlvbnNcbiAgIC0gUXVpY2sgc3RhcnQgZ3VpZGVcbiAgIC0gQ29uZmlndXJhdGlvbiBzZXR1cFxuXG4zLiBBUkNISVRFQ1RVUkVcbiAgIC0gU3lzdGVtIGFyY2hpdGVjdHVyZSBkZXNjcmlwdGlvblxuICAgLSBDb21wb25lbnQgcmVsYXRpb25zaGlwc1xuICAgLSBEYXRhIGZsb3dcbiAgIC0gSW50ZWdyYXRpb24gcG9pbnRzXG5cbjQuIEFQSSBET0NVTUVOVEFUSU9OXG4gICAtIEF2YWlsYWJsZSBlbmRwb2ludHNcbiAgIC0gUmVxdWVzdC9yZXNwb25zZSBleGFtcGxlc1xuICAgLSBBdXRoZW50aWNhdGlvbiByZXF1aXJlbWVudHNcblxuNS4gREVQTE9ZTUVOVFxuICAgLSBEZXBsb3ltZW50IG9wdGlvbnNcbiAgIC0gRW52aXJvbm1lbnQgc2V0dXBcbiAgIC0gQ0kvQ0QgcGlwZWxpbmVcbiAgIC0gTW9uaXRvcmluZyBhbmQgbG9nZ2luZ1xuXG42LiBERVZFTE9QTUVOVFxuICAgLSBEZXZlbG9wbWVudCBzZXR1cFxuICAgLSBUZXN0aW5nIGluc3RydWN0aW9uc1xuICAgLSBDb250cmlidXRpbmcgZ3VpZGVsaW5lc1xuICAgLSBDb2RlIHN0cnVjdHVyZVxuXG43LiBUUk9VQkxFU0hPT1RJTkdcbiAgIC0gQ29tbW9uIGlzc3VlcyBhbmQgc29sdXRpb25zXG4gICAtIERlYnVnZ2luZyB0aXBzXG4gICAtIFBlcmZvcm1hbmNlIG9wdGltaXphdGlvblxuXG44LiBBRERJVElPTkFMIFJFU09VUkNFU1xuICAgLSBMaW5rcyB0byBkb2N1bWVudGF0aW9uXG4gICAtIFN1cHBvcnQgaW5mb3JtYXRpb25cbiAgIC0gTGljZW5zZSBpbmZvcm1hdGlvblxuXG5NYWtlIHRoZSBSRUFETUUgcHJvZmVzc2lvbmFsLCBjb21wcmVoZW5zaXZlLCBhbmQgZWFzeSB0byBmb2xsb3cuIFVzZSBwcm9wZXIgTWFya2Rvd24gZm9ybWF0dGluZyB3aXRoIGNsZWFyIHNlY3Rpb25zLCBjb2RlIGJsb2NrcywgYW5kIGV4YW1wbGVzLlxuYDtcblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW52b2tlTExNKHJlYWRtZVByb21wdCwge1xuICAgICAgdGVtcGVyYXR1cmU6IDAuMyxcbiAgICAgIG1heFRva2VuczogNjAwMCxcbiAgICAgIHJlcXVlc3RJZCxcbiAgICB9KTtcblxuICAgIHJldHVybiByZXNwb25zZTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ2dlci5lcnJvcignUkVBRE1FIGdlbmVyYXRpb24gZmFpbGVkJywgeyByZXF1ZXN0SWQsIGVycm9yIH0pO1xuICAgIHJldHVybiBnZW5lcmF0ZUZhbGxiYWNrUmVhZG1lKHNwZWNpZmljYXRpb24pO1xuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlRGVwbG95bWVudFNjcmlwdChcbiAgc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uLFxuICBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdLFxuICByZXF1ZXN0SWQ6IHN0cmluZ1xuKTogUHJvbWlzZTx7IGNvbnRlbnQ6IHN0cmluZzsgdHlwZTogJ2NkaycgfCAnY2xvdWRmb3JtYXRpb24nIHwgJ3RlcnJhZm9ybScgfT4ge1xuICBcbiAgY29uc3QgZGVwbG95bWVudFByb21wdCA9IGBcbkdlbmVyYXRlIGEgY29tcHJlaGVuc2l2ZSBkZXBsb3ltZW50IHNjcmlwdCBmb3IgdGhpcyBwcm9qZWN0IGJhc2VkIG9uIHRoZSBzcGVjaWZpY2F0aW9uIGFuZCBnZW5lcmF0ZWQgYXJ0aWZhY3RzLlxuXG5QUk9KRUNUIFNQRUNJRklDQVRJT046XG4ke0pTT04uc3RyaW5naWZ5KHNwZWNpZmljYXRpb24sIG51bGwsIDIpfVxuXG5BUlRJRkFDVFM6XG4ke0pTT04uc3RyaW5naWZ5KGFydGlmYWN0cy5tYXAoYSA9PiAoe1xuICB0YXNrSWQ6IGEudGFza0lkLFxuICBmaWxlczogYS5maWxlcy5tYXAoZiA9PiAoeyBwYXRoOiBmLnBhdGgsIHR5cGU6IGYudHlwZSwgbGFuZ3VhZ2U6IGYubGFuZ3VhZ2UgfSkpXG59KSksIG51bGwsIDIpfVxuXG5DcmVhdGUgYSBkZXBsb3ltZW50IHNjcmlwdCB0aGF0OlxuXG4xLiBTZXRzIHVwIGFsbCBuZWNlc3NhcnkgQVdTIHJlc291cmNlc1xuMi4gQ29uZmlndXJlcyBwcm9wZXIgSUFNIHJvbGVzIGFuZCBwb2xpY2llc1xuMy4gU2V0cyB1cCBuZXR3b3JraW5nIGFuZCBzZWN1cml0eSBncm91cHNcbjQuIENvbmZpZ3VyZXMgZGF0YWJhc2VzIGFuZCBzdG9yYWdlXG41LiBTZXRzIHVwIG1vbml0b3JpbmcgYW5kIGxvZ2dpbmdcbjYuIEluY2x1ZGVzIGVudmlyb25tZW50IHZhcmlhYmxlcyBhbmQgc2VjcmV0cyBtYW5hZ2VtZW50XG43LiBDb25maWd1cmVzIGF1dG8tc2NhbGluZyBhbmQgbG9hZCBiYWxhbmNpbmdcbjguIFNldHMgdXAgQ0kvQ0QgcGlwZWxpbmVcblxuQ2hvb3NlIHRoZSBtb3N0IGFwcHJvcHJpYXRlIGRlcGxveW1lbnQgdG9vbCAoQ0RLLCBDbG91ZEZvcm1hdGlvbiwgb3IgVGVycmFmb3JtKSBiYXNlZCBvbiB0aGUgcHJvamVjdCByZXF1aXJlbWVudHMuXG5cblJldHVybiB0aGUgZGVwbG95bWVudCBzY3JpcHQgYXMgYSBjb21wbGV0ZSwgcHJvZHVjdGlvbi1yZWFkeSBjb25maWd1cmF0aW9uLlxuYDtcblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW52b2tlTExNKGRlcGxveW1lbnRQcm9tcHQsIHtcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjIsXG4gICAgICBtYXhUb2tlbnM6IDgwMDAsXG4gICAgICByZXF1ZXN0SWQsXG4gICAgfSk7XG5cbiAgICAvLyBEZXRlcm1pbmUgZGVwbG95bWVudCB0eXBlIGJhc2VkIG9uIGNvbnRlbnRcbiAgICBsZXQgdHlwZTogJ2NkaycgfCAnY2xvdWRmb3JtYXRpb24nIHwgJ3RlcnJhZm9ybScgPSAnY2RrJztcbiAgICBpZiAocmVzcG9uc2UuaW5jbHVkZXMoJ3RlcnJhZm9ybScpKSB0eXBlID0gJ3RlcnJhZm9ybSc7XG4gICAgZWxzZSBpZiAocmVzcG9uc2UuaW5jbHVkZXMoJ0FXU1RlbXBsYXRlRm9ybWF0VmVyc2lvbicpKSB0eXBlID0gJ2Nsb3VkZm9ybWF0aW9uJztcblxuICAgIHJldHVybiB7IGNvbnRlbnQ6IHJlc3BvbnNlLCB0eXBlIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dnZXIuZXJyb3IoJ0RlcGxveW1lbnQgc2NyaXB0IGdlbmVyYXRpb24gZmFpbGVkJywgeyByZXF1ZXN0SWQsIGVycm9yIH0pO1xuICAgIHJldHVybiB7XG4gICAgICBjb250ZW50OiBnZW5lcmF0ZUZhbGxiYWNrRGVwbG95bWVudFNjcmlwdChzcGVjaWZpY2F0aW9uKSxcbiAgICAgIHR5cGU6ICdjZGsnXG4gICAgfTtcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBnZW5lcmF0ZUFQSURvY3VtZW50YXRpb24oXG4gIHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbixcbiAgYXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXSxcbiAgcmVxdWVzdElkOiBzdHJpbmdcbik6IFByb21pc2U8c3RyaW5nPiB7XG4gIFxuICBjb25zdCBhcGlEb2NQcm9tcHQgPSBgXG5HZW5lcmF0ZSBjb21wcmVoZW5zaXZlIEFQSSBkb2N1bWVudGF0aW9uIGluIE9wZW5BUEkgMy4wIGZvcm1hdCBmb3IgdGhpcyBwcm9qZWN0LlxuXG5QUk9KRUNUIFNQRUNJRklDQVRJT046XG4ke0pTT04uc3RyaW5naWZ5KHNwZWNpZmljYXRpb24sIG51bGwsIDIpfVxuXG5HRU5FUkFURUQgQ09ERSBBUlRJRkFDVFM6XG4ke0pTT04uc3RyaW5naWZ5KGFydGlmYWN0cy5tYXAoYSA9PiAoe1xuICB0YXNrSWQ6IGEudGFza0lkLFxuICBmaWxlczogYS5maWxlcy5maWx0ZXIoZiA9PiBmLnR5cGUgPT09ICdzb3VyY2UnKS5tYXAoZiA9PiAoeyBwYXRoOiBmLnBhdGgsIGxhbmd1YWdlOiBmLmxhbmd1YWdlIH0pKVxufSkpLCBudWxsLCAyKX1cblxuQ3JlYXRlIGEgY29tcGxldGUgT3BlbkFQSSBzcGVjaWZpY2F0aW9uIHRoYXQgaW5jbHVkZXM6XG5cbjEuIEFQSSBpbmZvcm1hdGlvbiBhbmQgbWV0YWRhdGFcbjIuIFNlcnZlciBjb25maWd1cmF0aW9uc1xuMy4gQWxsIGVuZHBvaW50cyB3aXRoIGRldGFpbGVkIGRlc2NyaXB0aW9uc1xuNC4gUmVxdWVzdC9yZXNwb25zZSBzY2hlbWFzXG41LiBBdXRoZW50aWNhdGlvbiBhbmQgc2VjdXJpdHkgc2NoZW1lc1xuNi4gRXJyb3IgcmVzcG9uc2UgZm9ybWF0c1xuNy4gRXhhbXBsZXMgZm9yIGFsbCBlbmRwb2ludHNcbjguIERhdGEgbW9kZWxzIGFuZCBjb21wb25lbnRzXG5cbk1ha2Ugc3VyZSB0aGUgZG9jdW1lbnRhdGlvbiBpcyBjb21wcmVoZW5zaXZlIGFuZCBmb2xsb3dzIE9wZW5BUEkgMy4wIHN0YW5kYXJkcy5cbmA7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGludm9rZUxMTShhcGlEb2NQcm9tcHQsIHtcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjIsXG4gICAgICBtYXhUb2tlbnM6IDYwMDAsXG4gICAgICByZXF1ZXN0SWQsXG4gICAgfSk7XG5cbiAgICByZXR1cm4gcmVzcG9uc2U7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dnZXIuZXJyb3IoJ0FQSSBkb2N1bWVudGF0aW9uIGdlbmVyYXRpb24gZmFpbGVkJywgeyByZXF1ZXN0SWQsIGVycm9yIH0pO1xuICAgIHJldHVybiBnZW5lcmF0ZUZhbGxiYWNrQVBJRG9jKHNwZWNpZmljYXRpb24pO1xuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGdlbmVyYXRlQXJjaGl0ZWN0dXJlRGlhZ3JhbShcbiAgc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uLFxuICBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdLFxuICByZXF1ZXN0SWQ6IHN0cmluZ1xuKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgXG4gIHRyeSB7XG4gICAgcmV0dXJuIGF3YWl0IGdlbmVyYXRlTWVybWFpZERpYWdyYW0oc3BlY2lmaWNhdGlvbiwgYXJ0aWZhY3RzKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dnZXIuZXJyb3IoJ0FyY2hpdGVjdHVyZSBkaWFncmFtIGdlbmVyYXRpb24gZmFpbGVkJywgeyByZXF1ZXN0SWQsIGVycm9yIH0pO1xuICAgIHJldHVybiBnZW5lcmF0ZUZhbGxiYWNrRGlhZ3JhbShzcGVjaWZpY2F0aW9uKTtcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBjcmVhdGVQcm9qZWN0QnVuZGxlKFxuICBzZXNzaW9uSWQ6IHN0cmluZyxcbiAgc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uLFxuICBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdLFxuICByZWFkbWU6IHN0cmluZyxcbiAgZGVwbG95bWVudFNjcmlwdDogeyBjb250ZW50OiBzdHJpbmc7IHR5cGU6IHN0cmluZyB9LFxuICBhcGlEb2NzOiBzdHJpbmcsXG4gIGFyY2hpdGVjdHVyZURpYWdyYW06IHN0cmluZ1xuKTogUHJvbWlzZTx7IGJ1ZmZlcjogQnVmZmVyOyBzaXplOiBudW1iZXIgfT4ge1xuICBcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBjb25zdCBhcmNoaXZlID0gYXJjaGl2ZXIoJ3ppcCcsIHsgemxpYjogeyBsZXZlbDogOSB9IH0pO1xuICAgIGNvbnN0IGNodW5rczogQnVmZmVyW10gPSBbXTtcblxuICAgIGFyY2hpdmUub24oJ2RhdGEnLCAoY2h1bmspID0+IGNodW5rcy5wdXNoKGNodW5rKSk7XG4gICAgYXJjaGl2ZS5vbignZW5kJywgKCkgPT4ge1xuICAgICAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmNvbmNhdChjaHVua3MpO1xuICAgICAgcmVzb2x2ZSh7IGJ1ZmZlciwgc2l6ZTogYnVmZmVyLmxlbmd0aCB9KTtcbiAgICB9KTtcbiAgICBhcmNoaXZlLm9uKCdlcnJvcicsIHJlamVjdCk7XG5cbiAgICAvLyBBZGQgUkVBRE1FXG4gICAgYXJjaGl2ZS5hcHBlbmQocmVhZG1lLCB7IG5hbWU6ICdSRUFETUUubWQnIH0pO1xuXG4gICAgLy8gQWRkIGRlcGxveW1lbnQgc2NyaXB0XG4gICAgY29uc3QgZGVwbG95bWVudEZpbGVOYW1lID0gYGRlcGxveW1lbnQuJHtkZXBsb3ltZW50U2NyaXB0LnR5cGUgPT09ICd0ZXJyYWZvcm0nID8gJ3RmJyA6ICd0cyd9YDtcbiAgICBhcmNoaXZlLmFwcGVuZChkZXBsb3ltZW50U2NyaXB0LmNvbnRlbnQsIHsgbmFtZTogZGVwbG95bWVudEZpbGVOYW1lIH0pO1xuXG4gICAgLy8gQWRkIEFQSSBkb2N1bWVudGF0aW9uXG4gICAgYXJjaGl2ZS5hcHBlbmQoYXBpRG9jcywgeyBuYW1lOiAnYXBpLWRvY3MueWFtbCcgfSk7XG5cbiAgICAvLyBBZGQgYXJjaGl0ZWN0dXJlIGRpYWdyYW1cbiAgICBhcmNoaXZlLmFwcGVuZChhcmNoaXRlY3R1cmVEaWFncmFtLCB7IG5hbWU6ICdhcmNoaXRlY3R1cmUubW1kJyB9KTtcblxuICAgIC8vIEFkZCBhbGwgZ2VuZXJhdGVkIGZpbGVzXG4gICAgZm9yIChjb25zdCBhcnRpZmFjdCBvZiBhcnRpZmFjdHMpIHtcbiAgICAgIGZvciAoY29uc3QgZmlsZSBvZiBhcnRpZmFjdC5maWxlcykge1xuICAgICAgICBhcmNoaXZlLmFwcGVuZChmaWxlLmNvbnRlbnQsIHsgbmFtZTogZmlsZS5wYXRoIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEFkZCBwYWNrYWdlLmpzb24gaWYgbm90IGFscmVhZHkgcHJlc2VudFxuICAgIGNvbnN0IGhhc1BhY2thZ2VKc29uID0gYXJ0aWZhY3RzLnNvbWUoYSA9PiBcbiAgICAgIGEuZmlsZXMuc29tZShmID0+IGYucGF0aCA9PT0gJ3BhY2thZ2UuanNvbicpXG4gICAgKTtcbiAgICBcbiAgICBpZiAoIWhhc1BhY2thZ2VKc29uICYmIHNwZWNpZmljYXRpb24udGVjaFN0YWNrLmZyb250ZW5kLmZyYW1ld29yay5pbmNsdWRlcygnUmVhY3QnKSkge1xuICAgICAgY29uc3QgcGFja2FnZUpzb24gPSBnZW5lcmF0ZVBhY2thZ2VKc29uKHNwZWNpZmljYXRpb24pO1xuICAgICAgYXJjaGl2ZS5hcHBlbmQocGFja2FnZUpzb24sIHsgbmFtZTogJ3BhY2thZ2UuanNvbicgfSk7XG4gICAgfVxuXG4gICAgYXJjaGl2ZS5maW5hbGl6ZSgpO1xuICB9KTtcbn1cblxuYXN5bmMgZnVuY3Rpb24gc3RvcmVGaW5hbEFydGlmYWN0cyhcbiAgc2Vzc2lvbklkOiBzdHJpbmcsXG4gIHByb2plY3RCdW5kbGU6IHsgYnVmZmVyOiBCdWZmZXI7IHNpemU6IG51bWJlciB9LFxuICByZWFkbWU6IHN0cmluZyxcbiAgZGVwbG95bWVudFNjcmlwdDogeyBjb250ZW50OiBzdHJpbmc7IHR5cGU6IHN0cmluZyB9LFxuICBhcGlEb2NzOiBzdHJpbmcsXG4gIGFyY2hpdGVjdHVyZURpYWdyYW06IHN0cmluZ1xuKTogUHJvbWlzZTxGaW5hbE91dHB1dD4ge1xuICBcbiAgY29uc3QgdGltZXN0YW1wID0gRGF0ZS5ub3coKTtcbiAgXG4gIC8vIFN0b3JlIHByb2plY3QgYnVuZGxlXG4gIGNvbnN0IGJ1bmRsZUtleSA9IGBzZXNzaW9ucy8ke3Nlc3Npb25JZH0vZmluYWwvcHJvamVjdC1idW5kbGUtJHt0aW1lc3RhbXB9LnppcGA7XG4gIGF3YWl0IHMzQ2xpZW50LnNlbmQobmV3IFB1dE9iamVjdENvbW1hbmQoe1xuICAgIEJ1Y2tldDogQVJUSUZBQ1RTX0JVQ0tFVCxcbiAgICBLZXk6IGJ1bmRsZUtleSxcbiAgICBCb2R5OiBwcm9qZWN0QnVuZGxlLmJ1ZmZlcixcbiAgICBDb250ZW50VHlwZTogJ2FwcGxpY2F0aW9uL3ppcCcsXG4gICAgTWV0YWRhdGE6IHtcbiAgICAgIHNlc3Npb25JZCxcbiAgICAgIHR5cGU6ICdwcm9qZWN0LWJ1bmRsZScsXG4gICAgICBzaXplOiBwcm9qZWN0QnVuZGxlLnNpemUudG9TdHJpbmcoKSxcbiAgICB9LFxuICB9KSk7XG5cbiAgLy8gU3RvcmUgUkVBRE1FXG4gIGNvbnN0IHJlYWRtZUtleSA9IGBzZXNzaW9ucy8ke3Nlc3Npb25JZH0vZmluYWwvUkVBRE1FLm1kYDtcbiAgYXdhaXQgczNDbGllbnQuc2VuZChuZXcgUHV0T2JqZWN0Q29tbWFuZCh7XG4gICAgQnVja2V0OiBBUlRJRkFDVFNfQlVDS0VULFxuICAgIEtleTogcmVhZG1lS2V5LFxuICAgIEJvZHk6IHJlYWRtZSxcbiAgICBDb250ZW50VHlwZTogJ3RleHQvbWFya2Rvd24nLFxuICB9KSk7XG5cbiAgLy8gU3RvcmUgZGVwbG95bWVudCBzY3JpcHRcbiAgY29uc3QgZGVwbG95bWVudEtleSA9IGBzZXNzaW9ucy8ke3Nlc3Npb25JZH0vZmluYWwvZGVwbG95bWVudC4ke2RlcGxveW1lbnRTY3JpcHQudHlwZX1gO1xuICBhd2FpdCBzM0NsaWVudC5zZW5kKG5ldyBQdXRPYmplY3RDb21tYW5kKHtcbiAgICBCdWNrZXQ6IEFSVElGQUNUU19CVUNLRVQsXG4gICAgS2V5OiBkZXBsb3ltZW50S2V5LFxuICAgIEJvZHk6IGRlcGxveW1lbnRTY3JpcHQuY29udGVudCxcbiAgICBDb250ZW50VHlwZTogJ3RleHQvcGxhaW4nLFxuICB9KSk7XG5cbiAgLy8gR2VuZXJhdGUgZG93bmxvYWQgVVJMXG4gIGNvbnN0IGRvd25sb2FkVXJsID0gYXdhaXQgZ2V0U2lnbmVkVXJsKFxuICAgIHMzQ2xpZW50LFxuICAgIG5ldyBHZXRPYmplY3RDb21tYW5kKHtcbiAgICAgIEJ1Y2tldDogQVJUSUZBQ1RTX0JVQ0tFVCxcbiAgICAgIEtleTogYnVuZGxlS2V5LFxuICAgIH0pLFxuICAgIHsgZXhwaXJlc0luOiAzNjAwICogMjQgKiA3IH0gLy8gNyBkYXlzXG4gICk7XG5cbiAgcmV0dXJuIHtcbiAgICBwcm9qZWN0QnVuZGxlOiB7XG4gICAgICBzM0tleTogYnVuZGxlS2V5LFxuICAgICAgZG93bmxvYWRVcmwsXG4gICAgfSxcbiAgICByZWFkbWU6IHtcbiAgICAgIHMzS2V5OiByZWFkbWVLZXksXG4gICAgICBjb250ZW50OiByZWFkbWUsXG4gICAgfSxcbiAgICBkZXBsb3ltZW50U2NyaXB0OiB7XG4gICAgICBzM0tleTogZGVwbG95bWVudEtleSxcbiAgICAgIHR5cGU6IGRlcGxveW1lbnRTY3JpcHQudHlwZSBhcyAnY2RrJyB8ICdjbG91ZGZvcm1hdGlvbicgfCAndGVycmFmb3JtJyxcbiAgICB9LFxuICAgIGRvY3VtZW50YXRpb246IHtcbiAgICAgIGFwaURvY3MsXG4gICAgICBhcmNoaXRlY3R1cmVEaWFncmFtLFxuICAgICAgdXNlckd1aWRlOiByZWFkbWUsXG4gICAgfSxcbiAgfTtcbn1cblxuYXN5bmMgZnVuY3Rpb24gdXBkYXRlV29ya2Zsb3dTdGF0ZShcbiAgc2Vzc2lvbklkOiBzdHJpbmcsIFxuICBzdGF0dXM6ICdjb21wbGV0ZWQnIHwgJ2ZhaWxlZCcsXG4gIGZpbmFsT3V0cHV0PzogRmluYWxPdXRwdXRcbik6IFByb21pc2U8dm9pZD4ge1xuICBcbiAgYXdhaXQgZHluYW1vQ2xpZW50LnNlbmQobmV3IFVwZGF0ZUNvbW1hbmQoe1xuICAgIFRhYmxlTmFtZTogVEFCTEVfTkFNRSxcbiAgICBLZXk6IHtcbiAgICAgIFBLOiBgU0VTU0lPTiMke3Nlc3Npb25JZH1gLFxuICAgICAgU0s6IGBXT1JLRkxPVyMke0RhdGUubm93KCl9YCxcbiAgICB9LFxuICAgIFVwZGF0ZUV4cHJlc3Npb246ICdTRVQgI3N0YXR1cyA9IDpzdGF0dXMsIHVwZGF0ZWRBdCA9IDp1cGRhdGVkQXQsIGZpbmFsT3V0cHV0ID0gOmZpbmFsT3V0cHV0JyxcbiAgICBFeHByZXNzaW9uQXR0cmlidXRlTmFtZXM6IHtcbiAgICAgICcjc3RhdHVzJzogJ3N0YXR1cycsXG4gICAgfSxcbiAgICBFeHByZXNzaW9uQXR0cmlidXRlVmFsdWVzOiB7XG4gICAgICAnOnN0YXR1cyc6IHN0YXR1cyxcbiAgICAgICc6dXBkYXRlZEF0JzogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgJzpmaW5hbE91dHB1dCc6IGZpbmFsT3V0cHV0ID8gSlNPTi5zdHJpbmdpZnkoZmluYWxPdXRwdXQpIDogbnVsbCxcbiAgICB9LFxuICB9KSk7XG59XG5cbi8vIEZhbGxiYWNrIGZ1bmN0aW9uc1xuZnVuY3Rpb24gZ2VuZXJhdGVGYWxsYmFja1JlYWRtZShzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24pOiBzdHJpbmcge1xuICByZXR1cm4gYCMgJHtzcGVjaWZpY2F0aW9uLnByb2plY3ROYW1lfVxuXG4ke3NwZWNpZmljYXRpb24uZGVzY3JpcHRpb259XG5cbiMjIEZlYXR1cmVzXG5cbiR7c3BlY2lmaWNhdGlvbi5mZWF0dXJlcy5tYXAoZiA9PiBgLSAke2YubmFtZX06ICR7Zi5kZXNjcmlwdGlvbn1gKS5qb2luKCdcXG4nKX1cblxuIyMgVGVjaCBTdGFja1xuXG4tIEZyb250ZW5kOiAke3NwZWNpZmljYXRpb24udGVjaFN0YWNrLmZyb250ZW5kLmZyYW1ld29ya31cbi0gQmFja2VuZDogJHtzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5iYWNrZW5kLmZyYW1ld29ya31cbi0gRGF0YWJhc2U6ICR7c3BlY2lmaWNhdGlvbi50ZWNoU3RhY2suYmFja2VuZC5kYXRhYmFzZX1cblxuIyMgR2V0dGluZyBTdGFydGVkXG5cbjEuIENsb25lIHRoZSByZXBvc2l0b3J5XG4yLiBJbnN0YWxsIGRlcGVuZGVuY2llczogXFxgbnBtIGluc3RhbGxcXGBcbjMuIFN0YXJ0IHRoZSBkZXZlbG9wbWVudCBzZXJ2ZXI6IFxcYG5wbSBydW4gZGV2XFxgXG5cbiMjIERlcGxveW1lbnRcblxuRm9sbG93IHRoZSBkZXBsb3ltZW50IHNjcmlwdCBpbnN0cnVjdGlvbnMgaW4gdGhlIGRlcGxveW1lbnQgZmlsZXMuXG5gO1xufVxuXG5mdW5jdGlvbiBnZW5lcmF0ZUZhbGxiYWNrRGVwbG95bWVudFNjcmlwdChzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24pOiBzdHJpbmcge1xuICByZXR1cm4gYC8vIENESyBEZXBsb3ltZW50IFNjcmlwdCBmb3IgJHtzcGVjaWZpY2F0aW9uLnByb2plY3ROYW1lfVxuaW1wb3J0ICogYXMgY2RrIGZyb20gJ2F3cy1jZGstbGliJztcbmltcG9ydCB7IENvbnN0cnVjdCB9IGZyb20gJ2NvbnN0cnVjdHMnO1xuXG5leHBvcnQgY2xhc3MgJHtzcGVjaWZpY2F0aW9uLnByb2plY3ROYW1lLnJlcGxhY2UoL1teYS16QS1aMC05XS9nLCAnJyl9U3RhY2sgZXh0ZW5kcyBjZGsuU3RhY2sge1xuICBjb25zdHJ1Y3RvcihzY29wZTogQ29uc3RydWN0LCBpZDogc3RyaW5nLCBwcm9wcz86IGNkay5TdGFja1Byb3BzKSB7XG4gICAgc3VwZXIoc2NvcGUsIGlkLCBwcm9wcyk7XG4gICAgXG4gICAgLy8gQWRkIHlvdXIgQVdTIHJlc291cmNlcyBoZXJlXG4gIH1cbn1gO1xufVxuXG5mdW5jdGlvbiBnZW5lcmF0ZUZhbGxiYWNrQVBJRG9jKHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbik6IHN0cmluZyB7XG4gIHJldHVybiBgb3BlbmFwaTogMy4wLjBcbmluZm86XG4gIHRpdGxlOiAke3NwZWNpZmljYXRpb24ucHJvamVjdE5hbWV9IEFQSVxuICBkZXNjcmlwdGlvbjogJHtzcGVjaWZpY2F0aW9uLmRlc2NyaXB0aW9ufVxuICB2ZXJzaW9uOiAxLjAuMFxucGF0aHM6IHt9YDtcbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVGYWxsYmFja0RpYWdyYW0oc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uKTogc3RyaW5nIHtcbiAgcmV0dXJuIGBncmFwaCBURFxuICAgIEFbRnJvbnRlbmRdIC0tPiBCW0JhY2tlbmRdXG4gICAgQiAtLT4gQ1tEYXRhYmFzZV1gO1xufVxuXG5mdW5jdGlvbiBnZW5lcmF0ZVBhY2thZ2VKc29uKHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbik6IHN0cmluZyB7XG4gIHJldHVybiBKU09OLnN0cmluZ2lmeSh7XG4gICAgbmFtZTogc3BlY2lmaWNhdGlvbi5wcm9qZWN0TmFtZSxcbiAgICB2ZXJzaW9uOiBcIjEuMC4wXCIsXG4gICAgZGVzY3JpcHRpb246IHNwZWNpZmljYXRpb24uZGVzY3JpcHRpb24sXG4gICAgc2NyaXB0czoge1xuICAgICAgZGV2OiBcIm5wbSBydW4gZGV2XCIsXG4gICAgICBidWlsZDogXCJucG0gcnVuIGJ1aWxkXCIsXG4gICAgICBzdGFydDogXCJucG0gc3RhcnRcIlxuICAgIH0sXG4gICAgZGVwZW5kZW5jaWVzOiB7fSxcbiAgICBkZXZEZXBlbmRlbmNpZXM6IHt9XG4gIH0sIG51bGwsIDIpO1xufVxuIl19