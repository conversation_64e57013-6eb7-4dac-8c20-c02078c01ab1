"use strict";
/**
 * Finalizer Agent
 * Generates README, deployment scripts, and final artifacts
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const s3_request_presigner_1 = require("@aws-sdk/s3-request-presigner");
const archiver_1 = __importDefault(require("archiver"));
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const diagram_generator_1 = require("../../utils/diagram-generator");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Finalizer Agent started', { requestId, event });
    try {
        const { sessionId, specification, artifacts, reviews } = event;
        // Generate comprehensive README
        const readme = await generateComprehensiveReadme(specification, artifacts, reviews, requestId);
        // Generate deployment scripts
        const deploymentScript = await generateDeploymentScript(specification, artifacts, requestId);
        // Generate API documentation
        const apiDocs = await generateAPIDocumentation(specification, artifacts, requestId);
        // Generate architecture diagram
        const architectureDiagram = await generateArchitectureDiagram(specification, artifacts, requestId);
        // Create project bundle
        const projectBundle = await createProjectBundle(sessionId, specification, artifacts, readme, deploymentScript, apiDocs, architectureDiagram);
        // Store final artifacts
        const finalOutput = await storeFinalArtifacts(sessionId, projectBundle, readme, deploymentScript, apiDocs, architectureDiagram);
        // Update workflow state to completed
        await updateWorkflowState(sessionId, 'completed', finalOutput);
        logger_1.logger.info('Finalization completed', {
            requestId,
            sessionId,
            bundleSize: projectBundle.size,
            filesCount: artifacts.reduce((sum, a) => sum + a.files.length, 0)
        });
        return finalOutput;
    }
    catch (error) {
        logger_1.logger.error('Finalizer Agent failed', { requestId, error });
        // Update workflow state to failed
        await updateWorkflowState(event.sessionId, 'failed');
        throw error;
    }
};
exports.handler = handler;
async function generateComprehensiveReadme(specification, artifacts, reviews, requestId) {
    const readmePrompt = `
You are a technical documentation expert. Generate a comprehensive, professional README.md file for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
        errorsCount: a.errors.length
    })), null, 2)}

REVIEW SUMMARY:
${JSON.stringify(reviews.map(r => ({
        taskId: r.taskId,
        quality: r.overallQuality,
        errorsCount: r.errors.length,
        suggestionsCount: r.suggestions.length
    })), null, 2)}

Create a comprehensive README.md that includes:

1. PROJECT OVERVIEW
   - Clear project description
   - Key features and capabilities
   - Technology stack
   - Architecture overview

2. GETTING STARTED
   - Prerequisites
   - Installation instructions
   - Quick start guide
   - Configuration setup

3. ARCHITECTURE
   - System architecture description
   - Component relationships
   - Data flow
   - Integration points

4. API DOCUMENTATION
   - Available endpoints
   - Request/response examples
   - Authentication requirements

5. DEPLOYMENT
   - Deployment options
   - Environment setup
   - CI/CD pipeline
   - Monitoring and logging

6. DEVELOPMENT
   - Development setup
   - Testing instructions
   - Contributing guidelines
   - Code structure

7. TROUBLESHOOTING
   - Common issues and solutions
   - Debugging tips
   - Performance optimization

8. ADDITIONAL RESOURCES
   - Links to documentation
   - Support information
   - License information

Make the README professional, comprehensive, and easy to follow. Use proper Markdown formatting with clear sections, code blocks, and examples.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(readmePrompt, {
            temperature: 0.3,
            maxTokens: 6000,
            requestId,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('README generation failed', { requestId, error });
        return generateFallbackReadme(specification);
    }
}
async function generateDeploymentScript(specification, artifacts, requestId) {
    const deploymentPrompt = `
Generate a comprehensive deployment script for this project based on the specification and generated artifacts.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language }))
    })), null, 2)}

Create a deployment script that:

1. Sets up all necessary AWS resources
2. Configures proper IAM roles and policies
3. Sets up networking and security groups
4. Configures databases and storage
5. Sets up monitoring and logging
6. Includes environment variables and secrets management
7. Configures auto-scaling and load balancing
8. Sets up CI/CD pipeline

Choose the most appropriate deployment tool (CDK, CloudFormation, or Terraform) based on the project requirements.

Return the deployment script as a complete, production-ready configuration.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(deploymentPrompt, {
            temperature: 0.2,
            maxTokens: 8000,
            requestId,
        });
        // Determine deployment type based on content
        let type = 'cdk';
        if (response.includes('terraform'))
            type = 'terraform';
        else if (response.includes('AWSTemplateFormatVersion'))
            type = 'cloudformation';
        return { content: response, type };
    }
    catch (error) {
        logger_1.logger.error('Deployment script generation failed', { requestId, error });
        return {
            content: generateFallbackDeploymentScript(specification),
            type: 'cdk'
        };
    }
}
async function generateAPIDocumentation(specification, artifacts, requestId) {
    const apiDocPrompt = `
Generate comprehensive API documentation in OpenAPI 3.0 format for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED CODE ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.filter(f => f.type === 'source').map(f => ({ path: f.path, language: f.language }))
    })), null, 2)}

Create a complete OpenAPI specification that includes:

1. API information and metadata
2. Server configurations
3. All endpoints with detailed descriptions
4. Request/response schemas
5. Authentication and security schemes
6. Error response formats
7. Examples for all endpoints
8. Data models and components

Make sure the documentation is comprehensive and follows OpenAPI 3.0 standards.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(apiDocPrompt, {
            temperature: 0.2,
            maxTokens: 6000,
            requestId,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('API documentation generation failed', { requestId, error });
        return generateFallbackAPIDoc(specification);
    }
}
async function generateArchitectureDiagram(specification, artifacts, requestId) {
    try {
        return await (0, diagram_generator_1.generateMermaidDiagram)(specification, artifacts);
    }
    catch (error) {
        logger_1.logger.error('Architecture diagram generation failed', { requestId, error });
        return generateFallbackDiagram(specification);
    }
}
async function createProjectBundle(sessionId, specification, artifacts, readme, deploymentScript, apiDocs, architectureDiagram) {
    return new Promise((resolve, reject) => {
        const archive = (0, archiver_1.default)('zip', { zlib: { level: 9 } });
        const chunks = [];
        archive.on('data', (chunk) => chunks.push(chunk));
        archive.on('end', () => {
            const buffer = Buffer.concat(chunks);
            resolve({ buffer, size: buffer.length });
        });
        archive.on('error', reject);
        // Add README
        archive.append(readme, { name: 'README.md' });
        // Add deployment script
        const deploymentFileName = `deployment.${deploymentScript.type === 'terraform' ? 'tf' : 'ts'}`;
        archive.append(deploymentScript.content, { name: deploymentFileName });
        // Add API documentation
        archive.append(apiDocs, { name: 'api-docs.yaml' });
        // Add architecture diagram
        archive.append(architectureDiagram, { name: 'architecture.mmd' });
        // Add all generated files
        for (const artifact of artifacts) {
            for (const file of artifact.files) {
                archive.append(file.content, { name: file.path });
            }
        }
        // Add package.json if not already present
        const hasPackageJson = artifacts.some(a => a.files.some(f => f.path === 'package.json'));
        if (!hasPackageJson && specification.techStack.frontend.framework.includes('React')) {
            const packageJson = generatePackageJson(specification);
            archive.append(packageJson, { name: 'package.json' });
        }
        archive.finalize();
    });
}
async function storeFinalArtifacts(sessionId, projectBundle, readme, deploymentScript, apiDocs, architectureDiagram) {
    const timestamp = Date.now();
    // Store project bundle
    const bundleKey = `sessions/${sessionId}/final/project-bundle-${timestamp}.zip`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: bundleKey,
        Body: projectBundle.buffer,
        ContentType: 'application/zip',
        Metadata: {
            sessionId,
            type: 'project-bundle',
            size: projectBundle.size.toString(),
        },
    }));
    // Store README
    const readmeKey = `sessions/${sessionId}/final/README.md`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: readmeKey,
        Body: readme,
        ContentType: 'text/markdown',
    }));
    // Store deployment script
    const deploymentKey = `sessions/${sessionId}/final/deployment.${deploymentScript.type}`;
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: deploymentKey,
        Body: deploymentScript.content,
        ContentType: 'text/plain',
    }));
    // Generate download URL
    const downloadUrl = await (0, s3_request_presigner_1.getSignedUrl)(s3Client, new client_s3_1.GetObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: bundleKey,
    }), { expiresIn: 3600 * 24 * 7 } // 7 days
    );
    return {
        projectBundle: {
            s3Key: bundleKey,
            downloadUrl,
        },
        readme: {
            s3Key: readmeKey,
            content: readme,
        },
        deploymentScript: {
            s3Key: deploymentKey,
            type: deploymentScript.type,
        },
        documentation: {
            apiDocs,
            architectureDiagram,
            userGuide: readme,
        },
    };
}
async function updateWorkflowState(sessionId, status, finalOutput) {
    await dynamoClient.send(new lib_dynamodb_1.UpdateCommand({
        TableName: TABLE_NAME,
        Key: {
            PK: `SESSION#${sessionId}`,
            SK: `WORKFLOW#${Date.now()}`,
        },
        UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, finalOutput = :finalOutput',
        ExpressionAttributeNames: {
            '#status': 'status',
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':updatedAt': new Date().toISOString(),
            ':finalOutput': finalOutput ? JSON.stringify(finalOutput) : null,
        },
    }));
}
// Fallback functions
function generateFallbackReadme(specification) {
    return `# ${specification.projectName}

${specification.description}

## Features

${specification.features.map(f => `- ${f.name}: ${f.description}`).join('\n')}

## Tech Stack

- Frontend: ${specification.techStack.frontend.framework}
- Backend: ${specification.techStack.backend.framework}
- Database: ${specification.techStack.backend.database}

## Getting Started

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start the development server: \`npm run dev\`

## Deployment

Follow the deployment script instructions in the deployment files.
`;
}
function generateFallbackDeploymentScript(specification) {
    return `// CDK Deployment Script for ${specification.projectName}
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';

export class ${specification.projectName.replace(/[^a-zA-Z0-9]/g, '')}Stack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    
    // Add your AWS resources here
  }
}`;
}
function generateFallbackAPIDoc(specification) {
    return `openapi: 3.0.0
info:
  title: ${specification.projectName} API
  description: ${specification.description}
  version: 1.0.0
paths: {}`;
}
function generateFallbackDiagram(specification) {
    return `graph TD
    A[Frontend] --> B[Backend]
    B --> C[Database]`;
}
function generatePackageJson(specification) {
    return JSON.stringify({
        name: specification.projectName,
        version: "1.0.0",
        description: specification.description,
        scripts: {
            dev: "npm run dev",
            build: "npm run build",
            start: "npm start"
        },
        dependencies: {},
        devDependencies: {}
    }, null, 2);
}
//# sourceMappingURL=data:application/json;base64,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