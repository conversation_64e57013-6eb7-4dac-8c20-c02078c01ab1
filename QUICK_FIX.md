# Quick Fix for Current Issues

## 🚨 Current Problem
You're getting TypeScript compilation errors because some dependencies are missing.

## 🔧 Quick Solution

### Step 1: Install Missing Dependencies
```powershell
# Run this in your PowerShell terminal
npm install
```

### Step 2: Install TypeScript and ts-node globally (if needed)
```powershell
npm install -g typescript ts-node
```

### Step 3: Install AWS CDK globally (if not already installed)
```powershell
npm install -g aws-cdk
```

### Step 4: Build the project
```powershell
npm run build
```

### Step 5: Set up your environment variables
```powershell
# Copy the example file
copy .env.example .env

# Edit .env file with your API keys
notepad .env
```

Add these to your `.env` file:
```bash
# Required: At least one AI provider
GEMINI_API_KEY=your-gemini-api-key-here
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Required: AWS Configuration  
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1

# Optional
NOTIFICATION_EMAIL=<EMAIL>
ENVIRONMENT=development
```

### Step 6: Configure AWS CLI (if not done)
```powershell
aws configure
```
Enter your:
- AWS Access Key ID
- AWS Secret Access Key  
- Default region: us-east-1
- Default output format: json

### Step 7: Bootstrap CDK
```powershell
cdk bootstrap aws://************/us-east-1
```

### Step 8: Deploy
```powershell
npm run deploy
```

## 🔑 Getting API Keys

### OpenRouter API Key (Recommended for DeepSeek)
1. Go to https://openrouter.ai/
2. Sign up for an account
3. Go to "Keys" section
4. Create a new API key
5. Copy the key (starts with "sk-or-...")

### Google Gemini API Key (Optional but recommended)
1. Go to https://makersuite.google.com/app/apikey
2. Sign in with Google account
3. Click "Create API Key"
4. Copy the key (starts with "AI...")

## 🛠️ Alternative: Use Setup Script

I've created a PowerShell setup script that automates most of this:

```powershell
# Run the setup script
.\setup.ps1
```

This script will:
- Check Node.js and npm installation
- Install all dependencies
- Install AWS CDK if needed
- Build the project
- Create .env file from template
- Guide you through next steps

## 🔍 Troubleshooting

### If you get "execution policy" error:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### If npm install fails:
```powershell
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rmdir /s node_modules
npm install
```

### If CDK bootstrap fails:
```powershell
# Check AWS credentials
aws sts get-caller-identity

# If that fails, reconfigure AWS
aws configure
```

## 📞 Need Help?

If you're still having issues:
1. Check that Node.js version is 18+ (`node --version`)
2. Make sure you have internet connection for npm installs
3. Verify AWS credentials are correct
4. Check that your API keys are valid

The most common issue is missing dependencies, which `npm install` should fix.
