# AI Agent Workflow System

A serverless multi-agent workflow system that transforms user descriptions into production-ready websites, backends, and chatbots using AWS Lambda, Step Functions, and AI agents.

## 🚀 Features

- **Multi-Agent Architecture**: Specialized AI agents for different tasks (enhancement, code generation, review, finalization)
- **Serverless Infrastructure**: Built on AWS Lambda, Step Functions, DynamoDB, and S3
- **Iterative Refinement**: Automatic code review and improvement cycles
- **Production-Ready Output**: Generates complete projects with deployment scripts, documentation, and CI/CD configurations
- **Comprehensive Monitoring**: CloudWatch dashboards, alarms, and X-Ray tracing
- **Security-First**: Built-in security scanning, encryption, and best practices

## 🏗️ Architecture

```mermaid
graph TB
    User[👤 User] --> API[🚪 API Gateway]
    API --> SFN[⚙️ Step Functions]
    
    SFN --> Agent1[🔍 Description Enhancer]
    SFN --> Agent2[💻 Code Creator]
    SFN --> Agent3[🔍 Review & Refine]
    SFN --> Agent4[📦 Finalizer]
    
    Agent1 --> DDB[(🗄️ DynamoDB)]
    Agent2 --> DDB
    Agent3 --> DDB
    Agent4 --> DDB
    
    Agent2 --> S3[🪣 S3 Artifacts]
    Agent3 --> S3
    Agent4 --> S3
    
    SFN -.-> CW[📊 CloudWatch]
    API -.-> CW
```

## 🛠️ Tech Stack

- **Infrastructure**: AWS CDK (TypeScript)
- **Compute**: AWS Lambda (Node.js 20.x)
- **Orchestration**: AWS Step Functions
- **Storage**: DynamoDB, S3
- **API**: API Gateway with API Key authentication
- **Monitoring**: CloudWatch, X-Ray
- **AI/ML**: Google Gemini 1.5 Pro + DeepSeek R1 via OpenRouter (intelligent provider selection)
- **Security**: KMS encryption, IAM least privilege

## 📋 Prerequisites

- Node.js 18+ and npm
- AWS CLI configured with appropriate permissions
- AWS CDK CLI installed (`npm install -g aws-cdk`)
- Google Gemini API key and/or OpenRouter API key (for DeepSeek access)

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd ai-agent-workflow-serverless
npm install
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
export GEMINI_API_KEY="your-gemini-api-key"
export DEEPSEEK_API_KEY="your-deepseek-api-key"
export AWS_ACCOUNT_ID="your-aws-account-id"
export AWS_REGION="us-east-1"
export NOTIFICATION_EMAIL="<EMAIL>"
```

### 3. Bootstrap CDK (first time only)

```bash
npm run bootstrap
```

### 4. Deploy Infrastructure

```bash
npm run deploy
```

### 5. Test the System

```bash
# Get the API endpoint from CDK output
export API_ENDPOINT="https://your-api-id.execute-api.region.amazonaws.com/prod"
export API_KEY="your-api-key"

# Test description enhancement
curl -X POST $API_ENDPOINT/enhance \
  -H "Content-Type: application/json" \
  -H "x-api-key: $API_KEY" \
  -d '{
    "userDescription": "Create a modern e-commerce website with user authentication, product catalog, shopping cart, and payment integration",
    "preferences": {
      "techStack": ["react", "next.js"],
      "deploymentTarget": "aws",
      "includeTests": true,
      "includeCICD": true
    }
  }'
```

## 📖 API Documentation

### POST /enhance

Enhances a user description and creates a detailed specification.

**Request:**
```json
{
  "userDescription": "string",
  "preferences": {
    "techStack": ["string"],
    "deploymentTarget": "aws|vercel|netlify",
    "includeTests": boolean,
    "includeCICD": boolean
  }
}
```

**Response:**
```json
{
  "sessionId": "string",
  "specification": { /* DetailedSpecification */ },
  "taskPlan": { /* TaskPlan */ },
  "status": "enhancement_completed"
}
```

### POST /workflow

Starts the complete workflow from description to final artifacts.

**Request:**
```json
{
  "userDescription": "string",
  "preferences": { /* same as /enhance */ }
}
```

**Response:**
```json
{
  "executionArn": "string",
  "startDate": "string",
  "message": "Workflow started successfully"
}
```

### GET /status

Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "string",
  "version": "1.0.0"
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Google Gemini API key for LLM calls | One of Gemini/DeepSeek |
| `DEEPSEEK_API_KEY` | DeepSeek API key for LLM calls | One of Gemini/DeepSeek |
| `AWS_ACCOUNT_ID` | AWS account ID | Yes |
| `AWS_REGION` | AWS region for deployment | Yes |
| `NOTIFICATION_EMAIL` | Email for error notifications | No |
| `LOG_LEVEL` | Logging level (debug, info, warn, error) | No |

### CDK Context

Configure CDK behavior in `cdk.json`:

```json
{
  "context": {
    "@aws-cdk/core:enableStackNameDuplicates": true,
    "@aws-cdk/aws-lambda:recognizeLayerVersion": true
  }
}
```

## 🏗️ Development

### Project Structure

```
src/
├── agents/                 # AI agent implementations
│   ├── description-enhancer/
│   ├── code-creator/
│   ├── review-refine/
│   └── finalizer/
├── infrastructure/         # CDK infrastructure code
├── step-functions/         # Step Functions definitions
├── utils/                  # Shared utilities
├── types/                  # TypeScript type definitions
├── database/               # DynamoDB schemas
└── monitoring/             # CloudWatch dashboards
```

### Local Development

```bash
# Build TypeScript
npm run build

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format

# Watch for changes
npm run watch
```

### Testing

```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run with coverage
npm run test:coverage
```

## 📊 Monitoring

### CloudWatch Dashboard

Access the CloudWatch dashboard to monitor:
- API Gateway requests and errors
- Lambda function performance
- Step Functions execution status
- DynamoDB metrics
- Custom business metrics

### Alarms

The system includes pre-configured alarms for:
- High error rates (>5%)
- Long execution times (>1 hour)
- DynamoDB throttling
- Lambda function errors

### X-Ray Tracing

X-Ray tracing is enabled for:
- API Gateway requests
- Lambda function executions
- Step Functions workflows

## 🔒 Security

### Authentication

- API Gateway uses API Key authentication
- All Lambda functions use IAM roles with least privilege
- KMS encryption for data at rest

### Data Protection

- All data encrypted in transit and at rest
- S3 bucket with versioning and lifecycle policies
- DynamoDB with point-in-time recovery

### Security Scanning

The system includes automated security scanning for:
- Code vulnerabilities
- Hardcoded secrets
- SQL injection patterns
- XSS vulnerabilities

## 🚀 Deployment

### Production Deployment

1. **Update Environment**: Set production environment variables
2. **Configure Monitoring**: Set up proper alerting and notifications
3. **Security Review**: Review IAM policies and security configurations
4. **Deploy**: Use CI/CD pipeline or manual deployment

```bash
# Production deployment
ENVIRONMENT=production npm run deploy
```

### CI/CD Pipeline

The system can be integrated with:
- AWS CodePipeline
- GitHub Actions
- GitLab CI/CD
- Jenkins

Example GitHub Actions workflow:

```yaml
name: Deploy AI Agent Workflow
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm test
      - run: npm run deploy
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
```

## 🔧 Troubleshooting

### Common Issues

1. **Deployment Fails**
   - Check AWS credentials and permissions
   - Verify CDK bootstrap is complete
   - Check for resource naming conflicts

2. **Lambda Timeouts**
   - Increase timeout in CDK configuration
   - Optimize LLM prompt size
   - Check network connectivity

3. **DynamoDB Throttling**
   - Review read/write capacity settings
   - Check for hot partitions
   - Consider using on-demand billing

4. **API Gateway Errors**
   - Verify API key configuration
   - Check CORS settings
   - Review request/response mappings

### Debugging

```bash
# View CloudWatch logs
aws logs tail /aws/lambda/function-name --follow

# Check Step Functions execution
aws stepfunctions describe-execution --execution-arn <arn>

# Monitor DynamoDB metrics
aws cloudwatch get-metric-statistics --namespace AWS/DynamoDB
```

## 📚 Additional Resources

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS Step Functions Guide](https://docs.aws.amazon.com/step-functions/)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting guide
- Review CloudWatch logs and metrics

---

**Built with ❤️ using AWS CDK and TypeScript**
