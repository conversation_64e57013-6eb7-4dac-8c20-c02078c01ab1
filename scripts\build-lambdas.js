const fs = require('fs');
const path = require('path');

// Create dist directories for Lambda functions
const lambdaDirs = [
  'dist/agents/description-enhancer',
  'dist/agents/code-creator', 
  'dist/agents/review-refine',
  'dist/agents/finalizer'
];

lambdaDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Copy compiled JS files and create index.js for each Lambda
const agents = [
  'description-enhancer',
  'code-creator',
  'review-refine', 
  'finalizer'
];

agents.forEach(agent => {
  const srcFile = `dist/agents/${agent}/handler.js`;
  const destDir = `dist/agents/${agent}`;
  const indexFile = `${destDir}/index.js`;
  
  // Create index.js that exports the handler
  const indexContent = `
const { handler } = require('./handler');
exports.handler = handler;
`;
  
  fs.writeFileSync(indexFile, indexContent);
  console.log(`Created ${indexFile}`);
});

console.log('Lambda build completed!');
