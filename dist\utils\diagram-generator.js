"use strict";
/**
 * Architecture diagram generation utilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateMermaidDiagram = generateMermaidDiagram;
exports.generateFlowchartDiagram = generateFlowchartDiagram;
exports.generateDeploymentDiagram = generateDeploymentDiagram;
async function generateMermaidDiagram(specification, artifacts) {
    const diagramType = determineDiagramType(specification);
    switch (diagramType) {
        case 'system':
            return generateSystemArchitectureDiagram(specification, artifacts);
        case 'component':
            return generateComponentDiagram(specification, artifacts);
        case 'sequence':
            return generateSequenceDiagram(specification, artifacts);
        case 'database':
            return generateDatabaseDiagram(specification);
        default:
            return generateSystemArchitectureDiagram(specification, artifacts);
    }
}
function determineDiagramType(specification) {
    // Determine the most appropriate diagram type based on the specification
    if (specification.dataModels.length > 3) {
        return 'database';
    }
    if (specification.architecture.components.length > 5) {
        return 'component';
    }
    if (specification.apiEndpoints.length > 10) {
        return 'sequence';
    }
    return 'system';
}
function generateSystemArchitectureDiagram(specification, artifacts) {
    let diagram = `graph TB\n`;
    diagram += `    %% ${specification.projectName} - System Architecture\n\n`;
    // Add user/client
    diagram += `    User[👤 User]\n`;
    // Add frontend components
    if (specification.techStack.frontend) {
        diagram += `    Frontend[🖥️ Frontend<br/>${specification.techStack.frontend.framework}]\n`;
        diagram += `    User --> Frontend\n`;
    }
    // Add API Gateway if serverless
    if (specification.architecture.type === 'serverless') {
        diagram += `    API[🚪 API Gateway]\n`;
        diagram += `    Frontend --> API\n`;
    }
    // Add backend components
    if (specification.techStack.backend) {
        diagram += `    Backend[⚙️ Backend<br/>${specification.techStack.backend.framework}]\n`;
        if (specification.architecture.type === 'serverless') {
            diagram += `    API --> Backend\n`;
        }
        else {
            diagram += `    Frontend --> Backend\n`;
        }
    }
    // Add database
    if (specification.techStack.backend?.database) {
        diagram += `    DB[(🗄️ Database<br/>${specification.techStack.backend.database})]\n`;
        diagram += `    Backend --> DB\n`;
    }
    // Add external services
    const externalServices = extractExternalServices(specification, artifacts);
    externalServices.forEach((service, index) => {
        diagram += `    Ext${index}[🌐 ${service}]\n`;
        diagram += `    Backend --> Ext${index}\n`;
    });
    // Add chatbot if present
    if (specification.chatbotFlows && specification.chatbotFlows.length > 0) {
        diagram += `    Chatbot[🤖 Chatbot]\n`;
        diagram += `    Frontend --> Chatbot\n`;
        diagram += `    Chatbot --> Backend\n`;
    }
    // Add monitoring and logging
    if (specification.techStack.infrastructure?.monitoring) {
        diagram += `    Monitor[📊 Monitoring<br/>${specification.techStack.infrastructure.monitoring}]\n`;
        diagram += `    Backend -.-> Monitor\n`;
        diagram += `    Frontend -.-> Monitor\n`;
    }
    // Add styling
    diagram += `\n    %% Styling\n`;
    diagram += `    classDef frontend fill:#e1f5fe\n`;
    diagram += `    classDef backend fill:#f3e5f5\n`;
    diagram += `    classDef database fill:#e8f5e8\n`;
    diagram += `    classDef external fill:#fff3e0\n`;
    diagram += `    classDef monitoring fill:#fce4ec\n`;
    diagram += `    class Frontend frontend\n`;
    diagram += `    class Backend backend\n`;
    diagram += `    class DB database\n`;
    diagram += `    class Monitor monitoring\n`;
    externalServices.forEach((_, index) => {
        diagram += `    class Ext${index} external\n`;
    });
    return diagram;
}
function generateComponentDiagram(specification, artifacts) {
    let diagram = `graph TD\n`;
    diagram += `    %% ${specification.projectName} - Component Architecture\n\n`;
    // Group components by type
    const frontendComponents = specification.architecture.components.filter(c => c.type === 'frontend');
    const backendComponents = specification.architecture.components.filter(c => c.type === 'backend');
    const apiComponents = specification.architecture.components.filter(c => c.type === 'api');
    // Add frontend components
    if (frontendComponents.length > 0) {
        diagram += `    subgraph Frontend["🖥️ Frontend Layer"]\n`;
        frontendComponents.forEach(component => {
            diagram += `        ${component.id}[${component.name}]\n`;
        });
        diagram += `    end\n\n`;
    }
    // Add API components
    if (apiComponents.length > 0) {
        diagram += `    subgraph API["🚪 API Layer"]\n`;
        apiComponents.forEach(component => {
            diagram += `        ${component.id}[${component.name}]\n`;
        });
        diagram += `    end\n\n`;
    }
    // Add backend components
    if (backendComponents.length > 0) {
        diagram += `    subgraph Backend["⚙️ Backend Layer"]\n`;
        backendComponents.forEach(component => {
            diagram += `        ${component.id}[${component.name}]\n`;
        });
        diagram += `    end\n\n`;
    }
    // Add relationships
    specification.architecture.integrations.forEach(integration => {
        diagram += `    ${integration.source} --> ${integration.target}\n`;
    });
    return diagram;
}
function generateSequenceDiagram(specification, artifacts) {
    let diagram = `sequenceDiagram\n`;
    diagram += `    participant U as 👤 User\n`;
    diagram += `    participant F as 🖥️ Frontend\n`;
    diagram += `    participant A as 🚪 API\n`;
    diagram += `    participant B as ⚙️ Backend\n`;
    diagram += `    participant D as 🗄️ Database\n\n`;
    // Generate sequence for main user flows
    const mainEndpoints = specification.apiEndpoints.slice(0, 5); // Limit to first 5 endpoints
    mainEndpoints.forEach(endpoint => {
        const action = endpoint.method === 'GET' ? 'Request' : 'Submit';
        const response = endpoint.method === 'GET' ? 'Data' : 'Confirmation';
        diagram += `    U->>F: ${action} ${endpoint.path}\n`;
        diagram += `    F->>A: ${endpoint.method} ${endpoint.path}\n`;
        diagram += `    A->>B: Process Request\n`;
        if (endpoint.method !== 'GET') {
            diagram += `    B->>D: Store/Update Data\n`;
            diagram += `    D-->>B: Success\n`;
        }
        else {
            diagram += `    B->>D: Query Data\n`;
            diagram += `    D-->>B: Return Data\n`;
        }
        diagram += `    B-->>A: ${response}\n`;
        diagram += `    A-->>F: ${response}\n`;
        diagram += `    F-->>U: Display ${response}\n\n`;
    });
    return diagram;
}
function generateDatabaseDiagram(specification) {
    let diagram = `erDiagram\n`;
    diagram += `    %% ${specification.projectName} - Database Schema\n\n`;
    specification.dataModels.forEach(model => {
        diagram += `    ${model.name} {\n`;
        model.fields.forEach(field => {
            const type = mapFieldTypeToERD(field.type);
            const constraint = field.required ? 'NOT NULL' : '';
            diagram += `        ${type} ${field.name} ${constraint}\n`;
        });
        diagram += `    }\n\n`;
    });
    // Add relationships
    specification.dataModels.forEach(model => {
        model.relationships.forEach(rel => {
            const relationshipType = mapRelationshipType(rel.type);
            diagram += `    ${model.name} ${relationshipType} ${rel.target} : "${rel.type}"\n`;
        });
    });
    return diagram;
}
function extractExternalServices(specification, artifacts) {
    const services = new Set();
    // Check for common external services in the specification
    if (specification.techStack.backend?.authentication === 'OAuth') {
        services.add('OAuth Provider');
    }
    // Check artifacts for external service usage
    artifacts.forEach(artifact => {
        artifact.files.forEach(file => {
            const content = file.content.toLowerCase();
            // Check for common external services
            if (content.includes('stripe'))
                services.add('Stripe');
            if (content.includes('sendgrid') || content.includes('mailgun'))
                services.add('Email Service');
            if (content.includes('twilio'))
                services.add('SMS Service');
            if (content.includes('aws-sdk'))
                services.add('AWS Services');
            if (content.includes('firebase'))
                services.add('Firebase');
            if (content.includes('redis'))
                services.add('Redis Cache');
            if (content.includes('elasticsearch'))
                services.add('Elasticsearch');
        });
    });
    return Array.from(services);
}
function mapFieldTypeToERD(fieldType) {
    const typeMap = {
        'string': 'varchar',
        'number': 'int',
        'boolean': 'boolean',
        'date': 'datetime',
        'array': 'json',
        'object': 'json',
    };
    return typeMap[fieldType.toLowerCase()] || 'varchar';
}
function mapRelationshipType(relType) {
    const relationshipMap = {
        'oneToOne': '||--||',
        'oneToMany': '||--o{',
        'manyToMany': '}o--o{',
    };
    return relationshipMap[relType] || '||--o{';
}
function generateFlowchartDiagram(specification, artifacts) {
    let diagram = `flowchart TD\n`;
    diagram += `    %% ${specification.projectName} - User Flow\n\n`;
    diagram += `    Start([👤 User Starts])\n`;
    diagram += `    Auth{🔐 Authenticated?}\n`;
    diagram += `    Login[📝 Login]\n`;
    diagram += `    Dashboard[📊 Dashboard]\n`;
    diagram += `    Features[⚡ Features]\n`;
    diagram += `    End([✅ Complete])\n\n`;
    diagram += `    Start --> Auth\n`;
    diagram += `    Auth -->|No| Login\n`;
    diagram += `    Auth -->|Yes| Dashboard\n`;
    diagram += `    Login --> Dashboard\n`;
    diagram += `    Dashboard --> Features\n`;
    diagram += `    Features --> End\n`;
    return diagram;
}
function generateDeploymentDiagram(specification) {
    let diagram = `graph TB\n`;
    diagram += `    %% ${specification.projectName} - Deployment Architecture\n\n`;
    if (specification.architecture.type === 'serverless') {
        diagram += `    subgraph AWS["☁️ AWS Cloud"]\n`;
        diagram += `        CF[📦 CloudFront]\n`;
        diagram += `        S3[🪣 S3 Bucket]\n`;
        diagram += `        API[🚪 API Gateway]\n`;
        diagram += `        Lambda[⚡ Lambda Functions]\n`;
        diagram += `        RDS[(🗄️ RDS Database)]\n`;
        diagram += `        CW[📊 CloudWatch]\n`;
        diagram += `    end\n\n`;
        diagram += `    User[👤 User] --> CF\n`;
        diagram += `    CF --> S3\n`;
        diagram += `    CF --> API\n`;
        diagram += `    API --> Lambda\n`;
        diagram += `    Lambda --> RDS\n`;
        diagram += `    Lambda -.-> CW\n`;
    }
    else {
        diagram += `    subgraph Cloud["☁️ Cloud Infrastructure"]\n`;
        diagram += `        LB[⚖️ Load Balancer]\n`;
        diagram += `        App[🖥️ Application Servers]\n`;
        diagram += `        DB[(🗄️ Database)]\n`;
        diagram += `        Cache[⚡ Cache]\n`;
        diagram += `    end\n\n`;
        diagram += `    User[👤 User] --> LB\n`;
        diagram += `    LB --> App\n`;
        diagram += `    App --> DB\n`;
        diagram += `    App --> Cache\n`;
    }
    return diagram;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGlhZ3JhbS1nZW5lcmF0b3IuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvdXRpbHMvZGlhZ3JhbS1nZW5lcmF0b3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOztHQUVHOztBQUlILHdEQW1CQztBQTJRRCw0REFzQkM7QUFFRCw4REFtQ0M7QUF6Vk0sS0FBSyxVQUFVLHNCQUFzQixDQUMxQyxhQUFvQyxFQUNwQyxTQUF5QjtJQUd6QixNQUFNLFdBQVcsR0FBRyxvQkFBb0IsQ0FBQyxhQUFhLENBQUMsQ0FBQztJQUV4RCxRQUFRLFdBQVcsRUFBRSxDQUFDO1FBQ3BCLEtBQUssUUFBUTtZQUNYLE9BQU8saUNBQWlDLENBQUMsYUFBYSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBQ3JFLEtBQUssV0FBVztZQUNkLE9BQU8sd0JBQXdCLENBQUMsYUFBYSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBQzVELEtBQUssVUFBVTtZQUNiLE9BQU8sdUJBQXVCLENBQUMsYUFBYSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1FBQzNELEtBQUssVUFBVTtZQUNiLE9BQU8sdUJBQXVCLENBQUMsYUFBYSxDQUFDLENBQUM7UUFDaEQ7WUFDRSxPQUFPLGlDQUFpQyxDQUFDLGFBQWEsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUN2RSxDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVMsb0JBQW9CLENBQUMsYUFBb0M7SUFDaEUseUVBQXlFO0lBQ3pFLElBQUksYUFBYSxDQUFDLFVBQVUsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDeEMsT0FBTyxVQUFVLENBQUM7SUFDcEIsQ0FBQztJQUVELElBQUksYUFBYSxDQUFDLFlBQVksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3JELE9BQU8sV0FBVyxDQUFDO0lBQ3JCLENBQUM7SUFFRCxJQUFJLGFBQWEsQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEVBQUUsRUFBRSxDQUFDO1FBQzNDLE9BQU8sVUFBVSxDQUFDO0lBQ3BCLENBQUM7SUFFRCxPQUFPLFFBQVEsQ0FBQztBQUNsQixDQUFDO0FBRUQsU0FBUyxpQ0FBaUMsQ0FDeEMsYUFBb0MsRUFDcEMsU0FBeUI7SUFHekIsSUFBSSxPQUFPLEdBQUcsWUFBWSxDQUFDO0lBQzNCLE9BQU8sSUFBSSxVQUFVLGFBQWEsQ0FBQyxXQUFXLDRCQUE0QixDQUFDO0lBRTNFLGtCQUFrQjtJQUNsQixPQUFPLElBQUkscUJBQXFCLENBQUM7SUFFakMsMEJBQTBCO0lBQzFCLElBQUksYUFBYSxDQUFDLFNBQVMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNyQyxPQUFPLElBQUksaUNBQWlDLGFBQWEsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLFNBQVMsS0FBSyxDQUFDO1FBQzVGLE9BQU8sSUFBSSx5QkFBeUIsQ0FBQztJQUN2QyxDQUFDO0lBRUQsZ0NBQWdDO0lBQ2hDLElBQUksYUFBYSxDQUFDLFlBQVksQ0FBQyxJQUFJLEtBQUssWUFBWSxFQUFFLENBQUM7UUFDckQsT0FBTyxJQUFJLDJCQUEyQixDQUFDO1FBQ3ZDLE9BQU8sSUFBSSx3QkFBd0IsQ0FBQztJQUN0QyxDQUFDO0lBRUQseUJBQXlCO0lBQ3pCLElBQUksYUFBYSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUNwQyxPQUFPLElBQUksOEJBQThCLGFBQWEsQ0FBQyxTQUFTLENBQUMsT0FBTyxDQUFDLFNBQVMsS0FBSyxDQUFDO1FBRXhGLElBQUksYUFBYSxDQUFDLFlBQVksQ0FBQyxJQUFJLEtBQUssWUFBWSxFQUFFLENBQUM7WUFDckQsT0FBTyxJQUFJLHVCQUF1QixDQUFDO1FBQ3JDLENBQUM7YUFBTSxDQUFDO1lBQ04sT0FBTyxJQUFJLDRCQUE0QixDQUFDO1FBQzFDLENBQUM7SUFDSCxDQUFDO0lBRUQsZUFBZTtJQUNmLElBQUksYUFBYSxDQUFDLFNBQVMsQ0FBQyxPQUFPLEVBQUUsUUFBUSxFQUFFLENBQUM7UUFDOUMsT0FBTyxJQUFJLDRCQUE0QixhQUFhLENBQUMsU0FBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLE1BQU0sQ0FBQztRQUN0RixPQUFPLElBQUksc0JBQXNCLENBQUM7SUFDcEMsQ0FBQztJQUVELHdCQUF3QjtJQUN4QixNQUFNLGdCQUFnQixHQUFHLHVCQUF1QixDQUFDLGFBQWEsRUFBRSxTQUFTLENBQUMsQ0FBQztJQUMzRSxnQkFBZ0IsQ0FBQyxPQUFPLENBQUMsQ0FBQyxPQUFPLEVBQUUsS0FBSyxFQUFFLEVBQUU7UUFDMUMsT0FBTyxJQUFJLFVBQVUsS0FBSyxPQUFPLE9BQU8sS0FBSyxDQUFDO1FBQzlDLE9BQU8sSUFBSSxzQkFBc0IsS0FBSyxJQUFJLENBQUM7SUFDN0MsQ0FBQyxDQUFDLENBQUM7SUFFSCx5QkFBeUI7SUFDekIsSUFBSSxhQUFhLENBQUMsWUFBWSxJQUFJLGFBQWEsQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3hFLE9BQU8sSUFBSSwyQkFBMkIsQ0FBQztRQUN2QyxPQUFPLElBQUksNEJBQTRCLENBQUM7UUFDeEMsT0FBTyxJQUFJLDJCQUEyQixDQUFDO0lBQ3pDLENBQUM7SUFFRCw2QkFBNkI7SUFDN0IsSUFBSSxhQUFhLENBQUMsU0FBUyxDQUFDLGNBQWMsRUFBRSxVQUFVLEVBQUUsQ0FBQztRQUN2RCxPQUFPLElBQUksaUNBQWlDLGFBQWEsQ0FBQyxTQUFTLENBQUMsY0FBYyxDQUFDLFVBQVUsS0FBSyxDQUFDO1FBQ25HLE9BQU8sSUFBSSw0QkFBNEIsQ0FBQztRQUN4QyxPQUFPLElBQUksNkJBQTZCLENBQUM7SUFDM0MsQ0FBQztJQUVELGNBQWM7SUFDZCxPQUFPLElBQUksb0JBQW9CLENBQUM7SUFDaEMsT0FBTyxJQUFJLHNDQUFzQyxDQUFDO0lBQ2xELE9BQU8sSUFBSSxxQ0FBcUMsQ0FBQztJQUNqRCxPQUFPLElBQUksc0NBQXNDLENBQUM7SUFDbEQsT0FBTyxJQUFJLHNDQUFzQyxDQUFDO0lBQ2xELE9BQU8sSUFBSSx3Q0FBd0MsQ0FBQztJQUVwRCxPQUFPLElBQUksK0JBQStCLENBQUM7SUFDM0MsT0FBTyxJQUFJLDZCQUE2QixDQUFDO0lBQ3pDLE9BQU8sSUFBSSx5QkFBeUIsQ0FBQztJQUNyQyxPQUFPLElBQUksZ0NBQWdDLENBQUM7SUFFNUMsZ0JBQWdCLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLEtBQUssRUFBRSxFQUFFO1FBQ3BDLE9BQU8sSUFBSSxnQkFBZ0IsS0FBSyxhQUFhLENBQUM7SUFDaEQsQ0FBQyxDQUFDLENBQUM7SUFFSCxPQUFPLE9BQU8sQ0FBQztBQUNqQixDQUFDO0FBRUQsU0FBUyx3QkFBd0IsQ0FDL0IsYUFBb0MsRUFDcEMsU0FBeUI7SUFHekIsSUFBSSxPQUFPLEdBQUcsWUFBWSxDQUFDO0lBQzNCLE9BQU8sSUFBSSxVQUFVLGFBQWEsQ0FBQyxXQUFXLCtCQUErQixDQUFDO0lBRTlFLDJCQUEyQjtJQUMzQixNQUFNLGtCQUFrQixHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLEtBQUssVUFBVSxDQUFDLENBQUM7SUFDcEcsTUFBTSxpQkFBaUIsR0FBRyxhQUFhLENBQUMsWUFBWSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxLQUFLLFNBQVMsQ0FBQyxDQUFDO0lBQ2xHLE1BQU0sYUFBYSxHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUMsVUFBVSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLEtBQUssS0FBSyxDQUFDLENBQUM7SUFFMUYsMEJBQTBCO0lBQzFCLElBQUksa0JBQWtCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ2xDLE9BQU8sSUFBSSwrQ0FBK0MsQ0FBQztRQUMzRCxrQkFBa0IsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDckMsT0FBTyxJQUFJLFdBQVcsU0FBUyxDQUFDLEVBQUUsSUFBSSxTQUFTLENBQUMsSUFBSSxLQUFLLENBQUM7UUFDNUQsQ0FBQyxDQUFDLENBQUM7UUFDSCxPQUFPLElBQUksYUFBYSxDQUFDO0lBQzNCLENBQUM7SUFFRCxxQkFBcUI7SUFDckIsSUFBSSxhQUFhLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQzdCLE9BQU8sSUFBSSxvQ0FBb0MsQ0FBQztRQUNoRCxhQUFhLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxFQUFFO1lBQ2hDLE9BQU8sSUFBSSxXQUFXLFNBQVMsQ0FBQyxFQUFFLElBQUksU0FBUyxDQUFDLElBQUksS0FBSyxDQUFDO1FBQzVELENBQUMsQ0FBQyxDQUFDO1FBQ0gsT0FBTyxJQUFJLGFBQWEsQ0FBQztJQUMzQixDQUFDO0lBRUQseUJBQXlCO0lBQ3pCLElBQUksaUJBQWlCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ2pDLE9BQU8sSUFBSSw0Q0FBNEMsQ0FBQztRQUN4RCxpQkFBaUIsQ0FBQyxPQUFPLENBQUMsU0FBUyxDQUFDLEVBQUU7WUFDcEMsT0FBTyxJQUFJLFdBQVcsU0FBUyxDQUFDLEVBQUUsSUFBSSxTQUFTLENBQUMsSUFBSSxLQUFLLENBQUM7UUFDNUQsQ0FBQyxDQUFDLENBQUM7UUFDSCxPQUFPLElBQUksYUFBYSxDQUFDO0lBQzNCLENBQUM7SUFFRCxvQkFBb0I7SUFDcEIsYUFBYSxDQUFDLFlBQVksQ0FBQyxZQUFZLENBQUMsT0FBTyxDQUFDLFdBQVcsQ0FBQyxFQUFFO1FBQzVELE9BQU8sSUFBSSxPQUFPLFdBQVcsQ0FBQyxNQUFNLFFBQVEsV0FBVyxDQUFDLE1BQU0sSUFBSSxDQUFDO0lBQ3JFLENBQUMsQ0FBQyxDQUFDO0lBRUgsT0FBTyxPQUFPLENBQUM7QUFDakIsQ0FBQztBQUVELFNBQVMsdUJBQXVCLENBQzlCLGFBQW9DLEVBQ3BDLFNBQXlCO0lBR3pCLElBQUksT0FBTyxHQUFHLG1CQUFtQixDQUFDO0lBQ2xDLE9BQU8sSUFBSSxnQ0FBZ0MsQ0FBQztJQUM1QyxPQUFPLElBQUkscUNBQXFDLENBQUM7SUFDakQsT0FBTyxJQUFJLCtCQUErQixDQUFDO0lBQzNDLE9BQU8sSUFBSSxtQ0FBbUMsQ0FBQztJQUMvQyxPQUFPLElBQUksdUNBQXVDLENBQUM7SUFFbkQsd0NBQXdDO0lBQ3hDLE1BQU0sYUFBYSxHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLDZCQUE2QjtJQUUzRixhQUFhLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1FBQy9CLE1BQU0sTUFBTSxHQUFHLFFBQVEsQ0FBQyxNQUFNLEtBQUssS0FBSyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQztRQUNoRSxNQUFNLFFBQVEsR0FBRyxRQUFRLENBQUMsTUFBTSxLQUFLLEtBQUssQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxjQUFjLENBQUM7UUFFckUsT0FBTyxJQUFJLGNBQWMsTUFBTSxJQUFJLFFBQVEsQ0FBQyxJQUFJLElBQUksQ0FBQztRQUNyRCxPQUFPLElBQUksY0FBYyxRQUFRLENBQUMsTUFBTSxJQUFJLFFBQVEsQ0FBQyxJQUFJLElBQUksQ0FBQztRQUM5RCxPQUFPLElBQUksOEJBQThCLENBQUM7UUFFMUMsSUFBSSxRQUFRLENBQUMsTUFBTSxLQUFLLEtBQUssRUFBRSxDQUFDO1lBQzlCLE9BQU8sSUFBSSxnQ0FBZ0MsQ0FBQztZQUM1QyxPQUFPLElBQUksdUJBQXVCLENBQUM7UUFDckMsQ0FBQzthQUFNLENBQUM7WUFDTixPQUFPLElBQUkseUJBQXlCLENBQUM7WUFDckMsT0FBTyxJQUFJLDJCQUEyQixDQUFDO1FBQ3pDLENBQUM7UUFFRCxPQUFPLElBQUksZUFBZSxRQUFRLElBQUksQ0FBQztRQUN2QyxPQUFPLElBQUksZUFBZSxRQUFRLElBQUksQ0FBQztRQUN2QyxPQUFPLElBQUksdUJBQXVCLFFBQVEsTUFBTSxDQUFDO0lBQ25ELENBQUMsQ0FBQyxDQUFDO0lBRUgsT0FBTyxPQUFPLENBQUM7QUFDakIsQ0FBQztBQUVELFNBQVMsdUJBQXVCLENBQUMsYUFBb0M7SUFDbkUsSUFBSSxPQUFPLEdBQUcsYUFBYSxDQUFDO0lBQzVCLE9BQU8sSUFBSSxVQUFVLGFBQWEsQ0FBQyxXQUFXLHdCQUF3QixDQUFDO0lBRXZFLGFBQWEsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxFQUFFO1FBQ3ZDLE9BQU8sSUFBSSxPQUFPLEtBQUssQ0FBQyxJQUFJLE1BQU0sQ0FBQztRQUVuQyxLQUFLLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsRUFBRTtZQUMzQixNQUFNLElBQUksR0FBRyxpQkFBaUIsQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDM0MsTUFBTSxVQUFVLEdBQUcsS0FBSyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7WUFDcEQsT0FBTyxJQUFJLFdBQVcsSUFBSSxJQUFJLEtBQUssQ0FBQyxJQUFJLElBQUksVUFBVSxJQUFJLENBQUM7UUFDN0QsQ0FBQyxDQUFDLENBQUM7UUFFSCxPQUFPLElBQUksV0FBVyxDQUFDO0lBQ3pCLENBQUMsQ0FBQyxDQUFDO0lBRUgsb0JBQW9CO0lBQ3BCLGFBQWEsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLEtBQUssQ0FBQyxFQUFFO1FBQ3ZDLEtBQUssQ0FBQyxhQUFhLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ2hDLE1BQU0sZ0JBQWdCLEdBQUcsbUJBQW1CLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3ZELE9BQU8sSUFBSSxPQUFPLEtBQUssQ0FBQyxJQUFJLElBQUksZ0JBQWdCLElBQUksR0FBRyxDQUFDLE1BQU0sT0FBTyxHQUFHLENBQUMsSUFBSSxLQUFLLENBQUM7UUFDckYsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILE9BQU8sT0FBTyxDQUFDO0FBQ2pCLENBQUM7QUFFRCxTQUFTLHVCQUF1QixDQUM5QixhQUFvQyxFQUNwQyxTQUF5QjtJQUV6QixNQUFNLFFBQVEsR0FBRyxJQUFJLEdBQUcsRUFBVSxDQUFDO0lBRW5DLDBEQUEwRDtJQUMxRCxJQUFJLGFBQWEsQ0FBQyxTQUFTLENBQUMsT0FBTyxFQUFFLGNBQWMsS0FBSyxPQUFPLEVBQUUsQ0FBQztRQUNoRSxRQUFRLENBQUMsR0FBRyxDQUFDLGdCQUFnQixDQUFDLENBQUM7SUFDakMsQ0FBQztJQUVELDZDQUE2QztJQUM3QyxTQUFTLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1FBQzNCLFFBQVEsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQzVCLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsV0FBVyxFQUFFLENBQUM7WUFFM0MscUNBQXFDO1lBQ3JDLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUM7Z0JBQUUsUUFBUSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN2RCxJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUM7Z0JBQUUsUUFBUSxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQUMsQ0FBQztZQUMvRixJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDO2dCQUFFLFFBQVEsQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDNUQsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQztnQkFBRSxRQUFRLENBQUMsR0FBRyxDQUFDLGNBQWMsQ0FBQyxDQUFDO1lBQzlELElBQUksT0FBTyxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUM7Z0JBQUUsUUFBUSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztZQUMzRCxJQUFJLE9BQU8sQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDO2dCQUFFLFFBQVEsQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDLENBQUM7WUFDM0QsSUFBSSxPQUFPLENBQUMsUUFBUSxDQUFDLGVBQWUsQ0FBQztnQkFBRSxRQUFRLENBQUMsR0FBRyxDQUFDLGVBQWUsQ0FBQyxDQUFDO1FBQ3ZFLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxPQUFPLEtBQUssQ0FBQyxJQUFJLENBQUMsUUFBUSxDQUFDLENBQUM7QUFDOUIsQ0FBQztBQUVELFNBQVMsaUJBQWlCLENBQUMsU0FBaUI7SUFDMUMsTUFBTSxPQUFPLEdBQTJCO1FBQ3RDLFFBQVEsRUFBRSxTQUFTO1FBQ25CLFFBQVEsRUFBRSxLQUFLO1FBQ2YsU0FBUyxFQUFFLFNBQVM7UUFDcEIsTUFBTSxFQUFFLFVBQVU7UUFDbEIsT0FBTyxFQUFFLE1BQU07UUFDZixRQUFRLEVBQUUsTUFBTTtLQUNqQixDQUFDO0lBRUYsT0FBTyxPQUFPLENBQUMsU0FBUyxDQUFDLFdBQVcsRUFBRSxDQUFDLElBQUksU0FBUyxDQUFDO0FBQ3ZELENBQUM7QUFFRCxTQUFTLG1CQUFtQixDQUFDLE9BQWU7SUFDMUMsTUFBTSxlQUFlLEdBQTJCO1FBQzlDLFVBQVUsRUFBRSxRQUFRO1FBQ3BCLFdBQVcsRUFBRSxRQUFRO1FBQ3JCLFlBQVksRUFBRSxRQUFRO0tBQ3ZCLENBQUM7SUFFRixPQUFPLGVBQWUsQ0FBQyxPQUFPLENBQUMsSUFBSSxRQUFRLENBQUM7QUFDOUMsQ0FBQztBQUVELFNBQWdCLHdCQUF3QixDQUN0QyxhQUFvQyxFQUNwQyxTQUF5QjtJQUV6QixJQUFJLE9BQU8sR0FBRyxnQkFBZ0IsQ0FBQztJQUMvQixPQUFPLElBQUksVUFBVSxhQUFhLENBQUMsV0FBVyxrQkFBa0IsQ0FBQztJQUVqRSxPQUFPLElBQUksK0JBQStCLENBQUM7SUFDM0MsT0FBTyxJQUFJLCtCQUErQixDQUFDO0lBQzNDLE9BQU8sSUFBSSx1QkFBdUIsQ0FBQztJQUNuQyxPQUFPLElBQUksK0JBQStCLENBQUM7SUFDM0MsT0FBTyxJQUFJLDRCQUE0QixDQUFDO0lBQ3hDLE9BQU8sSUFBSSwyQkFBMkIsQ0FBQztJQUV2QyxPQUFPLElBQUksc0JBQXNCLENBQUM7SUFDbEMsT0FBTyxJQUFJLDBCQUEwQixDQUFDO0lBQ3RDLE9BQU8sSUFBSSwrQkFBK0IsQ0FBQztJQUMzQyxPQUFPLElBQUksMkJBQTJCLENBQUM7SUFDdkMsT0FBTyxJQUFJLDhCQUE4QixDQUFDO0lBQzFDLE9BQU8sSUFBSSx3QkFBd0IsQ0FBQztJQUVwQyxPQUFPLE9BQU8sQ0FBQztBQUNqQixDQUFDO0FBRUQsU0FBZ0IseUJBQXlCLENBQUMsYUFBb0M7SUFDNUUsSUFBSSxPQUFPLEdBQUcsWUFBWSxDQUFDO0lBQzNCLE9BQU8sSUFBSSxVQUFVLGFBQWEsQ0FBQyxXQUFXLGdDQUFnQyxDQUFDO0lBRS9FLElBQUksYUFBYSxDQUFDLFlBQVksQ0FBQyxJQUFJLEtBQUssWUFBWSxFQUFFLENBQUM7UUFDckQsT0FBTyxJQUFJLG9DQUFvQyxDQUFDO1FBQ2hELE9BQU8sSUFBSSw2QkFBNkIsQ0FBQztRQUN6QyxPQUFPLElBQUksNEJBQTRCLENBQUM7UUFDeEMsT0FBTyxJQUFJLCtCQUErQixDQUFDO1FBQzNDLE9BQU8sSUFBSSxzQ0FBc0MsQ0FBQztRQUNsRCxPQUFPLElBQUksbUNBQW1DLENBQUM7UUFDL0MsT0FBTyxJQUFJLDZCQUE2QixDQUFDO1FBQ3pDLE9BQU8sSUFBSSxhQUFhLENBQUM7UUFFekIsT0FBTyxJQUFJLDRCQUE0QixDQUFDO1FBQ3hDLE9BQU8sSUFBSSxpQkFBaUIsQ0FBQztRQUM3QixPQUFPLElBQUksa0JBQWtCLENBQUM7UUFDOUIsT0FBTyxJQUFJLHNCQUFzQixDQUFDO1FBQ2xDLE9BQU8sSUFBSSxzQkFBc0IsQ0FBQztRQUNsQyxPQUFPLElBQUksc0JBQXNCLENBQUM7SUFDcEMsQ0FBQztTQUFNLENBQUM7UUFDTixPQUFPLElBQUksaURBQWlELENBQUM7UUFDN0QsT0FBTyxJQUFJLGdDQUFnQyxDQUFDO1FBQzVDLE9BQU8sSUFBSSx3Q0FBd0MsQ0FBQztRQUNwRCxPQUFPLElBQUksOEJBQThCLENBQUM7UUFDMUMsT0FBTyxJQUFJLDBCQUEwQixDQUFDO1FBQ3RDLE9BQU8sSUFBSSxhQUFhLENBQUM7UUFFekIsT0FBTyxJQUFJLDRCQUE0QixDQUFDO1FBQ3hDLE9BQU8sSUFBSSxrQkFBa0IsQ0FBQztRQUM5QixPQUFPLElBQUksa0JBQWtCLENBQUM7UUFDOUIsT0FBTyxJQUFJLHFCQUFxQixDQUFDO0lBQ25DLENBQUM7SUFFRCxPQUFPLE9BQU8sQ0FBQztBQUNqQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBcmNoaXRlY3R1cmUgZGlhZ3JhbSBnZW5lcmF0aW9uIHV0aWxpdGllc1xuICovXG5cbmltcG9ydCB7IERldGFpbGVkU3BlY2lmaWNhdGlvbiwgQ29kZUFydGlmYWN0IH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVNZXJtYWlkRGlhZ3JhbShcbiAgc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uLFxuICBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdXG4pOiBQcm9taXNlPHN0cmluZz4ge1xuICBcbiAgY29uc3QgZGlhZ3JhbVR5cGUgPSBkZXRlcm1pbmVEaWFncmFtVHlwZShzcGVjaWZpY2F0aW9uKTtcbiAgXG4gIHN3aXRjaCAoZGlhZ3JhbVR5cGUpIHtcbiAgICBjYXNlICdzeXN0ZW0nOlxuICAgICAgcmV0dXJuIGdlbmVyYXRlU3lzdGVtQXJjaGl0ZWN0dXJlRGlhZ3JhbShzcGVjaWZpY2F0aW9uLCBhcnRpZmFjdHMpO1xuICAgIGNhc2UgJ2NvbXBvbmVudCc6XG4gICAgICByZXR1cm4gZ2VuZXJhdGVDb21wb25lbnREaWFncmFtKHNwZWNpZmljYXRpb24sIGFydGlmYWN0cyk7XG4gICAgY2FzZSAnc2VxdWVuY2UnOlxuICAgICAgcmV0dXJuIGdlbmVyYXRlU2VxdWVuY2VEaWFncmFtKHNwZWNpZmljYXRpb24sIGFydGlmYWN0cyk7XG4gICAgY2FzZSAnZGF0YWJhc2UnOlxuICAgICAgcmV0dXJuIGdlbmVyYXRlRGF0YWJhc2VEaWFncmFtKHNwZWNpZmljYXRpb24pO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gZ2VuZXJhdGVTeXN0ZW1BcmNoaXRlY3R1cmVEaWFncmFtKHNwZWNpZmljYXRpb24sIGFydGlmYWN0cyk7XG4gIH1cbn1cblxuZnVuY3Rpb24gZGV0ZXJtaW5lRGlhZ3JhbVR5cGUoc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uKTogc3RyaW5nIHtcbiAgLy8gRGV0ZXJtaW5lIHRoZSBtb3N0IGFwcHJvcHJpYXRlIGRpYWdyYW0gdHlwZSBiYXNlZCBvbiB0aGUgc3BlY2lmaWNhdGlvblxuICBpZiAoc3BlY2lmaWNhdGlvbi5kYXRhTW9kZWxzLmxlbmd0aCA+IDMpIHtcbiAgICByZXR1cm4gJ2RhdGFiYXNlJztcbiAgfVxuICBcbiAgaWYgKHNwZWNpZmljYXRpb24uYXJjaGl0ZWN0dXJlLmNvbXBvbmVudHMubGVuZ3RoID4gNSkge1xuICAgIHJldHVybiAnY29tcG9uZW50JztcbiAgfVxuICBcbiAgaWYgKHNwZWNpZmljYXRpb24uYXBpRW5kcG9pbnRzLmxlbmd0aCA+IDEwKSB7XG4gICAgcmV0dXJuICdzZXF1ZW5jZSc7XG4gIH1cbiAgXG4gIHJldHVybiAnc3lzdGVtJztcbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVTeXN0ZW1BcmNoaXRlY3R1cmVEaWFncmFtKFxuICBzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIGFydGlmYWN0czogQ29kZUFydGlmYWN0W11cbik6IHN0cmluZyB7XG4gIFxuICBsZXQgZGlhZ3JhbSA9IGBncmFwaCBUQlxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICAlJSAke3NwZWNpZmljYXRpb24ucHJvamVjdE5hbWV9IC0gU3lzdGVtIEFyY2hpdGVjdHVyZVxcblxcbmA7XG4gIFxuICAvLyBBZGQgdXNlci9jbGllbnRcbiAgZGlhZ3JhbSArPSBgICAgIFVzZXJb8J+RpCBVc2VyXVxcbmA7XG4gIFxuICAvLyBBZGQgZnJvbnRlbmQgY29tcG9uZW50c1xuICBpZiAoc3BlY2lmaWNhdGlvbi50ZWNoU3RhY2suZnJvbnRlbmQpIHtcbiAgICBkaWFncmFtICs9IGAgICAgRnJvbnRlbmRb8J+Wpe+4jyBGcm9udGVuZDxici8+JHtzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5mcm9udGVuZC5mcmFtZXdvcmt9XVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIFVzZXIgLS0+IEZyb250ZW5kXFxuYDtcbiAgfVxuICBcbiAgLy8gQWRkIEFQSSBHYXRld2F5IGlmIHNlcnZlcmxlc3NcbiAgaWYgKHNwZWNpZmljYXRpb24uYXJjaGl0ZWN0dXJlLnR5cGUgPT09ICdzZXJ2ZXJsZXNzJykge1xuICAgIGRpYWdyYW0gKz0gYCAgICBBUElb8J+aqiBBUEkgR2F0ZXdheV1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBGcm9udGVuZCAtLT4gQVBJXFxuYDtcbiAgfVxuICBcbiAgLy8gQWRkIGJhY2tlbmQgY29tcG9uZW50c1xuICBpZiAoc3BlY2lmaWNhdGlvbi50ZWNoU3RhY2suYmFja2VuZCkge1xuICAgIGRpYWdyYW0gKz0gYCAgICBCYWNrZW5kW+Kame+4jyBCYWNrZW5kPGJyLz4ke3NwZWNpZmljYXRpb24udGVjaFN0YWNrLmJhY2tlbmQuZnJhbWV3b3JrfV1cXG5gO1xuICAgIFxuICAgIGlmIChzcGVjaWZpY2F0aW9uLmFyY2hpdGVjdHVyZS50eXBlID09PSAnc2VydmVybGVzcycpIHtcbiAgICAgIGRpYWdyYW0gKz0gYCAgICBBUEkgLS0+IEJhY2tlbmRcXG5gO1xuICAgIH0gZWxzZSB7XG4gICAgICBkaWFncmFtICs9IGAgICAgRnJvbnRlbmQgLS0+IEJhY2tlbmRcXG5gO1xuICAgIH1cbiAgfVxuICBcbiAgLy8gQWRkIGRhdGFiYXNlXG4gIGlmIChzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5iYWNrZW5kPy5kYXRhYmFzZSkge1xuICAgIGRpYWdyYW0gKz0gYCAgICBEQlso8J+XhO+4jyBEYXRhYmFzZTxici8+JHtzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5iYWNrZW5kLmRhdGFiYXNlfSldXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgQmFja2VuZCAtLT4gREJcXG5gO1xuICB9XG4gIFxuICAvLyBBZGQgZXh0ZXJuYWwgc2VydmljZXNcbiAgY29uc3QgZXh0ZXJuYWxTZXJ2aWNlcyA9IGV4dHJhY3RFeHRlcm5hbFNlcnZpY2VzKHNwZWNpZmljYXRpb24sIGFydGlmYWN0cyk7XG4gIGV4dGVybmFsU2VydmljZXMuZm9yRWFjaCgoc2VydmljZSwgaW5kZXgpID0+IHtcbiAgICBkaWFncmFtICs9IGAgICAgRXh0JHtpbmRleH1b8J+MkCAke3NlcnZpY2V9XVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIEJhY2tlbmQgLS0+IEV4dCR7aW5kZXh9XFxuYDtcbiAgfSk7XG4gIFxuICAvLyBBZGQgY2hhdGJvdCBpZiBwcmVzZW50XG4gIGlmIChzcGVjaWZpY2F0aW9uLmNoYXRib3RGbG93cyAmJiBzcGVjaWZpY2F0aW9uLmNoYXRib3RGbG93cy5sZW5ndGggPiAwKSB7XG4gICAgZGlhZ3JhbSArPSBgICAgIENoYXRib3Rb8J+kliBDaGF0Ym90XVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIEZyb250ZW5kIC0tPiBDaGF0Ym90XFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgQ2hhdGJvdCAtLT4gQmFja2VuZFxcbmA7XG4gIH1cbiAgXG4gIC8vIEFkZCBtb25pdG9yaW5nIGFuZCBsb2dnaW5nXG4gIGlmIChzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5pbmZyYXN0cnVjdHVyZT8ubW9uaXRvcmluZykge1xuICAgIGRpYWdyYW0gKz0gYCAgICBNb25pdG9yW/Cfk4ogTW9uaXRvcmluZzxici8+JHtzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5pbmZyYXN0cnVjdHVyZS5tb25pdG9yaW5nfV1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBCYWNrZW5kIC0uLT4gTW9uaXRvclxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIEZyb250ZW5kIC0uLT4gTW9uaXRvclxcbmA7XG4gIH1cbiAgXG4gIC8vIEFkZCBzdHlsaW5nXG4gIGRpYWdyYW0gKz0gYFxcbiAgICAlJSBTdHlsaW5nXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIGNsYXNzRGVmIGZyb250ZW5kIGZpbGw6I2UxZjVmZVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBjbGFzc0RlZiBiYWNrZW5kIGZpbGw6I2YzZTVmNVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBjbGFzc0RlZiBkYXRhYmFzZSBmaWxsOiNlOGY1ZThcXG5gO1xuICBkaWFncmFtICs9IGAgICAgY2xhc3NEZWYgZXh0ZXJuYWwgZmlsbDojZmZmM2UwXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIGNsYXNzRGVmIG1vbml0b3JpbmcgZmlsbDojZmNlNGVjXFxuYDtcbiAgXG4gIGRpYWdyYW0gKz0gYCAgICBjbGFzcyBGcm9udGVuZCBmcm9udGVuZFxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBjbGFzcyBCYWNrZW5kIGJhY2tlbmRcXG5gO1xuICBkaWFncmFtICs9IGAgICAgY2xhc3MgREIgZGF0YWJhc2VcXG5gO1xuICBkaWFncmFtICs9IGAgICAgY2xhc3MgTW9uaXRvciBtb25pdG9yaW5nXFxuYDtcbiAgXG4gIGV4dGVybmFsU2VydmljZXMuZm9yRWFjaCgoXywgaW5kZXgpID0+IHtcbiAgICBkaWFncmFtICs9IGAgICAgY2xhc3MgRXh0JHtpbmRleH0gZXh0ZXJuYWxcXG5gO1xuICB9KTtcbiAgXG4gIHJldHVybiBkaWFncmFtO1xufVxuXG5mdW5jdGlvbiBnZW5lcmF0ZUNvbXBvbmVudERpYWdyYW0oXG4gIHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbixcbiAgYXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXVxuKTogc3RyaW5nIHtcbiAgXG4gIGxldCBkaWFncmFtID0gYGdyYXBoIFREXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgICUlICR7c3BlY2lmaWNhdGlvbi5wcm9qZWN0TmFtZX0gLSBDb21wb25lbnQgQXJjaGl0ZWN0dXJlXFxuXFxuYDtcbiAgXG4gIC8vIEdyb3VwIGNvbXBvbmVudHMgYnkgdHlwZVxuICBjb25zdCBmcm9udGVuZENvbXBvbmVudHMgPSBzcGVjaWZpY2F0aW9uLmFyY2hpdGVjdHVyZS5jb21wb25lbnRzLmZpbHRlcihjID0+IGMudHlwZSA9PT0gJ2Zyb250ZW5kJyk7XG4gIGNvbnN0IGJhY2tlbmRDb21wb25lbnRzID0gc3BlY2lmaWNhdGlvbi5hcmNoaXRlY3R1cmUuY29tcG9uZW50cy5maWx0ZXIoYyA9PiBjLnR5cGUgPT09ICdiYWNrZW5kJyk7XG4gIGNvbnN0IGFwaUNvbXBvbmVudHMgPSBzcGVjaWZpY2F0aW9uLmFyY2hpdGVjdHVyZS5jb21wb25lbnRzLmZpbHRlcihjID0+IGMudHlwZSA9PT0gJ2FwaScpO1xuICBcbiAgLy8gQWRkIGZyb250ZW5kIGNvbXBvbmVudHNcbiAgaWYgKGZyb250ZW5kQ29tcG9uZW50cy5sZW5ndGggPiAwKSB7XG4gICAgZGlhZ3JhbSArPSBgICAgIHN1YmdyYXBoIEZyb250ZW5kW1wi8J+Wpe+4jyBGcm9udGVuZCBMYXllclwiXVxcbmA7XG4gICAgZnJvbnRlbmRDb21wb25lbnRzLmZvckVhY2goY29tcG9uZW50ID0+IHtcbiAgICAgIGRpYWdyYW0gKz0gYCAgICAgICAgJHtjb21wb25lbnQuaWR9WyR7Y29tcG9uZW50Lm5hbWV9XVxcbmA7XG4gICAgfSk7XG4gICAgZGlhZ3JhbSArPSBgICAgIGVuZFxcblxcbmA7XG4gIH1cbiAgXG4gIC8vIEFkZCBBUEkgY29tcG9uZW50c1xuICBpZiAoYXBpQ29tcG9uZW50cy5sZW5ndGggPiAwKSB7XG4gICAgZGlhZ3JhbSArPSBgICAgIHN1YmdyYXBoIEFQSVtcIvCfmqogQVBJIExheWVyXCJdXFxuYDtcbiAgICBhcGlDb21wb25lbnRzLmZvckVhY2goY29tcG9uZW50ID0+IHtcbiAgICAgIGRpYWdyYW0gKz0gYCAgICAgICAgJHtjb21wb25lbnQuaWR9WyR7Y29tcG9uZW50Lm5hbWV9XVxcbmA7XG4gICAgfSk7XG4gICAgZGlhZ3JhbSArPSBgICAgIGVuZFxcblxcbmA7XG4gIH1cbiAgXG4gIC8vIEFkZCBiYWNrZW5kIGNvbXBvbmVudHNcbiAgaWYgKGJhY2tlbmRDb21wb25lbnRzLmxlbmd0aCA+IDApIHtcbiAgICBkaWFncmFtICs9IGAgICAgc3ViZ3JhcGggQmFja2VuZFtcIuKame+4jyBCYWNrZW5kIExheWVyXCJdXFxuYDtcbiAgICBiYWNrZW5kQ29tcG9uZW50cy5mb3JFYWNoKGNvbXBvbmVudCA9PiB7XG4gICAgICBkaWFncmFtICs9IGAgICAgICAgICR7Y29tcG9uZW50LmlkfVske2NvbXBvbmVudC5uYW1lfV1cXG5gO1xuICAgIH0pO1xuICAgIGRpYWdyYW0gKz0gYCAgICBlbmRcXG5cXG5gO1xuICB9XG4gIFxuICAvLyBBZGQgcmVsYXRpb25zaGlwc1xuICBzcGVjaWZpY2F0aW9uLmFyY2hpdGVjdHVyZS5pbnRlZ3JhdGlvbnMuZm9yRWFjaChpbnRlZ3JhdGlvbiA9PiB7XG4gICAgZGlhZ3JhbSArPSBgICAgICR7aW50ZWdyYXRpb24uc291cmNlfSAtLT4gJHtpbnRlZ3JhdGlvbi50YXJnZXR9XFxuYDtcbiAgfSk7XG4gIFxuICByZXR1cm4gZGlhZ3JhbTtcbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVTZXF1ZW5jZURpYWdyYW0oXG4gIHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbixcbiAgYXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXVxuKTogc3RyaW5nIHtcbiAgXG4gIGxldCBkaWFncmFtID0gYHNlcXVlbmNlRGlhZ3JhbVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBwYXJ0aWNpcGFudCBVIGFzIPCfkaQgVXNlclxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBwYXJ0aWNpcGFudCBGIGFzIPCflqXvuI8gRnJvbnRlbmRcXG5gO1xuICBkaWFncmFtICs9IGAgICAgcGFydGljaXBhbnQgQSBhcyDwn5qqIEFQSVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBwYXJ0aWNpcGFudCBCIGFzIOKame+4jyBCYWNrZW5kXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIHBhcnRpY2lwYW50IEQgYXMg8J+XhO+4jyBEYXRhYmFzZVxcblxcbmA7XG4gIFxuICAvLyBHZW5lcmF0ZSBzZXF1ZW5jZSBmb3IgbWFpbiB1c2VyIGZsb3dzXG4gIGNvbnN0IG1haW5FbmRwb2ludHMgPSBzcGVjaWZpY2F0aW9uLmFwaUVuZHBvaW50cy5zbGljZSgwLCA1KTsgLy8gTGltaXQgdG8gZmlyc3QgNSBlbmRwb2ludHNcbiAgXG4gIG1haW5FbmRwb2ludHMuZm9yRWFjaChlbmRwb2ludCA9PiB7XG4gICAgY29uc3QgYWN0aW9uID0gZW5kcG9pbnQubWV0aG9kID09PSAnR0VUJyA/ICdSZXF1ZXN0JyA6ICdTdWJtaXQnO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gZW5kcG9pbnQubWV0aG9kID09PSAnR0VUJyA/ICdEYXRhJyA6ICdDb25maXJtYXRpb24nO1xuICAgIFxuICAgIGRpYWdyYW0gKz0gYCAgICBVLT4+RjogJHthY3Rpb259ICR7ZW5kcG9pbnQucGF0aH1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBGLT4+QTogJHtlbmRwb2ludC5tZXRob2R9ICR7ZW5kcG9pbnQucGF0aH1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBBLT4+QjogUHJvY2VzcyBSZXF1ZXN0XFxuYDtcbiAgICBcbiAgICBpZiAoZW5kcG9pbnQubWV0aG9kICE9PSAnR0VUJykge1xuICAgICAgZGlhZ3JhbSArPSBgICAgIEItPj5EOiBTdG9yZS9VcGRhdGUgRGF0YVxcbmA7XG4gICAgICBkaWFncmFtICs9IGAgICAgRC0tPj5COiBTdWNjZXNzXFxuYDtcbiAgICB9IGVsc2Uge1xuICAgICAgZGlhZ3JhbSArPSBgICAgIEItPj5EOiBRdWVyeSBEYXRhXFxuYDtcbiAgICAgIGRpYWdyYW0gKz0gYCAgICBELS0+PkI6IFJldHVybiBEYXRhXFxuYDtcbiAgICB9XG4gICAgXG4gICAgZGlhZ3JhbSArPSBgICAgIEItLT4+QTogJHtyZXNwb25zZX1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBBLS0+PkY6ICR7cmVzcG9uc2V9XFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgRi0tPj5VOiBEaXNwbGF5ICR7cmVzcG9uc2V9XFxuXFxuYDtcbiAgfSk7XG4gIFxuICByZXR1cm4gZGlhZ3JhbTtcbn1cblxuZnVuY3Rpb24gZ2VuZXJhdGVEYXRhYmFzZURpYWdyYW0oc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uKTogc3RyaW5nIHtcbiAgbGV0IGRpYWdyYW0gPSBgZXJEaWFncmFtXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgICUlICR7c3BlY2lmaWNhdGlvbi5wcm9qZWN0TmFtZX0gLSBEYXRhYmFzZSBTY2hlbWFcXG5cXG5gO1xuICBcbiAgc3BlY2lmaWNhdGlvbi5kYXRhTW9kZWxzLmZvckVhY2gobW9kZWwgPT4ge1xuICAgIGRpYWdyYW0gKz0gYCAgICAke21vZGVsLm5hbWV9IHtcXG5gO1xuICAgIFxuICAgIG1vZGVsLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHtcbiAgICAgIGNvbnN0IHR5cGUgPSBtYXBGaWVsZFR5cGVUb0VSRChmaWVsZC50eXBlKTtcbiAgICAgIGNvbnN0IGNvbnN0cmFpbnQgPSBmaWVsZC5yZXF1aXJlZCA/ICdOT1QgTlVMTCcgOiAnJztcbiAgICAgIGRpYWdyYW0gKz0gYCAgICAgICAgJHt0eXBlfSAke2ZpZWxkLm5hbWV9ICR7Y29uc3RyYWludH1cXG5gO1xuICAgIH0pO1xuICAgIFxuICAgIGRpYWdyYW0gKz0gYCAgICB9XFxuXFxuYDtcbiAgfSk7XG4gIFxuICAvLyBBZGQgcmVsYXRpb25zaGlwc1xuICBzcGVjaWZpY2F0aW9uLmRhdGFNb2RlbHMuZm9yRWFjaChtb2RlbCA9PiB7XG4gICAgbW9kZWwucmVsYXRpb25zaGlwcy5mb3JFYWNoKHJlbCA9PiB7XG4gICAgICBjb25zdCByZWxhdGlvbnNoaXBUeXBlID0gbWFwUmVsYXRpb25zaGlwVHlwZShyZWwudHlwZSk7XG4gICAgICBkaWFncmFtICs9IGAgICAgJHttb2RlbC5uYW1lfSAke3JlbGF0aW9uc2hpcFR5cGV9ICR7cmVsLnRhcmdldH0gOiBcIiR7cmVsLnR5cGV9XCJcXG5gO1xuICAgIH0pO1xuICB9KTtcbiAgXG4gIHJldHVybiBkaWFncmFtO1xufVxuXG5mdW5jdGlvbiBleHRyYWN0RXh0ZXJuYWxTZXJ2aWNlcyhcbiAgc3BlY2lmaWNhdGlvbjogRGV0YWlsZWRTcGVjaWZpY2F0aW9uLFxuICBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdXG4pOiBzdHJpbmdbXSB7XG4gIGNvbnN0IHNlcnZpY2VzID0gbmV3IFNldDxzdHJpbmc+KCk7XG4gIFxuICAvLyBDaGVjayBmb3IgY29tbW9uIGV4dGVybmFsIHNlcnZpY2VzIGluIHRoZSBzcGVjaWZpY2F0aW9uXG4gIGlmIChzcGVjaWZpY2F0aW9uLnRlY2hTdGFjay5iYWNrZW5kPy5hdXRoZW50aWNhdGlvbiA9PT0gJ09BdXRoJykge1xuICAgIHNlcnZpY2VzLmFkZCgnT0F1dGggUHJvdmlkZXInKTtcbiAgfVxuICBcbiAgLy8gQ2hlY2sgYXJ0aWZhY3RzIGZvciBleHRlcm5hbCBzZXJ2aWNlIHVzYWdlXG4gIGFydGlmYWN0cy5mb3JFYWNoKGFydGlmYWN0ID0+IHtcbiAgICBhcnRpZmFjdC5maWxlcy5mb3JFYWNoKGZpbGUgPT4ge1xuICAgICAgY29uc3QgY29udGVudCA9IGZpbGUuY29udGVudC50b0xvd2VyQ2FzZSgpO1xuICAgICAgXG4gICAgICAvLyBDaGVjayBmb3IgY29tbW9uIGV4dGVybmFsIHNlcnZpY2VzXG4gICAgICBpZiAoY29udGVudC5pbmNsdWRlcygnc3RyaXBlJykpIHNlcnZpY2VzLmFkZCgnU3RyaXBlJyk7XG4gICAgICBpZiAoY29udGVudC5pbmNsdWRlcygnc2VuZGdyaWQnKSB8fCBjb250ZW50LmluY2x1ZGVzKCdtYWlsZ3VuJykpIHNlcnZpY2VzLmFkZCgnRW1haWwgU2VydmljZScpO1xuICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJ3R3aWxpbycpKSBzZXJ2aWNlcy5hZGQoJ1NNUyBTZXJ2aWNlJyk7XG4gICAgICBpZiAoY29udGVudC5pbmNsdWRlcygnYXdzLXNkaycpKSBzZXJ2aWNlcy5hZGQoJ0FXUyBTZXJ2aWNlcycpO1xuICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJ2ZpcmViYXNlJykpIHNlcnZpY2VzLmFkZCgnRmlyZWJhc2UnKTtcbiAgICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCdyZWRpcycpKSBzZXJ2aWNlcy5hZGQoJ1JlZGlzIENhY2hlJyk7XG4gICAgICBpZiAoY29udGVudC5pbmNsdWRlcygnZWxhc3RpY3NlYXJjaCcpKSBzZXJ2aWNlcy5hZGQoJ0VsYXN0aWNzZWFyY2gnKTtcbiAgICB9KTtcbiAgfSk7XG4gIFxuICByZXR1cm4gQXJyYXkuZnJvbShzZXJ2aWNlcyk7XG59XG5cbmZ1bmN0aW9uIG1hcEZpZWxkVHlwZVRvRVJEKGZpZWxkVHlwZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgdHlwZU1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAnc3RyaW5nJzogJ3ZhcmNoYXInLFxuICAgICdudW1iZXInOiAnaW50JyxcbiAgICAnYm9vbGVhbic6ICdib29sZWFuJyxcbiAgICAnZGF0ZSc6ICdkYXRldGltZScsXG4gICAgJ2FycmF5JzogJ2pzb24nLFxuICAgICdvYmplY3QnOiAnanNvbicsXG4gIH07XG4gIFxuICByZXR1cm4gdHlwZU1hcFtmaWVsZFR5cGUudG9Mb3dlckNhc2UoKV0gfHwgJ3ZhcmNoYXInO1xufVxuXG5mdW5jdGlvbiBtYXBSZWxhdGlvbnNoaXBUeXBlKHJlbFR5cGU6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IHJlbGF0aW9uc2hpcE1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAnb25lVG9PbmUnOiAnfHwtLXx8JyxcbiAgICAnb25lVG9NYW55JzogJ3x8LS1veycsXG4gICAgJ21hbnlUb01hbnknOiAnfW8tLW97JyxcbiAgfTtcbiAgXG4gIHJldHVybiByZWxhdGlvbnNoaXBNYXBbcmVsVHlwZV0gfHwgJ3x8LS1veyc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUZsb3djaGFydERpYWdyYW0oXG4gIHNwZWNpZmljYXRpb246IERldGFpbGVkU3BlY2lmaWNhdGlvbixcbiAgYXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXVxuKTogc3RyaW5nIHtcbiAgbGV0IGRpYWdyYW0gPSBgZmxvd2NoYXJ0IFREXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgICUlICR7c3BlY2lmaWNhdGlvbi5wcm9qZWN0TmFtZX0gLSBVc2VyIEZsb3dcXG5cXG5gO1xuICBcbiAgZGlhZ3JhbSArPSBgICAgIFN0YXJ0KFvwn5GkIFVzZXIgU3RhcnRzXSlcXG5gO1xuICBkaWFncmFtICs9IGAgICAgQXV0aHvwn5SQIEF1dGhlbnRpY2F0ZWQ/fVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBMb2dpblvwn5OdIExvZ2luXVxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBEYXNoYm9hcmRb8J+TiiBEYXNoYm9hcmRdXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIEZlYXR1cmVzW+KaoSBGZWF0dXJlc11cXG5gO1xuICBkaWFncmFtICs9IGAgICAgRW5kKFvinIUgQ29tcGxldGVdKVxcblxcbmA7XG4gIFxuICBkaWFncmFtICs9IGAgICAgU3RhcnQgLS0+IEF1dGhcXG5gO1xuICBkaWFncmFtICs9IGAgICAgQXV0aCAtLT58Tm98IExvZ2luXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIEF1dGggLS0+fFllc3wgRGFzaGJvYXJkXFxuYDtcbiAgZGlhZ3JhbSArPSBgICAgIExvZ2luIC0tPiBEYXNoYm9hcmRcXG5gO1xuICBkaWFncmFtICs9IGAgICAgRGFzaGJvYXJkIC0tPiBGZWF0dXJlc1xcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICBGZWF0dXJlcyAtLT4gRW5kXFxuYDtcbiAgXG4gIHJldHVybiBkaWFncmFtO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVEZXBsb3ltZW50RGlhZ3JhbShzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24pOiBzdHJpbmcge1xuICBsZXQgZGlhZ3JhbSA9IGBncmFwaCBUQlxcbmA7XG4gIGRpYWdyYW0gKz0gYCAgICAlJSAke3NwZWNpZmljYXRpb24ucHJvamVjdE5hbWV9IC0gRGVwbG95bWVudCBBcmNoaXRlY3R1cmVcXG5cXG5gO1xuICBcbiAgaWYgKHNwZWNpZmljYXRpb24uYXJjaGl0ZWN0dXJlLnR5cGUgPT09ICdzZXJ2ZXJsZXNzJykge1xuICAgIGRpYWdyYW0gKz0gYCAgICBzdWJncmFwaCBBV1NbXCLimIHvuI8gQVdTIENsb3VkXCJdXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgICAgIENGW/Cfk6YgQ2xvdWRGcm9udF1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICAgICAgUzNb8J+qoyBTMyBCdWNrZXRdXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgICAgIEFQSVvwn5qqIEFQSSBHYXRld2F5XVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgICAgICBMYW1iZGFb4pqhIExhbWJkYSBGdW5jdGlvbnNdXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgICAgIFJEU1so8J+XhO+4jyBSRFMgRGF0YWJhc2UpXVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgICAgICBDV1vwn5OKIENsb3VkV2F0Y2hdXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgZW5kXFxuXFxuYDtcbiAgICBcbiAgICBkaWFncmFtICs9IGAgICAgVXNlclvwn5GkIFVzZXJdIC0tPiBDRlxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIENGIC0tPiBTM1xcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIENGIC0tPiBBUElcXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBBUEkgLS0+IExhbWJkYVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIExhbWJkYSAtLT4gUkRTXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgTGFtYmRhIC0uLT4gQ1dcXG5gO1xuICB9IGVsc2Uge1xuICAgIGRpYWdyYW0gKz0gYCAgICBzdWJncmFwaCBDbG91ZFtcIuKYge+4jyBDbG91ZCBJbmZyYXN0cnVjdHVyZVwiXVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgICAgICBMQlvimpbvuI8gTG9hZCBCYWxhbmNlcl1cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICAgICAgQXBwW/CflqXvuI8gQXBwbGljYXRpb24gU2VydmVyc11cXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICAgICAgREJbKPCfl4TvuI8gRGF0YWJhc2UpXVxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgICAgICBDYWNoZVvimqEgQ2FjaGVdXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgZW5kXFxuXFxuYDtcbiAgICBcbiAgICBkaWFncmFtICs9IGAgICAgVXNlclvwn5GkIFVzZXJdIC0tPiBMQlxcbmA7XG4gICAgZGlhZ3JhbSArPSBgICAgIExCIC0tPiBBcHBcXG5gO1xuICAgIGRpYWdyYW0gKz0gYCAgICBBcHAgLS0+IERCXFxuYDtcbiAgICBkaWFncmFtICs9IGAgICAgQXBwIC0tPiBDYWNoZVxcbmA7XG4gIH1cbiAgXG4gIHJldHVybiBkaWFncmFtO1xufVxuIl19