"use strict";
/**
 * Review & Refine Agent
 * Analyzes code, finds errors, and suggests improvements
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const static_analysis_1 = require("../../utils/static-analysis");
const security_scanner_1 = require("../../utils/security-scanner");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Review & Refine Agent started', { requestId, event });
    try {
        const { sessionId, specification, taskPlan, artifacts, iteration } = event;
        // Fetch all artifacts from S3
        const allArtifacts = await fetchAllArtifacts(sessionId, artifacts);
        // Perform comprehensive review
        const reviewResults = [];
        for (const artifact of allArtifacts) {
            const review = await performComprehensiveReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId);
            reviewResults.push(review);
            // Save review record to DynamoDB
            const reviewRecord = (0, schemas_1.createReviewRecord)(sessionId, artifact.taskId, review, 'ReviewRefineAgent');
            await dynamoClient.send(new lib_dynamodb_1.PutCommand({
                TableName: TABLE_NAME,
                Item: reviewRecord,
            }));
        }
        // Perform cross-artifact analysis
        const crossArtifactReview = await performCrossArtifactAnalysis(allArtifacts, specification, taskPlan, requestId);
        if (crossArtifactReview) {
            reviewResults.push(crossArtifactReview);
            const crossReviewRecord = (0, schemas_1.createReviewRecord)(sessionId, 'cross-artifact-analysis', crossArtifactReview, 'ReviewRefineAgent');
            await dynamoClient.send(new lib_dynamodb_1.PutCommand({
                TableName: TABLE_NAME,
                Item: crossReviewRecord,
            }));
        }
        logger_1.logger.info('Review & Refine completed', {
            requestId,
            sessionId,
            reviewsCount: reviewResults.length,
            totalErrors: reviewResults.reduce((sum, r) => sum + r.errors.length, 0),
            totalSuggestions: reviewResults.reduce((sum, r) => sum + r.suggestions.length, 0)
        });
        return reviewResults;
    }
    catch (error) {
        logger_1.logger.error('Review & Refine Agent failed', { requestId, error });
        throw error;
    }
};
exports.handler = handler;
async function fetchAllArtifacts(sessionId, artifactReferences) {
    const artifacts = [];
    // Query DynamoDB for all artifacts in this session
    const queryResult = await dynamoClient.send(new lib_dynamodb_1.QueryCommand({
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `SESSION#${sessionId}`,
            ':sk': 'ARTIFACT#',
        },
    }));
    // Fetch artifact details from S3
    for (const item of queryResult.Items || []) {
        try {
            const s3Response = await s3Client.send(new client_s3_1.GetObjectCommand({
                Bucket: ARTIFACTS_BUCKET,
                Key: item.s3Key,
            }));
            const artifactData = JSON.parse(await s3Response.Body?.transformToString() || '{}');
            artifacts.push(artifactData);
        }
        catch (error) {
            logger_1.logger.error('Failed to fetch artifact from S3', { s3Key: item.s3Key, error });
        }
    }
    return artifacts;
}
async function performComprehensiveReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId) {
    // Run static analysis
    const staticAnalysisResults = await (0, static_analysis_1.runStaticAnalysis)(artifact.files);
    // Run security scan
    const securityResults = await (0, security_scanner_1.runSecurityScan)(artifact.files);
    // Perform AI-powered code review
    const aiReviewResults = await performAICodeReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId);
    // Combine all results
    const review = {
        taskId: artifact.taskId,
        overallQuality: determineOverallQuality(staticAnalysisResults.errors, securityResults.issues, aiReviewResults.errors),
        errors: [
            ...staticAnalysisResults.errors,
            ...securityResults.errors,
            ...aiReviewResults.errors,
        ],
        suggestions: [
            ...staticAnalysisResults.suggestions,
            ...aiReviewResults.suggestions,
        ],
        securityIssues: securityResults.issues,
        performanceIssues: aiReviewResults.performanceIssues,
        updatedSpecDelta: aiReviewResults.updatedSpecDelta,
        updatedTasks: aiReviewResults.updatedTasks,
    };
    return review;
}
async function performAICodeReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId) {
    const reviewPrompt = `
You are an expert code reviewer and software architect. Your task is to perform a comprehensive review of the generated code and provide detailed feedback.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

TASK PLAN:
${JSON.stringify(taskPlan, null, 2)}

CURRENT ARTIFACT:
${JSON.stringify(artifact, null, 2)}

ALL ARTIFACTS CONTEXT:
${JSON.stringify(allArtifacts.map(a => ({ taskId: a.taskId, fileCount: a.files.length, errors: a.errors.length })), null, 2)}

ITERATION: ${iteration}

Please perform a thorough review focusing on:

1. CODE QUALITY:
   - Adherence to best practices and design patterns
   - Code readability and maintainability
   - Proper error handling and validation
   - Type safety and null checks
   - Documentation and comments

2. ARCHITECTURE & DESIGN:
   - Consistency with the overall specification
   - Proper separation of concerns
   - Scalability and extensibility
   - Integration with other components

3. SECURITY:
   - Input validation and sanitization
   - Authentication and authorization
   - Protection against common vulnerabilities
   - Secure data handling

4. PERFORMANCE:
   - Efficient algorithms and data structures
   - Proper caching strategies
   - Database query optimization
   - Resource usage optimization

5. TESTING:
   - Test coverage and quality
   - Edge case handling
   - Integration test scenarios

6. DEPLOYMENT & OPERATIONS:
   - Configuration management
   - Monitoring and logging
   - Error tracking and alerting

Based on your review, provide:

1. ERRORS: Critical issues that must be fixed
2. SUGGESTIONS: Improvements and optimizations
3. PERFORMANCE ISSUES: Performance-related concerns
4. UPDATED SPECIFICATION: Any changes needed to the original specification
5. UPDATED TASKS: New or modified tasks based on your findings

Return your response as a JSON object with the following structure:
{
  "errors": [
    {
      "severity": "critical|major|minor",
      "message": "string",
      "file": "string",
      "line": number,
      "rule": "string"
    }
  ],
  "suggestions": [
    {
      "type": "improvement|optimization|refactor|feature",
      "description": "string",
      "impact": "high|medium|low",
      "effort": "high|medium|low"
    }
  ],
  "performanceIssues": [
    {
      "type": "memory|cpu|network|database",
      "description": "string",
      "impact": "high|medium|low",
      "recommendation": "string"
    }
  ],
  "updatedSpecDelta": {
    // Only include fields that need to be updated
  },
  "updatedTasks": [
    // New or modified tasks
  ]
}

Be thorough but constructive in your feedback. Focus on actionable improvements.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(reviewPrompt, {
            temperature: 0.2,
            maxTokens: 6000,
            requestId,
        });
        const result = JSON.parse(response);
        return {
            errors: result.errors || [],
            suggestions: result.suggestions || [],
            performanceIssues: result.performanceIssues || [],
            updatedSpecDelta: result.updatedSpecDelta,
            updatedTasks: result.updatedTasks,
        };
    }
    catch (error) {
        logger_1.logger.error('AI code review failed', { requestId, taskId: artifact.taskId, error });
        return {
            errors: [{
                    severity: 'major',
                    message: `AI code review failed: ${error instanceof Error ? error.message : String(error)}`,
                    rule: 'review_error',
                }],
            suggestions: [],
            performanceIssues: [],
        };
    }
}
async function performCrossArtifactAnalysis(allArtifacts, specification, taskPlan, requestId) {
    if (allArtifacts.length < 2) {
        return null; // No cross-artifact analysis needed for single artifact
    }
    const crossAnalysisPrompt = `
You are performing a cross-artifact analysis to ensure consistency and integration across all generated code components.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ALL ARTIFACTS:
${JSON.stringify(allArtifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
        errors: a.errors,
    })), null, 2)}

Analyze the artifacts for:

1. INTEGRATION CONSISTENCY:
   - API contracts between frontend and backend
   - Data model consistency across components
   - Import/export compatibility
   - Configuration alignment

2. ARCHITECTURAL COHERENCE:
   - Consistent design patterns
   - Proper dependency management
   - Service boundaries and interfaces
   - Data flow consistency

3. CROSS-CUTTING CONCERNS:
   - Error handling strategies
   - Logging and monitoring
   - Security implementations
   - Performance optimizations

Provide feedback on integration issues and overall system coherence.

Return your response as a JSON object following the same structure as individual artifact reviews.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(crossAnalysisPrompt, {
            temperature: 0.2,
            maxTokens: 4000,
            requestId,
        });
        const result = JSON.parse(response);
        return {
            taskId: 'cross-artifact-analysis',
            overallQuality: result.overallQuality || 'good',
            errors: result.errors || [],
            suggestions: result.suggestions || [],
            securityIssues: result.securityIssues || [],
            performanceIssues: result.performanceIssues || [],
            updatedSpecDelta: result.updatedSpecDelta,
            updatedTasks: result.updatedTasks,
        };
    }
    catch (error) {
        logger_1.logger.error('Cross-artifact analysis failed', { requestId, error });
        return null;
    }
}
function determineOverallQuality(staticErrors, securityIssues, aiErrors) {
    const criticalIssues = [
        ...staticErrors.filter(e => e.severity === 'critical'),
        ...securityIssues.filter(s => s.severity === 'critical'),
        ...aiErrors.filter(e => e.severity === 'critical'),
    ].length;
    const majorIssues = [
        ...staticErrors.filter(e => e.severity === 'major'),
        ...securityIssues.filter(s => s.severity === 'high'),
        ...aiErrors.filter(e => e.severity === 'major'),
    ].length;
    if (criticalIssues > 0)
        return 'poor';
    if (majorIssues > 3)
        return 'fair';
    if (majorIssues > 0)
        return 'good';
    return 'excellent';
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaGFuZGxlci5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uL3NyYy9hZ2VudHMvcmV2aWV3LXJlZmluZS9oYW5kbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7O0dBR0c7OztBQUdILDhEQUEwRDtBQUMxRCx3REFBeUY7QUFDekYsa0RBQWdFO0FBYWhFLG9EQUE0RDtBQUM1RCx1REFBbUQ7QUFDbkQsK0NBQTRDO0FBQzVDLGlFQUFnRTtBQUNoRSxtRUFBK0Q7QUFFL0QsTUFBTSxZQUFZLEdBQUcscUNBQXNCLENBQUMsSUFBSSxDQUFDLElBQUksZ0NBQWMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0FBQ3pFLE1BQU0sUUFBUSxHQUFHLElBQUksb0JBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztBQUNsQyxNQUFNLFVBQVUsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLG1CQUFvQixDQUFDO0FBQ3BELE1BQU0sZ0JBQWdCLEdBQUcsT0FBTyxDQUFDLEdBQUcsQ0FBQyxxQkFBc0IsQ0FBQztBQUVyRCxNQUFNLE9BQU8sR0FBRyxLQUFLLEVBQzFCLEtBQXdCLEVBQ3hCLE9BQWdCLEVBQ1MsRUFBRTtJQUMzQixNQUFNLFNBQVMsR0FBRyxPQUFPLENBQUMsWUFBWSxDQUFDO0lBQ3ZDLGVBQU0sQ0FBQyxJQUFJLENBQUMsK0JBQStCLEVBQUUsRUFBRSxTQUFTLEVBQUUsS0FBSyxFQUFFLENBQUMsQ0FBQztJQUVuRSxJQUFJLENBQUM7UUFDSCxNQUFNLEVBQUUsU0FBUyxFQUFFLGFBQWEsRUFBRSxRQUFRLEVBQUUsU0FBUyxFQUFFLFNBQVMsRUFBRSxHQUFHLEtBQUssQ0FBQztRQUUzRSw4QkFBOEI7UUFDOUIsTUFBTSxZQUFZLEdBQUcsTUFBTSxpQkFBaUIsQ0FBQyxTQUFTLEVBQUUsU0FBUyxDQUFDLENBQUM7UUFFbkUsK0JBQStCO1FBQy9CLE1BQU0sYUFBYSxHQUFtQixFQUFFLENBQUM7UUFFekMsS0FBSyxNQUFNLFFBQVEsSUFBSSxZQUFZLEVBQUUsQ0FBQztZQUNwQyxNQUFNLE1BQU0sR0FBRyxNQUFNLDBCQUEwQixDQUM3QyxRQUFRLEVBQ1IsYUFBYSxFQUNiLFFBQVEsRUFDUixZQUFZLEVBQ1osU0FBUyxFQUNULFNBQVMsQ0FDVixDQUFDO1lBRUYsYUFBYSxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUUzQixpQ0FBaUM7WUFDakMsTUFBTSxZQUFZLEdBQUcsSUFBQSw0QkFBa0IsRUFDckMsU0FBUyxFQUNULFFBQVEsQ0FBQyxNQUFNLEVBQ2YsTUFBTSxFQUNOLG1CQUFtQixDQUNwQixDQUFDO1lBQ0YsTUFBTSxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUkseUJBQVUsQ0FBQztnQkFDckMsU0FBUyxFQUFFLFVBQVU7Z0JBQ3JCLElBQUksRUFBRSxZQUFZO2FBQ25CLENBQUMsQ0FBQyxDQUFDO1FBQ04sQ0FBQztRQUVELGtDQUFrQztRQUNsQyxNQUFNLG1CQUFtQixHQUFHLE1BQU0sNEJBQTRCLENBQzVELFlBQVksRUFDWixhQUFhLEVBQ2IsUUFBUSxFQUNSLFNBQVMsQ0FDVixDQUFDO1FBRUYsSUFBSSxtQkFBbUIsRUFBRSxDQUFDO1lBQ3hCLGFBQWEsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQztZQUV4QyxNQUFNLGlCQUFpQixHQUFHLElBQUEsNEJBQWtCLEVBQzFDLFNBQVMsRUFDVCx5QkFBeUIsRUFDekIsbUJBQW1CLEVBQ25CLG1CQUFtQixDQUNwQixDQUFDO1lBQ0YsTUFBTSxZQUFZLENBQUMsSUFBSSxDQUFDLElBQUkseUJBQVUsQ0FBQztnQkFDckMsU0FBUyxFQUFFLFVBQVU7Z0JBQ3JCLElBQUksRUFBRSxpQkFBaUI7YUFDeEIsQ0FBQyxDQUFDLENBQUM7UUFDTixDQUFDO1FBRUQsZUFBTSxDQUFDLElBQUksQ0FBQywyQkFBMkIsRUFBRTtZQUN2QyxTQUFTO1lBQ1QsU0FBUztZQUNULFlBQVksRUFBRSxhQUFhLENBQUMsTUFBTTtZQUNsQyxXQUFXLEVBQUUsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7WUFDdkUsZ0JBQWdCLEVBQUUsYUFBYSxDQUFDLE1BQU0sQ0FBQyxDQUFDLEdBQUcsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDLENBQUMsV0FBVyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7U0FDbEYsQ0FBQyxDQUFDO1FBRUgsT0FBTyxhQUFhLENBQUM7SUFFdkIsQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixlQUFNLENBQUMsS0FBSyxDQUFDLDhCQUE4QixFQUFFLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7UUFDbkUsTUFBTSxLQUFLLENBQUM7SUFDZCxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBOUVXLFFBQUEsT0FBTyxXQThFbEI7QUFFRixLQUFLLFVBQVUsaUJBQWlCLENBQzlCLFNBQWlCLEVBQ2pCLGtCQUFrQztJQUdsQyxNQUFNLFNBQVMsR0FBbUIsRUFBRSxDQUFDO0lBRXJDLG1EQUFtRDtJQUNuRCxNQUFNLFdBQVcsR0FBRyxNQUFNLFlBQVksQ0FBQyxJQUFJLENBQUMsSUFBSSwyQkFBWSxDQUFDO1FBQzNELFNBQVMsRUFBRSxVQUFVO1FBQ3JCLHNCQUFzQixFQUFFLG1DQUFtQztRQUMzRCx5QkFBeUIsRUFBRTtZQUN6QixLQUFLLEVBQUUsV0FBVyxTQUFTLEVBQUU7WUFDN0IsS0FBSyxFQUFFLFdBQVc7U0FDbkI7S0FDRixDQUFDLENBQUMsQ0FBQztJQUVKLGlDQUFpQztJQUNqQyxLQUFLLE1BQU0sSUFBSSxJQUFJLFdBQVcsQ0FBQyxLQUFLLElBQUksRUFBRSxFQUFFLENBQUM7UUFDM0MsSUFBSSxDQUFDO1lBQ0gsTUFBTSxVQUFVLEdBQUcsTUFBTSxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUksNEJBQWdCLENBQUM7Z0JBQzFELE1BQU0sRUFBRSxnQkFBZ0I7Z0JBQ3hCLEdBQUcsRUFBRSxJQUFJLENBQUMsS0FBSzthQUNoQixDQUFDLENBQUMsQ0FBQztZQUVKLE1BQU0sWUFBWSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsTUFBTSxVQUFVLENBQUMsSUFBSSxFQUFFLGlCQUFpQixFQUFFLElBQUksSUFBSSxDQUFDLENBQUM7WUFDcEYsU0FBUyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztRQUMvQixDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLGVBQU0sQ0FBQyxLQUFLLENBQUMsa0NBQWtDLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxDQUFDLEtBQUssRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQ2pGLENBQUM7SUFDSCxDQUFDO0lBRUQsT0FBTyxTQUFTLENBQUM7QUFDbkIsQ0FBQztBQUVELEtBQUssVUFBVSwwQkFBMEIsQ0FDdkMsUUFBc0IsRUFDdEIsYUFBb0MsRUFDcEMsUUFBa0IsRUFDbEIsWUFBNEIsRUFDNUIsU0FBaUIsRUFDakIsU0FBaUI7SUFHakIsc0JBQXNCO0lBQ3RCLE1BQU0scUJBQXFCLEdBQUcsTUFBTSxJQUFBLG1DQUFpQixFQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUV0RSxvQkFBb0I7SUFDcEIsTUFBTSxlQUFlLEdBQUcsTUFBTSxJQUFBLGtDQUFlLEVBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDO0lBRTlELGlDQUFpQztJQUNqQyxNQUFNLGVBQWUsR0FBRyxNQUFNLG1CQUFtQixDQUMvQyxRQUFRLEVBQ1IsYUFBYSxFQUNiLFFBQVEsRUFDUixZQUFZLEVBQ1osU0FBUyxFQUNULFNBQVMsQ0FDVixDQUFDO0lBRUYsc0JBQXNCO0lBQ3RCLE1BQU0sTUFBTSxHQUFpQjtRQUMzQixNQUFNLEVBQUUsUUFBUSxDQUFDLE1BQU07UUFDdkIsY0FBYyxFQUFFLHVCQUF1QixDQUNyQyxxQkFBcUIsQ0FBQyxNQUFNLEVBQzVCLGVBQWUsQ0FBQyxNQUFNLEVBQ3RCLGVBQWUsQ0FBQyxNQUFNLENBQ3ZCO1FBQ0QsTUFBTSxFQUFFO1lBQ04sR0FBRyxxQkFBcUIsQ0FBQyxNQUFNO1lBQy9CLEdBQUcsZUFBZSxDQUFDLE1BQU07WUFDekIsR0FBRyxlQUFlLENBQUMsTUFBTTtTQUMxQjtRQUNELFdBQVcsRUFBRTtZQUNYLEdBQUcscUJBQXFCLENBQUMsV0FBVztZQUNwQyxHQUFHLGVBQWUsQ0FBQyxXQUFXO1NBQy9CO1FBQ0QsY0FBYyxFQUFFLGVBQWUsQ0FBQyxNQUFNO1FBQ3RDLGlCQUFpQixFQUFFLGVBQWUsQ0FBQyxpQkFBaUI7UUFDcEQsZ0JBQWdCLEVBQUUsZUFBZSxDQUFDLGdCQUFnQjtRQUNsRCxZQUFZLEVBQUUsZUFBZSxDQUFDLFlBQVk7S0FDM0MsQ0FBQztJQUVGLE9BQU8sTUFBTSxDQUFDO0FBQ2hCLENBQUM7QUFFRCxLQUFLLFVBQVUsbUJBQW1CLENBQ2hDLFFBQXNCLEVBQ3RCLGFBQW9DLEVBQ3BDLFFBQWtCLEVBQ2xCLFlBQTRCLEVBQzVCLFNBQWlCLEVBQ2pCLFNBQWlCO0lBU2pCLE1BQU0sWUFBWSxHQUFHOzs7O0VBSXJCLElBQUksQ0FBQyxTQUFTLENBQUMsYUFBYSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7OztFQUd0QyxJQUFJLENBQUMsU0FBUyxDQUFDLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDOzs7RUFHakMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQzs7O0VBR2pDLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxNQUFNLEVBQUUsQ0FBQyxDQUFDLE1BQU0sRUFBRSxTQUFTLEVBQUUsQ0FBQyxDQUFDLEtBQUssQ0FBQyxNQUFNLEVBQUUsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7O2FBRS9HLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBbUZyQixDQUFDO0lBRUEsSUFBSSxDQUFDO1FBQ0gsTUFBTSxRQUFRLEdBQUcsTUFBTSxJQUFBLHNCQUFTLEVBQUMsWUFBWSxFQUFFO1lBQzdDLFdBQVcsRUFBRSxHQUFHO1lBQ2hCLFNBQVMsRUFBRSxJQUFJO1lBQ2YsU0FBUztTQUNWLENBQUMsQ0FBQztRQUVILE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUM7UUFFcEMsT0FBTztZQUNMLE1BQU0sRUFBRSxNQUFNLENBQUMsTUFBTSxJQUFJLEVBQUU7WUFDM0IsV0FBVyxFQUFFLE1BQU0sQ0FBQyxXQUFXLElBQUksRUFBRTtZQUNyQyxpQkFBaUIsRUFBRSxNQUFNLENBQUMsaUJBQWlCLElBQUksRUFBRTtZQUNqRCxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsZ0JBQWdCO1lBQ3pDLFlBQVksRUFBRSxNQUFNLENBQUMsWUFBWTtTQUNsQyxDQUFDO0lBRUosQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixlQUFNLENBQUMsS0FBSyxDQUFDLHVCQUF1QixFQUFFLEVBQUUsU0FBUyxFQUFFLE1BQU0sRUFBRSxRQUFRLENBQUMsTUFBTSxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7UUFFckYsT0FBTztZQUNMLE1BQU0sRUFBRSxDQUFDO29CQUNQLFFBQVEsRUFBRSxPQUFPO29CQUNqQixPQUFPLEVBQUUsMEJBQTBCLEtBQUssWUFBWSxLQUFLLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRTtvQkFDM0YsSUFBSSxFQUFFLGNBQWM7aUJBQ3JCLENBQUM7WUFDRixXQUFXLEVBQUUsRUFBRTtZQUNmLGlCQUFpQixFQUFFLEVBQUU7U0FDdEIsQ0FBQztJQUNKLENBQUM7QUFDSCxDQUFDO0FBRUQsS0FBSyxVQUFVLDRCQUE0QixDQUN6QyxZQUE0QixFQUM1QixhQUFvQyxFQUNwQyxRQUFrQixFQUNsQixTQUFpQjtJQUdqQixJQUFJLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDNUIsT0FBTyxJQUFJLENBQUMsQ0FBQyx3REFBd0Q7SUFDdkUsQ0FBQztJQUVELE1BQU0sbUJBQW1CLEdBQUc7Ozs7RUFJNUIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxhQUFhLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQzs7O0VBR3RDLElBQUksQ0FBQyxTQUFTLENBQUMsWUFBWSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDdEMsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNO1FBQ2hCLEtBQUssRUFBRSxDQUFDLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLElBQUksRUFBRSxRQUFRLEVBQUUsQ0FBQyxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDL0UsTUFBTSxFQUFFLENBQUMsQ0FBQyxNQUFNO0tBQ2pCLENBQUMsQ0FBQyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0F5QlosQ0FBQztJQUVBLElBQUksQ0FBQztRQUNILE1BQU0sUUFBUSxHQUFHLE1BQU0sSUFBQSxzQkFBUyxFQUFDLG1CQUFtQixFQUFFO1lBQ3BELFdBQVcsRUFBRSxHQUFHO1lBQ2hCLFNBQVMsRUFBRSxJQUFJO1lBQ2YsU0FBUztTQUNWLENBQUMsQ0FBQztRQUVILE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUM7UUFFcEMsT0FBTztZQUNMLE1BQU0sRUFBRSx5QkFBeUI7WUFDakMsY0FBYyxFQUFFLE1BQU0sQ0FBQyxjQUFjLElBQUksTUFBTTtZQUMvQyxNQUFNLEVBQUUsTUFBTSxDQUFDLE1BQU0sSUFBSSxFQUFFO1lBQzNCLFdBQVcsRUFBRSxNQUFNLENBQUMsV0FBVyxJQUFJLEVBQUU7WUFDckMsY0FBYyxFQUFFLE1BQU0sQ0FBQyxjQUFjLElBQUksRUFBRTtZQUMzQyxpQkFBaUIsRUFBRSxNQUFNLENBQUMsaUJBQWlCLElBQUksRUFBRTtZQUNqRCxnQkFBZ0IsRUFBRSxNQUFNLENBQUMsZ0JBQWdCO1lBQ3pDLFlBQVksRUFBRSxNQUFNLENBQUMsWUFBWTtTQUNsQyxDQUFDO0lBRUosQ0FBQztJQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7UUFDZixlQUFNLENBQUMsS0FBSyxDQUFDLGdDQUFnQyxFQUFFLEVBQUUsU0FBUyxFQUFFLEtBQUssRUFBRSxDQUFDLENBQUM7UUFDckUsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVMsdUJBQXVCLENBQzlCLFlBQXlCLEVBQ3pCLGNBQStCLEVBQy9CLFFBQXFCO0lBR3JCLE1BQU0sY0FBYyxHQUFHO1FBQ3JCLEdBQUcsWUFBWSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLEtBQUssVUFBVSxDQUFDO1FBQ3RELEdBQUcsY0FBYyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLEtBQUssVUFBVSxDQUFDO1FBQ3hELEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRLEtBQUssVUFBVSxDQUFDO0tBQ25ELENBQUMsTUFBTSxDQUFDO0lBRVQsTUFBTSxXQUFXLEdBQUc7UUFDbEIsR0FBRyxZQUFZLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFFBQVEsS0FBSyxPQUFPLENBQUM7UUFDbkQsR0FBRyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFFBQVEsS0FBSyxNQUFNLENBQUM7UUFDcEQsR0FBRyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFFBQVEsS0FBSyxPQUFPLENBQUM7S0FDaEQsQ0FBQyxNQUFNLENBQUM7SUFFVCxJQUFJLGNBQWMsR0FBRyxDQUFDO1FBQUUsT0FBTyxNQUFNLENBQUM7SUFDdEMsSUFBSSxXQUFXLEdBQUcsQ0FBQztRQUFFLE9BQU8sTUFBTSxDQUFDO0lBQ25DLElBQUksV0FBVyxHQUFHLENBQUM7UUFBRSxPQUFPLE1BQU0sQ0FBQztJQUNuQyxPQUFPLFdBQVcsQ0FBQztBQUNyQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXZpZXcgJiBSZWZpbmUgQWdlbnRcbiAqIEFuYWx5emVzIGNvZGUsIGZpbmRzIGVycm9ycywgYW5kIHN1Z2dlc3RzIGltcHJvdmVtZW50c1xuICovXG5cbmltcG9ydCB7IENvbnRleHQgfSBmcm9tICdhd3MtbGFtYmRhJztcbmltcG9ydCB7IER5bmFtb0RCQ2xpZW50IH0gZnJvbSAnQGF3cy1zZGsvY2xpZW50LWR5bmFtb2RiJztcbmltcG9ydCB7IER5bmFtb0RCRG9jdW1lbnRDbGllbnQsIFB1dENvbW1hbmQsIFF1ZXJ5Q29tbWFuZCB9IGZyb20gJ0Bhd3Mtc2RrL2xpYi1keW5hbW9kYic7XG5pbXBvcnQgeyBTM0NsaWVudCwgR2V0T2JqZWN0Q29tbWFuZCB9IGZyb20gJ0Bhd3Mtc2RrL2NsaWVudC1zMyc7XG5pbXBvcnQgeyBcbiAgUmV2aWV3UmVmaW5lRXZlbnQsIFxuICBSZXZpZXdSZXN1bHQsIFxuICBDb2RlQXJ0aWZhY3QsIFxuICBDb2RlRXJyb3IsIFxuICBTdWdnZXN0aW9uLFxuICBTZWN1cml0eUlzc3VlLFxuICBQZXJmb3JtYW5jZUlzc3VlLFxuICBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIFRhc2tQbGFuLFxuICBUYXNrXG59IGZyb20gJy4uLy4uL3R5cGVzJztcbmltcG9ydCB7IGNyZWF0ZVJldmlld1JlY29yZCB9IGZyb20gJy4uLy4uL2RhdGFiYXNlL3NjaGVtYXMnO1xuaW1wb3J0IHsgaW52b2tlTExNIH0gZnJvbSAnLi4vLi4vdXRpbHMvbGxtLWNsaWVudCc7XG5pbXBvcnQgeyBsb2dnZXIgfSBmcm9tICcuLi8uLi91dGlscy9sb2dnZXInO1xuaW1wb3J0IHsgcnVuU3RhdGljQW5hbHlzaXMgfSBmcm9tICcuLi8uLi91dGlscy9zdGF0aWMtYW5hbHlzaXMnO1xuaW1wb3J0IHsgcnVuU2VjdXJpdHlTY2FuIH0gZnJvbSAnLi4vLi4vdXRpbHMvc2VjdXJpdHktc2Nhbm5lcic7XG5cbmNvbnN0IGR5bmFtb0NsaWVudCA9IER5bmFtb0RCRG9jdW1lbnRDbGllbnQuZnJvbShuZXcgRHluYW1vREJDbGllbnQoe30pKTtcbmNvbnN0IHMzQ2xpZW50ID0gbmV3IFMzQ2xpZW50KHt9KTtcbmNvbnN0IFRBQkxFX05BTUUgPSBwcm9jZXNzLmVudi5XT1JLRkxPV19UQUJMRV9OQU1FITtcbmNvbnN0IEFSVElGQUNUU19CVUNLRVQgPSBwcm9jZXNzLmVudi5BUlRJRkFDVFNfQlVDS0VUX05BTUUhO1xuXG5leHBvcnQgY29uc3QgaGFuZGxlciA9IGFzeW5jIChcbiAgZXZlbnQ6IFJldmlld1JlZmluZUV2ZW50LFxuICBjb250ZXh0OiBDb250ZXh0XG4pOiBQcm9taXNlPFJldmlld1Jlc3VsdFtdPiA9PiB7XG4gIGNvbnN0IHJlcXVlc3RJZCA9IGNvbnRleHQuYXdzUmVxdWVzdElkO1xuICBsb2dnZXIuaW5mbygnUmV2aWV3ICYgUmVmaW5lIEFnZW50IHN0YXJ0ZWQnLCB7IHJlcXVlc3RJZCwgZXZlbnQgfSk7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlc3Npb25JZCwgc3BlY2lmaWNhdGlvbiwgdGFza1BsYW4sIGFydGlmYWN0cywgaXRlcmF0aW9uIH0gPSBldmVudDtcblxuICAgIC8vIEZldGNoIGFsbCBhcnRpZmFjdHMgZnJvbSBTM1xuICAgIGNvbnN0IGFsbEFydGlmYWN0cyA9IGF3YWl0IGZldGNoQWxsQXJ0aWZhY3RzKHNlc3Npb25JZCwgYXJ0aWZhY3RzKTtcblxuICAgIC8vIFBlcmZvcm0gY29tcHJlaGVuc2l2ZSByZXZpZXdcbiAgICBjb25zdCByZXZpZXdSZXN1bHRzOiBSZXZpZXdSZXN1bHRbXSA9IFtdO1xuXG4gICAgZm9yIChjb25zdCBhcnRpZmFjdCBvZiBhbGxBcnRpZmFjdHMpIHtcbiAgICAgIGNvbnN0IHJldmlldyA9IGF3YWl0IHBlcmZvcm1Db21wcmVoZW5zaXZlUmV2aWV3KFxuICAgICAgICBhcnRpZmFjdCxcbiAgICAgICAgc3BlY2lmaWNhdGlvbixcbiAgICAgICAgdGFza1BsYW4sXG4gICAgICAgIGFsbEFydGlmYWN0cyxcbiAgICAgICAgaXRlcmF0aW9uLFxuICAgICAgICByZXF1ZXN0SWRcbiAgICAgICk7XG5cbiAgICAgIHJldmlld1Jlc3VsdHMucHVzaChyZXZpZXcpO1xuXG4gICAgICAvLyBTYXZlIHJldmlldyByZWNvcmQgdG8gRHluYW1vREJcbiAgICAgIGNvbnN0IHJldmlld1JlY29yZCA9IGNyZWF0ZVJldmlld1JlY29yZChcbiAgICAgICAgc2Vzc2lvbklkLCBcbiAgICAgICAgYXJ0aWZhY3QudGFza0lkLCBcbiAgICAgICAgcmV2aWV3LCBcbiAgICAgICAgJ1Jldmlld1JlZmluZUFnZW50J1xuICAgICAgKTtcbiAgICAgIGF3YWl0IGR5bmFtb0NsaWVudC5zZW5kKG5ldyBQdXRDb21tYW5kKHtcbiAgICAgICAgVGFibGVOYW1lOiBUQUJMRV9OQU1FLFxuICAgICAgICBJdGVtOiByZXZpZXdSZWNvcmQsXG4gICAgICB9KSk7XG4gICAgfVxuXG4gICAgLy8gUGVyZm9ybSBjcm9zcy1hcnRpZmFjdCBhbmFseXNpc1xuICAgIGNvbnN0IGNyb3NzQXJ0aWZhY3RSZXZpZXcgPSBhd2FpdCBwZXJmb3JtQ3Jvc3NBcnRpZmFjdEFuYWx5c2lzKFxuICAgICAgYWxsQXJ0aWZhY3RzLFxuICAgICAgc3BlY2lmaWNhdGlvbixcbiAgICAgIHRhc2tQbGFuLFxuICAgICAgcmVxdWVzdElkXG4gICAgKTtcblxuICAgIGlmIChjcm9zc0FydGlmYWN0UmV2aWV3KSB7XG4gICAgICByZXZpZXdSZXN1bHRzLnB1c2goY3Jvc3NBcnRpZmFjdFJldmlldyk7XG4gICAgICBcbiAgICAgIGNvbnN0IGNyb3NzUmV2aWV3UmVjb3JkID0gY3JlYXRlUmV2aWV3UmVjb3JkKFxuICAgICAgICBzZXNzaW9uSWQsIFxuICAgICAgICAnY3Jvc3MtYXJ0aWZhY3QtYW5hbHlzaXMnLCBcbiAgICAgICAgY3Jvc3NBcnRpZmFjdFJldmlldywgXG4gICAgICAgICdSZXZpZXdSZWZpbmVBZ2VudCdcbiAgICAgICk7XG4gICAgICBhd2FpdCBkeW5hbW9DbGllbnQuc2VuZChuZXcgUHV0Q29tbWFuZCh7XG4gICAgICAgIFRhYmxlTmFtZTogVEFCTEVfTkFNRSxcbiAgICAgICAgSXRlbTogY3Jvc3NSZXZpZXdSZWNvcmQsXG4gICAgICB9KSk7XG4gICAgfVxuXG4gICAgbG9nZ2VyLmluZm8oJ1JldmlldyAmIFJlZmluZSBjb21wbGV0ZWQnLCB7IFxuICAgICAgcmVxdWVzdElkLCBcbiAgICAgIHNlc3Npb25JZCwgXG4gICAgICByZXZpZXdzQ291bnQ6IHJldmlld1Jlc3VsdHMubGVuZ3RoLFxuICAgICAgdG90YWxFcnJvcnM6IHJldmlld1Jlc3VsdHMucmVkdWNlKChzdW0sIHIpID0+IHN1bSArIHIuZXJyb3JzLmxlbmd0aCwgMCksXG4gICAgICB0b3RhbFN1Z2dlc3Rpb25zOiByZXZpZXdSZXN1bHRzLnJlZHVjZSgoc3VtLCByKSA9PiBzdW0gKyByLnN1Z2dlc3Rpb25zLmxlbmd0aCwgMClcbiAgICB9KTtcblxuICAgIHJldHVybiByZXZpZXdSZXN1bHRzO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nZ2VyLmVycm9yKCdSZXZpZXcgJiBSZWZpbmUgQWdlbnQgZmFpbGVkJywgeyByZXF1ZXN0SWQsIGVycm9yIH0pO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59O1xuXG5hc3luYyBmdW5jdGlvbiBmZXRjaEFsbEFydGlmYWN0cyhcbiAgc2Vzc2lvbklkOiBzdHJpbmcsIFxuICBhcnRpZmFjdFJlZmVyZW5jZXM6IENvZGVBcnRpZmFjdFtdXG4pOiBQcm9taXNlPENvZGVBcnRpZmFjdFtdPiB7XG4gIFxuICBjb25zdCBhcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdID0gW107XG5cbiAgLy8gUXVlcnkgRHluYW1vREIgZm9yIGFsbCBhcnRpZmFjdHMgaW4gdGhpcyBzZXNzaW9uXG4gIGNvbnN0IHF1ZXJ5UmVzdWx0ID0gYXdhaXQgZHluYW1vQ2xpZW50LnNlbmQobmV3IFF1ZXJ5Q29tbWFuZCh7XG4gICAgVGFibGVOYW1lOiBUQUJMRV9OQU1FLFxuICAgIEtleUNvbmRpdGlvbkV4cHJlc3Npb246ICdQSyA9IDpwayBBTkQgYmVnaW5zX3dpdGgoU0ssIDpzayknLFxuICAgIEV4cHJlc3Npb25BdHRyaWJ1dGVWYWx1ZXM6IHtcbiAgICAgICc6cGsnOiBgU0VTU0lPTiMke3Nlc3Npb25JZH1gLFxuICAgICAgJzpzayc6ICdBUlRJRkFDVCMnLFxuICAgIH0sXG4gIH0pKTtcblxuICAvLyBGZXRjaCBhcnRpZmFjdCBkZXRhaWxzIGZyb20gUzNcbiAgZm9yIChjb25zdCBpdGVtIG9mIHF1ZXJ5UmVzdWx0Lkl0ZW1zIHx8IFtdKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHMzUmVzcG9uc2UgPSBhd2FpdCBzM0NsaWVudC5zZW5kKG5ldyBHZXRPYmplY3RDb21tYW5kKHtcbiAgICAgICAgQnVja2V0OiBBUlRJRkFDVFNfQlVDS0VULFxuICAgICAgICBLZXk6IGl0ZW0uczNLZXksXG4gICAgICB9KSk7XG5cbiAgICAgIGNvbnN0IGFydGlmYWN0RGF0YSA9IEpTT04ucGFyc2UoYXdhaXQgczNSZXNwb25zZS5Cb2R5Py50cmFuc2Zvcm1Ub1N0cmluZygpIHx8ICd7fScpO1xuICAgICAgYXJ0aWZhY3RzLnB1c2goYXJ0aWZhY3REYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgbG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYXJ0aWZhY3QgZnJvbSBTMycsIHsgczNLZXk6IGl0ZW0uczNLZXksIGVycm9yIH0pO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBhcnRpZmFjdHM7XG59XG5cbmFzeW5jIGZ1bmN0aW9uIHBlcmZvcm1Db21wcmVoZW5zaXZlUmV2aWV3KFxuICBhcnRpZmFjdDogQ29kZUFydGlmYWN0LFxuICBzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIHRhc2tQbGFuOiBUYXNrUGxhbixcbiAgYWxsQXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXSxcbiAgaXRlcmF0aW9uOiBudW1iZXIsXG4gIHJlcXVlc3RJZDogc3RyaW5nXG4pOiBQcm9taXNlPFJldmlld1Jlc3VsdD4ge1xuICBcbiAgLy8gUnVuIHN0YXRpYyBhbmFseXNpc1xuICBjb25zdCBzdGF0aWNBbmFseXNpc1Jlc3VsdHMgPSBhd2FpdCBydW5TdGF0aWNBbmFseXNpcyhhcnRpZmFjdC5maWxlcyk7XG4gIFxuICAvLyBSdW4gc2VjdXJpdHkgc2NhblxuICBjb25zdCBzZWN1cml0eVJlc3VsdHMgPSBhd2FpdCBydW5TZWN1cml0eVNjYW4oYXJ0aWZhY3QuZmlsZXMpO1xuICBcbiAgLy8gUGVyZm9ybSBBSS1wb3dlcmVkIGNvZGUgcmV2aWV3XG4gIGNvbnN0IGFpUmV2aWV3UmVzdWx0cyA9IGF3YWl0IHBlcmZvcm1BSUNvZGVSZXZpZXcoXG4gICAgYXJ0aWZhY3QsXG4gICAgc3BlY2lmaWNhdGlvbixcbiAgICB0YXNrUGxhbixcbiAgICBhbGxBcnRpZmFjdHMsXG4gICAgaXRlcmF0aW9uLFxuICAgIHJlcXVlc3RJZFxuICApO1xuXG4gIC8vIENvbWJpbmUgYWxsIHJlc3VsdHNcbiAgY29uc3QgcmV2aWV3OiBSZXZpZXdSZXN1bHQgPSB7XG4gICAgdGFza0lkOiBhcnRpZmFjdC50YXNrSWQsXG4gICAgb3ZlcmFsbFF1YWxpdHk6IGRldGVybWluZU92ZXJhbGxRdWFsaXR5KFxuICAgICAgc3RhdGljQW5hbHlzaXNSZXN1bHRzLmVycm9ycyxcbiAgICAgIHNlY3VyaXR5UmVzdWx0cy5pc3N1ZXMsXG4gICAgICBhaVJldmlld1Jlc3VsdHMuZXJyb3JzXG4gICAgKSxcbiAgICBlcnJvcnM6IFtcbiAgICAgIC4uLnN0YXRpY0FuYWx5c2lzUmVzdWx0cy5lcnJvcnMsXG4gICAgICAuLi5zZWN1cml0eVJlc3VsdHMuZXJyb3JzLFxuICAgICAgLi4uYWlSZXZpZXdSZXN1bHRzLmVycm9ycyxcbiAgICBdLFxuICAgIHN1Z2dlc3Rpb25zOiBbXG4gICAgICAuLi5zdGF0aWNBbmFseXNpc1Jlc3VsdHMuc3VnZ2VzdGlvbnMsXG4gICAgICAuLi5haVJldmlld1Jlc3VsdHMuc3VnZ2VzdGlvbnMsXG4gICAgXSxcbiAgICBzZWN1cml0eUlzc3Vlczogc2VjdXJpdHlSZXN1bHRzLmlzc3VlcyxcbiAgICBwZXJmb3JtYW5jZUlzc3VlczogYWlSZXZpZXdSZXN1bHRzLnBlcmZvcm1hbmNlSXNzdWVzLFxuICAgIHVwZGF0ZWRTcGVjRGVsdGE6IGFpUmV2aWV3UmVzdWx0cy51cGRhdGVkU3BlY0RlbHRhLFxuICAgIHVwZGF0ZWRUYXNrczogYWlSZXZpZXdSZXN1bHRzLnVwZGF0ZWRUYXNrcyxcbiAgfTtcblxuICByZXR1cm4gcmV2aWV3O1xufVxuXG5hc3luYyBmdW5jdGlvbiBwZXJmb3JtQUlDb2RlUmV2aWV3KFxuICBhcnRpZmFjdDogQ29kZUFydGlmYWN0LFxuICBzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIHRhc2tQbGFuOiBUYXNrUGxhbixcbiAgYWxsQXJ0aWZhY3RzOiBDb2RlQXJ0aWZhY3RbXSxcbiAgaXRlcmF0aW9uOiBudW1iZXIsXG4gIHJlcXVlc3RJZDogc3RyaW5nXG4pOiBQcm9taXNlPHtcbiAgZXJyb3JzOiBDb2RlRXJyb3JbXTtcbiAgc3VnZ2VzdGlvbnM6IFN1Z2dlc3Rpb25bXTtcbiAgcGVyZm9ybWFuY2VJc3N1ZXM6IFBlcmZvcm1hbmNlSXNzdWVbXTtcbiAgdXBkYXRlZFNwZWNEZWx0YT86IFBhcnRpYWw8RGV0YWlsZWRTcGVjaWZpY2F0aW9uPjtcbiAgdXBkYXRlZFRhc2tzPzogVGFza1tdO1xufT4ge1xuICBcbiAgY29uc3QgcmV2aWV3UHJvbXB0ID0gYFxuWW91IGFyZSBhbiBleHBlcnQgY29kZSByZXZpZXdlciBhbmQgc29mdHdhcmUgYXJjaGl0ZWN0LiBZb3VyIHRhc2sgaXMgdG8gcGVyZm9ybSBhIGNvbXByZWhlbnNpdmUgcmV2aWV3IG9mIHRoZSBnZW5lcmF0ZWQgY29kZSBhbmQgcHJvdmlkZSBkZXRhaWxlZCBmZWVkYmFjay5cblxuUFJPSkVDVCBTUEVDSUZJQ0FUSU9OOlxuJHtKU09OLnN0cmluZ2lmeShzcGVjaWZpY2F0aW9uLCBudWxsLCAyKX1cblxuVEFTSyBQTEFOOlxuJHtKU09OLnN0cmluZ2lmeSh0YXNrUGxhbiwgbnVsbCwgMil9XG5cbkNVUlJFTlQgQVJUSUZBQ1Q6XG4ke0pTT04uc3RyaW5naWZ5KGFydGlmYWN0LCBudWxsLCAyKX1cblxuQUxMIEFSVElGQUNUUyBDT05URVhUOlxuJHtKU09OLnN0cmluZ2lmeShhbGxBcnRpZmFjdHMubWFwKGEgPT4gKHsgdGFza0lkOiBhLnRhc2tJZCwgZmlsZUNvdW50OiBhLmZpbGVzLmxlbmd0aCwgZXJyb3JzOiBhLmVycm9ycy5sZW5ndGggfSkpLCBudWxsLCAyKX1cblxuSVRFUkFUSU9OOiAke2l0ZXJhdGlvbn1cblxuUGxlYXNlIHBlcmZvcm0gYSB0aG9yb3VnaCByZXZpZXcgZm9jdXNpbmcgb246XG5cbjEuIENPREUgUVVBTElUWTpcbiAgIC0gQWRoZXJlbmNlIHRvIGJlc3QgcHJhY3RpY2VzIGFuZCBkZXNpZ24gcGF0dGVybnNcbiAgIC0gQ29kZSByZWFkYWJpbGl0eSBhbmQgbWFpbnRhaW5hYmlsaXR5XG4gICAtIFByb3BlciBlcnJvciBoYW5kbGluZyBhbmQgdmFsaWRhdGlvblxuICAgLSBUeXBlIHNhZmV0eSBhbmQgbnVsbCBjaGVja3NcbiAgIC0gRG9jdW1lbnRhdGlvbiBhbmQgY29tbWVudHNcblxuMi4gQVJDSElURUNUVVJFICYgREVTSUdOOlxuICAgLSBDb25zaXN0ZW5jeSB3aXRoIHRoZSBvdmVyYWxsIHNwZWNpZmljYXRpb25cbiAgIC0gUHJvcGVyIHNlcGFyYXRpb24gb2YgY29uY2VybnNcbiAgIC0gU2NhbGFiaWxpdHkgYW5kIGV4dGVuc2liaWxpdHlcbiAgIC0gSW50ZWdyYXRpb24gd2l0aCBvdGhlciBjb21wb25lbnRzXG5cbjMuIFNFQ1VSSVRZOlxuICAgLSBJbnB1dCB2YWxpZGF0aW9uIGFuZCBzYW5pdGl6YXRpb25cbiAgIC0gQXV0aGVudGljYXRpb24gYW5kIGF1dGhvcml6YXRpb25cbiAgIC0gUHJvdGVjdGlvbiBhZ2FpbnN0IGNvbW1vbiB2dWxuZXJhYmlsaXRpZXNcbiAgIC0gU2VjdXJlIGRhdGEgaGFuZGxpbmdcblxuNC4gUEVSRk9STUFOQ0U6XG4gICAtIEVmZmljaWVudCBhbGdvcml0aG1zIGFuZCBkYXRhIHN0cnVjdHVyZXNcbiAgIC0gUHJvcGVyIGNhY2hpbmcgc3RyYXRlZ2llc1xuICAgLSBEYXRhYmFzZSBxdWVyeSBvcHRpbWl6YXRpb25cbiAgIC0gUmVzb3VyY2UgdXNhZ2Ugb3B0aW1pemF0aW9uXG5cbjUuIFRFU1RJTkc6XG4gICAtIFRlc3QgY292ZXJhZ2UgYW5kIHF1YWxpdHlcbiAgIC0gRWRnZSBjYXNlIGhhbmRsaW5nXG4gICAtIEludGVncmF0aW9uIHRlc3Qgc2NlbmFyaW9zXG5cbjYuIERFUExPWU1FTlQgJiBPUEVSQVRJT05TOlxuICAgLSBDb25maWd1cmF0aW9uIG1hbmFnZW1lbnRcbiAgIC0gTW9uaXRvcmluZyBhbmQgbG9nZ2luZ1xuICAgLSBFcnJvciB0cmFja2luZyBhbmQgYWxlcnRpbmdcblxuQmFzZWQgb24geW91ciByZXZpZXcsIHByb3ZpZGU6XG5cbjEuIEVSUk9SUzogQ3JpdGljYWwgaXNzdWVzIHRoYXQgbXVzdCBiZSBmaXhlZFxuMi4gU1VHR0VTVElPTlM6IEltcHJvdmVtZW50cyBhbmQgb3B0aW1pemF0aW9uc1xuMy4gUEVSRk9STUFOQ0UgSVNTVUVTOiBQZXJmb3JtYW5jZS1yZWxhdGVkIGNvbmNlcm5zXG40LiBVUERBVEVEIFNQRUNJRklDQVRJT046IEFueSBjaGFuZ2VzIG5lZWRlZCB0byB0aGUgb3JpZ2luYWwgc3BlY2lmaWNhdGlvblxuNS4gVVBEQVRFRCBUQVNLUzogTmV3IG9yIG1vZGlmaWVkIHRhc2tzIGJhc2VkIG9uIHlvdXIgZmluZGluZ3NcblxuUmV0dXJuIHlvdXIgcmVzcG9uc2UgYXMgYSBKU09OIG9iamVjdCB3aXRoIHRoZSBmb2xsb3dpbmcgc3RydWN0dXJlOlxue1xuICBcImVycm9yc1wiOiBbXG4gICAge1xuICAgICAgXCJzZXZlcml0eVwiOiBcImNyaXRpY2FsfG1ham9yfG1pbm9yXCIsXG4gICAgICBcIm1lc3NhZ2VcIjogXCJzdHJpbmdcIixcbiAgICAgIFwiZmlsZVwiOiBcInN0cmluZ1wiLFxuICAgICAgXCJsaW5lXCI6IG51bWJlcixcbiAgICAgIFwicnVsZVwiOiBcInN0cmluZ1wiXG4gICAgfVxuICBdLFxuICBcInN1Z2dlc3Rpb25zXCI6IFtcbiAgICB7XG4gICAgICBcInR5cGVcIjogXCJpbXByb3ZlbWVudHxvcHRpbWl6YXRpb258cmVmYWN0b3J8ZmVhdHVyZVwiLFxuICAgICAgXCJkZXNjcmlwdGlvblwiOiBcInN0cmluZ1wiLFxuICAgICAgXCJpbXBhY3RcIjogXCJoaWdofG1lZGl1bXxsb3dcIixcbiAgICAgIFwiZWZmb3J0XCI6IFwiaGlnaHxtZWRpdW18bG93XCJcbiAgICB9XG4gIF0sXG4gIFwicGVyZm9ybWFuY2VJc3N1ZXNcIjogW1xuICAgIHtcbiAgICAgIFwidHlwZVwiOiBcIm1lbW9yeXxjcHV8bmV0d29ya3xkYXRhYmFzZVwiLFxuICAgICAgXCJkZXNjcmlwdGlvblwiOiBcInN0cmluZ1wiLFxuICAgICAgXCJpbXBhY3RcIjogXCJoaWdofG1lZGl1bXxsb3dcIixcbiAgICAgIFwicmVjb21tZW5kYXRpb25cIjogXCJzdHJpbmdcIlxuICAgIH1cbiAgXSxcbiAgXCJ1cGRhdGVkU3BlY0RlbHRhXCI6IHtcbiAgICAvLyBPbmx5IGluY2x1ZGUgZmllbGRzIHRoYXQgbmVlZCB0byBiZSB1cGRhdGVkXG4gIH0sXG4gIFwidXBkYXRlZFRhc2tzXCI6IFtcbiAgICAvLyBOZXcgb3IgbW9kaWZpZWQgdGFza3NcbiAgXVxufVxuXG5CZSB0aG9yb3VnaCBidXQgY29uc3RydWN0aXZlIGluIHlvdXIgZmVlZGJhY2suIEZvY3VzIG9uIGFjdGlvbmFibGUgaW1wcm92ZW1lbnRzLlxuYDtcblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW52b2tlTExNKHJldmlld1Byb21wdCwge1xuICAgICAgdGVtcGVyYXR1cmU6IDAuMixcbiAgICAgIG1heFRva2VuczogNjAwMCxcbiAgICAgIHJlcXVlc3RJZCxcbiAgICB9KTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IEpTT04ucGFyc2UocmVzcG9uc2UpO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBlcnJvcnM6IHJlc3VsdC5lcnJvcnMgfHwgW10sXG4gICAgICBzdWdnZXN0aW9uczogcmVzdWx0LnN1Z2dlc3Rpb25zIHx8IFtdLFxuICAgICAgcGVyZm9ybWFuY2VJc3N1ZXM6IHJlc3VsdC5wZXJmb3JtYW5jZUlzc3VlcyB8fCBbXSxcbiAgICAgIHVwZGF0ZWRTcGVjRGVsdGE6IHJlc3VsdC51cGRhdGVkU3BlY0RlbHRhLFxuICAgICAgdXBkYXRlZFRhc2tzOiByZXN1bHQudXBkYXRlZFRhc2tzLFxuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dnZXIuZXJyb3IoJ0FJIGNvZGUgcmV2aWV3IGZhaWxlZCcsIHsgcmVxdWVzdElkLCB0YXNrSWQ6IGFydGlmYWN0LnRhc2tJZCwgZXJyb3IgfSk7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIGVycm9yczogW3tcbiAgICAgICAgc2V2ZXJpdHk6ICdtYWpvcicsXG4gICAgICAgIG1lc3NhZ2U6IGBBSSBjb2RlIHJldmlldyBmYWlsZWQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpfWAsXG4gICAgICAgIHJ1bGU6ICdyZXZpZXdfZXJyb3InLFxuICAgICAgfV0sXG4gICAgICBzdWdnZXN0aW9uczogW10sXG4gICAgICBwZXJmb3JtYW5jZUlzc3VlczogW10sXG4gICAgfTtcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBwZXJmb3JtQ3Jvc3NBcnRpZmFjdEFuYWx5c2lzKFxuICBhbGxBcnRpZmFjdHM6IENvZGVBcnRpZmFjdFtdLFxuICBzcGVjaWZpY2F0aW9uOiBEZXRhaWxlZFNwZWNpZmljYXRpb24sXG4gIHRhc2tQbGFuOiBUYXNrUGxhbixcbiAgcmVxdWVzdElkOiBzdHJpbmdcbik6IFByb21pc2U8UmV2aWV3UmVzdWx0IHwgbnVsbD4ge1xuICBcbiAgaWYgKGFsbEFydGlmYWN0cy5sZW5ndGggPCAyKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIE5vIGNyb3NzLWFydGlmYWN0IGFuYWx5c2lzIG5lZWRlZCBmb3Igc2luZ2xlIGFydGlmYWN0XG4gIH1cblxuICBjb25zdCBjcm9zc0FuYWx5c2lzUHJvbXB0ID0gYFxuWW91IGFyZSBwZXJmb3JtaW5nIGEgY3Jvc3MtYXJ0aWZhY3QgYW5hbHlzaXMgdG8gZW5zdXJlIGNvbnNpc3RlbmN5IGFuZCBpbnRlZ3JhdGlvbiBhY3Jvc3MgYWxsIGdlbmVyYXRlZCBjb2RlIGNvbXBvbmVudHMuXG5cblBST0pFQ1QgU1BFQ0lGSUNBVElPTjpcbiR7SlNPTi5zdHJpbmdpZnkoc3BlY2lmaWNhdGlvbiwgbnVsbCwgMil9XG5cbkFMTCBBUlRJRkFDVFM6XG4ke0pTT04uc3RyaW5naWZ5KGFsbEFydGlmYWN0cy5tYXAoYSA9PiAoe1xuICB0YXNrSWQ6IGEudGFza0lkLFxuICBmaWxlczogYS5maWxlcy5tYXAoZiA9PiAoeyBwYXRoOiBmLnBhdGgsIHR5cGU6IGYudHlwZSwgbGFuZ3VhZ2U6IGYubGFuZ3VhZ2UgfSkpLFxuICBlcnJvcnM6IGEuZXJyb3JzLFxufSkpLCBudWxsLCAyKX1cblxuQW5hbHl6ZSB0aGUgYXJ0aWZhY3RzIGZvcjpcblxuMS4gSU5URUdSQVRJT04gQ09OU0lTVEVOQ1k6XG4gICAtIEFQSSBjb250cmFjdHMgYmV0d2VlbiBmcm9udGVuZCBhbmQgYmFja2VuZFxuICAgLSBEYXRhIG1vZGVsIGNvbnNpc3RlbmN5IGFjcm9zcyBjb21wb25lbnRzXG4gICAtIEltcG9ydC9leHBvcnQgY29tcGF0aWJpbGl0eVxuICAgLSBDb25maWd1cmF0aW9uIGFsaWdubWVudFxuXG4yLiBBUkNISVRFQ1RVUkFMIENPSEVSRU5DRTpcbiAgIC0gQ29uc2lzdGVudCBkZXNpZ24gcGF0dGVybnNcbiAgIC0gUHJvcGVyIGRlcGVuZGVuY3kgbWFuYWdlbWVudFxuICAgLSBTZXJ2aWNlIGJvdW5kYXJpZXMgYW5kIGludGVyZmFjZXNcbiAgIC0gRGF0YSBmbG93IGNvbnNpc3RlbmN5XG5cbjMuIENST1NTLUNVVFRJTkcgQ09OQ0VSTlM6XG4gICAtIEVycm9yIGhhbmRsaW5nIHN0cmF0ZWdpZXNcbiAgIC0gTG9nZ2luZyBhbmQgbW9uaXRvcmluZ1xuICAgLSBTZWN1cml0eSBpbXBsZW1lbnRhdGlvbnNcbiAgIC0gUGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uc1xuXG5Qcm92aWRlIGZlZWRiYWNrIG9uIGludGVncmF0aW9uIGlzc3VlcyBhbmQgb3ZlcmFsbCBzeXN0ZW0gY29oZXJlbmNlLlxuXG5SZXR1cm4geW91ciByZXNwb25zZSBhcyBhIEpTT04gb2JqZWN0IGZvbGxvd2luZyB0aGUgc2FtZSBzdHJ1Y3R1cmUgYXMgaW5kaXZpZHVhbCBhcnRpZmFjdCByZXZpZXdzLlxuYDtcblxuICB0cnkge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgaW52b2tlTExNKGNyb3NzQW5hbHlzaXNQcm9tcHQsIHtcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjIsXG4gICAgICBtYXhUb2tlbnM6IDQwMDAsXG4gICAgICByZXF1ZXN0SWQsXG4gICAgfSk7XG5cbiAgICBjb25zdCByZXN1bHQgPSBKU09OLnBhcnNlKHJlc3BvbnNlKTtcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgdGFza0lkOiAnY3Jvc3MtYXJ0aWZhY3QtYW5hbHlzaXMnLFxuICAgICAgb3ZlcmFsbFF1YWxpdHk6IHJlc3VsdC5vdmVyYWxsUXVhbGl0eSB8fCAnZ29vZCcsXG4gICAgICBlcnJvcnM6IHJlc3VsdC5lcnJvcnMgfHwgW10sXG4gICAgICBzdWdnZXN0aW9uczogcmVzdWx0LnN1Z2dlc3Rpb25zIHx8IFtdLFxuICAgICAgc2VjdXJpdHlJc3N1ZXM6IHJlc3VsdC5zZWN1cml0eUlzc3VlcyB8fCBbXSxcbiAgICAgIHBlcmZvcm1hbmNlSXNzdWVzOiByZXN1bHQucGVyZm9ybWFuY2VJc3N1ZXMgfHwgW10sXG4gICAgICB1cGRhdGVkU3BlY0RlbHRhOiByZXN1bHQudXBkYXRlZFNwZWNEZWx0YSxcbiAgICAgIHVwZGF0ZWRUYXNrczogcmVzdWx0LnVwZGF0ZWRUYXNrcyxcbiAgICB9O1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nZ2VyLmVycm9yKCdDcm9zcy1hcnRpZmFjdCBhbmFseXNpcyBmYWlsZWQnLCB7IHJlcXVlc3RJZCwgZXJyb3IgfSk7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuZnVuY3Rpb24gZGV0ZXJtaW5lT3ZlcmFsbFF1YWxpdHkoXG4gIHN0YXRpY0Vycm9yczogQ29kZUVycm9yW10sXG4gIHNlY3VyaXR5SXNzdWVzOiBTZWN1cml0eUlzc3VlW10sXG4gIGFpRXJyb3JzOiBDb2RlRXJyb3JbXVxuKTogJ2V4Y2VsbGVudCcgfCAnZ29vZCcgfCAnZmFpcicgfCAncG9vcicge1xuICBcbiAgY29uc3QgY3JpdGljYWxJc3N1ZXMgPSBbXG4gICAgLi4uc3RhdGljRXJyb3JzLmZpbHRlcihlID0+IGUuc2V2ZXJpdHkgPT09ICdjcml0aWNhbCcpLFxuICAgIC4uLnNlY3VyaXR5SXNzdWVzLmZpbHRlcihzID0+IHMuc2V2ZXJpdHkgPT09ICdjcml0aWNhbCcpLFxuICAgIC4uLmFpRXJyb3JzLmZpbHRlcihlID0+IGUuc2V2ZXJpdHkgPT09ICdjcml0aWNhbCcpLFxuICBdLmxlbmd0aDtcblxuICBjb25zdCBtYWpvcklzc3VlcyA9IFtcbiAgICAuLi5zdGF0aWNFcnJvcnMuZmlsdGVyKGUgPT4gZS5zZXZlcml0eSA9PT0gJ21ham9yJyksXG4gICAgLi4uc2VjdXJpdHlJc3N1ZXMuZmlsdGVyKHMgPT4gcy5zZXZlcml0eSA9PT0gJ2hpZ2gnKSxcbiAgICAuLi5haUVycm9ycy5maWx0ZXIoZSA9PiBlLnNldmVyaXR5ID09PSAnbWFqb3InKSxcbiAgXS5sZW5ndGg7XG5cbiAgaWYgKGNyaXRpY2FsSXNzdWVzID4gMCkgcmV0dXJuICdwb29yJztcbiAgaWYgKG1ham9ySXNzdWVzID4gMykgcmV0dXJuICdmYWlyJztcbiAgaWYgKG1ham9ySXNzdWVzID4gMCkgcmV0dXJuICdnb29kJztcbiAgcmV0dXJuICdleGNlbGxlbnQnO1xufVxuIl19