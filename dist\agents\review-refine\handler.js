"use strict";
/**
 * Review & Refine Agent
 * Analyzes code, finds errors, and suggests improvements
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const static_analysis_1 = require("../../utils/static-analysis");
const security_scanner_1 = require("../../utils/security-scanner");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Review & Refine Agent started', { requestId, event });
    try {
        const { sessionId, specification, taskPlan, artifacts, iteration } = event;
        // Fetch all artifacts from S3
        const allArtifacts = await fetchAllArtifacts(sessionId, artifacts);
        // Perform comprehensive review
        const reviewResults = [];
        for (const artifact of allArtifacts) {
            const review = await performComprehensiveReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId);
            reviewResults.push(review);
            // Save review record to DynamoDB
            const reviewRecord = (0, schemas_1.createReviewRecord)(sessionId, artifact.taskId, review, 'ReviewRefineAgent');
            await dynamoClient.send(new lib_dynamodb_1.PutCommand({
                TableName: TABLE_NAME,
                Item: reviewRecord,
            }));
        }
        // Perform cross-artifact analysis
        const crossArtifactReview = await performCrossArtifactAnalysis(allArtifacts, specification, taskPlan, requestId);
        if (crossArtifactReview) {
            reviewResults.push(crossArtifactReview);
            const crossReviewRecord = (0, schemas_1.createReviewRecord)(sessionId, 'cross-artifact-analysis', crossArtifactReview, 'ReviewRefineAgent');
            await dynamoClient.send(new lib_dynamodb_1.PutCommand({
                TableName: TABLE_NAME,
                Item: crossReviewRecord,
            }));
        }
        logger_1.logger.info('Review & Refine completed', {
            requestId,
            sessionId,
            reviewsCount: reviewResults.length,
            totalErrors: reviewResults.reduce((sum, r) => sum + r.errors.length, 0),
            totalSuggestions: reviewResults.reduce((sum, r) => sum + r.suggestions.length, 0)
        });
        return reviewResults;
    }
    catch (error) {
        logger_1.logger.error('Review & Refine Agent failed', { requestId, error });
        throw error;
    }
};
exports.handler = handler;
async function fetchAllArtifacts(sessionId, artifactReferences) {
    const artifacts = [];
    // Query DynamoDB for all artifacts in this session
    const queryResult = await dynamoClient.send(new lib_dynamodb_1.QueryCommand({
        TableName: TABLE_NAME,
        KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
        ExpressionAttributeValues: {
            ':pk': `SESSION#${sessionId}`,
            ':sk': 'ARTIFACT#',
        },
    }));
    // Fetch artifact details from S3
    for (const item of queryResult.Items || []) {
        try {
            const s3Response = await s3Client.send(new client_s3_1.GetObjectCommand({
                Bucket: ARTIFACTS_BUCKET,
                Key: item.s3Key,
            }));
            const artifactData = JSON.parse(await s3Response.Body?.transformToString() || '{}');
            artifacts.push(artifactData);
        }
        catch (error) {
            logger_1.logger.error('Failed to fetch artifact from S3', { s3Key: item.s3Key, error });
        }
    }
    return artifacts;
}
async function performComprehensiveReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId) {
    // Run static analysis
    const staticAnalysisResults = await (0, static_analysis_1.runStaticAnalysis)(artifact.files);
    // Run security scan
    const securityResults = await (0, security_scanner_1.runSecurityScan)(artifact.files);
    // Perform AI-powered code review
    const aiReviewResults = await performAICodeReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId);
    // Combine all results
    const review = {
        taskId: artifact.taskId,
        overallQuality: determineOverallQuality(staticAnalysisResults.errors, securityResults.issues, aiReviewResults.errors),
        errors: [
            ...staticAnalysisResults.errors,
            ...securityResults.errors,
            ...aiReviewResults.errors,
        ],
        suggestions: [
            ...staticAnalysisResults.suggestions,
            ...aiReviewResults.suggestions,
        ],
        securityIssues: securityResults.issues,
        performanceIssues: aiReviewResults.performanceIssues,
        updatedSpecDelta: aiReviewResults.updatedSpecDelta,
        updatedTasks: aiReviewResults.updatedTasks,
    };
    return review;
}
async function performAICodeReview(artifact, specification, taskPlan, allArtifacts, iteration, requestId) {
    const reviewPrompt = `
You are an expert code reviewer and software architect. Your task is to perform a comprehensive review of the generated code and provide detailed feedback.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

TASK PLAN:
${JSON.stringify(taskPlan, null, 2)}

CURRENT ARTIFACT:
${JSON.stringify(artifact, null, 2)}

ALL ARTIFACTS CONTEXT:
${JSON.stringify(allArtifacts.map(a => ({ taskId: a.taskId, fileCount: a.files.length, errors: a.errors.length })), null, 2)}

ITERATION: ${iteration}

Please perform a thorough review focusing on:

1. CODE QUALITY:
   - Adherence to best practices and design patterns
   - Code readability and maintainability
   - Proper error handling and validation
   - Type safety and null checks
   - Documentation and comments

2. ARCHITECTURE & DESIGN:
   - Consistency with the overall specification
   - Proper separation of concerns
   - Scalability and extensibility
   - Integration with other components

3. SECURITY:
   - Input validation and sanitization
   - Authentication and authorization
   - Protection against common vulnerabilities
   - Secure data handling

4. PERFORMANCE:
   - Efficient algorithms and data structures
   - Proper caching strategies
   - Database query optimization
   - Resource usage optimization

5. TESTING:
   - Test coverage and quality
   - Edge case handling
   - Integration test scenarios

6. DEPLOYMENT & OPERATIONS:
   - Configuration management
   - Monitoring and logging
   - Error tracking and alerting

Based on your review, provide:

1. ERRORS: Critical issues that must be fixed
2. SUGGESTIONS: Improvements and optimizations
3. PERFORMANCE ISSUES: Performance-related concerns
4. UPDATED SPECIFICATION: Any changes needed to the original specification
5. UPDATED TASKS: New or modified tasks based on your findings

Return your response as a JSON object with the following structure:
{
  "errors": [
    {
      "severity": "critical|major|minor",
      "message": "string",
      "file": "string",
      "line": number,
      "rule": "string"
    }
  ],
  "suggestions": [
    {
      "type": "improvement|optimization|refactor|feature",
      "description": "string",
      "impact": "high|medium|low",
      "effort": "high|medium|low"
    }
  ],
  "performanceIssues": [
    {
      "type": "memory|cpu|network|database",
      "description": "string",
      "impact": "high|medium|low",
      "recommendation": "string"
    }
  ],
  "updatedSpecDelta": {
    // Only include fields that need to be updated
  },
  "updatedTasks": [
    // New or modified tasks
  ]
}

Be thorough but constructive in your feedback. Focus on actionable improvements.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(reviewPrompt, {
            temperature: 0.2,
            maxTokens: 6000,
            requestId,
        });
        const result = JSON.parse(response);
        return {
            errors: result.errors || [],
            suggestions: result.suggestions || [],
            performanceIssues: result.performanceIssues || [],
            updatedSpecDelta: result.updatedSpecDelta,
            updatedTasks: result.updatedTasks,
        };
    }
    catch (error) {
        logger_1.logger.error('AI code review failed', { requestId, taskId: artifact.taskId, error });
        return {
            errors: [{
                    severity: 'major',
                    message: `AI code review failed: ${error.message}`,
                    rule: 'review_error',
                }],
            suggestions: [],
            performanceIssues: [],
        };
    }
}
async function performCrossArtifactAnalysis(allArtifacts, specification, taskPlan, requestId) {
    if (allArtifacts.length < 2) {
        return null; // No cross-artifact analysis needed for single artifact
    }
    const crossAnalysisPrompt = `
You are performing a cross-artifact analysis to ensure consistency and integration across all generated code components.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ALL ARTIFACTS:
${JSON.stringify(allArtifacts.map(a => ({
        taskId: a.taskId,
        files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
        errors: a.errors,
    })), null, 2)}

Analyze the artifacts for:

1. INTEGRATION CONSISTENCY:
   - API contracts between frontend and backend
   - Data model consistency across components
   - Import/export compatibility
   - Configuration alignment

2. ARCHITECTURAL COHERENCE:
   - Consistent design patterns
   - Proper dependency management
   - Service boundaries and interfaces
   - Data flow consistency

3. CROSS-CUTTING CONCERNS:
   - Error handling strategies
   - Logging and monitoring
   - Security implementations
   - Performance optimizations

Provide feedback on integration issues and overall system coherence.

Return your response as a JSON object following the same structure as individual artifact reviews.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(crossAnalysisPrompt, {
            temperature: 0.2,
            maxTokens: 4000,
            requestId,
        });
        const result = JSON.parse(response);
        return {
            taskId: 'cross-artifact-analysis',
            overallQuality: result.overallQuality || 'good',
            errors: result.errors || [],
            suggestions: result.suggestions || [],
            securityIssues: result.securityIssues || [],
            performanceIssues: result.performanceIssues || [],
            updatedSpecDelta: result.updatedSpecDelta,
            updatedTasks: result.updatedTasks,
        };
    }
    catch (error) {
        logger_1.logger.error('Cross-artifact analysis failed', { requestId, error });
        return null;
    }
}
function determineOverallQuality(staticErrors, securityIssues, aiErrors) {
    const criticalIssues = [
        ...staticErrors.filter(e => e.severity === 'critical'),
        ...securityIssues.filter(s => s.severity === 'critical'),
        ...aiErrors.filter(e => e.severity === 'critical'),
    ].length;
    const majorIssues = [
        ...staticErrors.filter(e => e.severity === 'major'),
        ...securityIssues.filter(s => s.severity === 'high'),
        ...aiErrors.filter(e => e.severity === 'major'),
    ].length;
    if (criticalIssues > 0)
        return 'poor';
    if (majorIssues > 3)
        return 'fair';
    if (majorIssues > 0)
        return 'good';
    return 'excellent';
}
//# sourceMappingURL=data:application/json;base64,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