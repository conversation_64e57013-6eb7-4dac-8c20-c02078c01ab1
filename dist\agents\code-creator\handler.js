"use strict";
/**
 * Code Creator Agent
 * Generates code files based on specifications and individual tasks
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const code_validator_1 = require("../../utils/code-validator");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Code Creator Agent started', { requestId, event });
    try {
        const { sessionId, specification, task, iteration } = event;
        // Update task status to in_progress
        await updateTaskStatus(sessionId, task.taskId, 'in_progress');
        // Generate code for the specific task
        const codeArtifact = await generateCodeForTask(specification, task, iteration, requestId);
        // Validate generated code
        const validationResult = await (0, code_validator_1.validateGeneratedCode)(codeArtifact.files);
        codeArtifact.errors.push(...validationResult.errors);
        codeArtifact.warnings.push(...validationResult.warnings);
        // Store artifacts in S3
        const s3Key = await storeArtifactsInS3(sessionId, task.taskId, codeArtifact);
        // Save artifact record to DynamoDB
        const artifactRecord = (0, schemas_1.createArtifactRecord)(sessionId, task.taskId, codeArtifact, s3Key);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: artifactRecord,
        }));
        // Update task status based on errors
        const finalStatus = codeArtifact.errors.length > 0 ? 'failed' : 'completed';
        await updateTaskStatus(sessionId, task.taskId, finalStatus);
        logger_1.logger.info('Code generation completed', {
            requestId,
            sessionId,
            taskId: task.taskId,
            filesGenerated: codeArtifact.files.length,
            errorsCount: codeArtifact.errors.length,
            warningsCount: codeArtifact.warnings.length
        });
        return codeArtifact;
    }
    catch (error) {
        logger_1.logger.error('Code Creator Agent failed', { requestId, error });
        // Update task status to failed
        if (event.task?.taskId) {
            await updateTaskStatus(event.sessionId, event.task.taskId, 'failed');
        }
        throw error;
    }
};
exports.handler = handler;
async function generateCodeForTask(specification, task, iteration, requestId) {
    const codeGenerationPrompt = `
You are an expert software developer tasked with generating production-ready code for a specific task.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

CURRENT TASK:
${JSON.stringify(task, null, 2)}

ITERATION: ${iteration}

Your task is to generate complete, production-ready code files for this specific task. Consider:

1. QUALITY REQUIREMENTS:
   - Write clean, maintainable, and well-documented code
   - Follow best practices and design patterns
   - Include proper error handling and validation
   - Add comprehensive comments and documentation
   - Ensure type safety (TypeScript where applicable)

2. SECURITY CONSIDERATIONS:
   - Implement proper input validation
   - Use secure authentication and authorization
   - Protect against common vulnerabilities (XSS, SQL injection, etc.)
   - Follow OWASP guidelines

3. PERFORMANCE & SCALABILITY:
   - Optimize for performance
   - Consider caching strategies
   - Implement proper database indexing
   - Use efficient algorithms and data structures

4. TESTING:
   - Include unit tests for critical functions
   - Add integration tests where appropriate
   - Ensure good test coverage

5. DEPLOYMENT & INFRASTRUCTURE:
   - Include necessary configuration files
   - Add Docker files if needed
   - Include CI/CD pipeline configurations
   - Add monitoring and logging

Generate the following for this task:
- All necessary source code files
- Configuration files
- Test files
- Documentation files
- Any other required files

Return your response as a JSON object with the following structure:
{
  "taskId": "string",
  "files": [
    {
      "path": "string",
      "content": "string",
      "type": "source|config|documentation|test",
      "language": "string"
    }
  ],
  "errors": [],
  "warnings": [],
  "metadata": {
    "generatedAt": "string",
    "agent": "CodeCreator",
    "version": "string"
  }
}

Focus specifically on the requirements of the current task while ensuring compatibility with the overall project specification.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(codeGenerationPrompt, {
            temperature: 0.3,
            maxTokens: 8000,
            requestId,
        });
        const result = JSON.parse(response);
        // Validate and enhance the generated code
        const codeArtifact = {
            taskId: task.taskId,
            files: result.files || [],
            errors: result.errors || [],
            warnings: result.warnings || [],
            metadata: {
                generatedAt: new Date().toISOString(),
                agent: 'CodeCreator',
                version: '1.0.0',
                ...result.metadata,
            },
        };
        // Post-process generated files
        codeArtifact.files = await postProcessGeneratedFiles(codeArtifact.files, specification, task);
        return codeArtifact;
    }
    catch (error) {
        logger_1.logger.error('Code generation failed', { requestId, taskId: task.taskId, error });
        return {
            taskId: task.taskId,
            files: [],
            errors: [{
                    severity: 'critical',
                    message: `Code generation failed: ${error.message}`,
                    rule: 'generation_error',
                }],
            warnings: [],
            metadata: {
                generatedAt: new Date().toISOString(),
                agent: 'CodeCreator',
                version: '1.0.0',
            },
        };
    }
}
async function postProcessGeneratedFiles(files, specification, task) {
    return files.map(file => {
        // Add file headers and metadata
        let content = file.content;
        if (file.type === 'source' && (file.language === 'typescript' || file.language === 'javascript')) {
            const header = `/**
 * ${file.path}
 * Generated by AI Agent Workflow System
 * Task: ${task.taskName}
 * Project: ${specification.projectName}
 * Generated: ${new Date().toISOString()}
 */

`;
            content = header + content;
        }
        // Add proper imports and dependencies
        if (file.language === 'typescript' && file.type === 'source') {
            content = addTypeScriptImports(content, specification);
        }
        return {
            ...file,
            content,
        };
    });
}
function addTypeScriptImports(content, specification) {
    // Add common imports based on the tech stack
    const imports = [];
    if (specification.techStack.frontend.framework === 'React') {
        if (content.includes('React') && !content.includes("import React")) {
            imports.push("import React from 'react';");
        }
    }
    if (specification.techStack.backend.framework === 'Express.js') {
        if (content.includes('express') && !content.includes("import express")) {
            imports.push("import express from 'express';");
        }
    }
    if (imports.length > 0) {
        return imports.join('\n') + '\n\n' + content;
    }
    return content;
}
async function storeArtifactsInS3(sessionId, taskId, artifact) {
    const s3Key = `sessions/${sessionId}/tasks/${taskId}/artifacts/${Date.now()}.json`;
    const artifactData = {
        ...artifact,
        storedAt: new Date().toISOString(),
    };
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: s3Key,
        Body: JSON.stringify(artifactData, null, 2),
        ContentType: 'application/json',
        Metadata: {
            sessionId,
            taskId,
            agent: 'CodeCreator',
        },
    }));
    // Store individual files as well
    for (const file of artifact.files) {
        const fileKey = `sessions/${sessionId}/tasks/${taskId}/files/${file.path}`;
        await s3Client.send(new client_s3_1.PutObjectCommand({
            Bucket: ARTIFACTS_BUCKET,
            Key: fileKey,
            Body: file.content,
            ContentType: getContentType(file.language),
            Metadata: {
                sessionId,
                taskId,
                fileType: file.type,
                language: file.language,
            },
        }));
    }
    return s3Key;
}
function getContentType(language) {
    const contentTypes = {
        'typescript': 'text/typescript',
        'javascript': 'text/javascript',
        'html': 'text/html',
        'css': 'text/css',
        'json': 'application/json',
        'yaml': 'text/yaml',
        'markdown': 'text/markdown',
        'python': 'text/x-python',
        'sql': 'text/sql',
    };
    return contentTypes[language] || 'text/plain';
}
async function updateTaskStatus(sessionId, taskId, status) {
    await dynamoClient.send(new lib_dynamodb_1.UpdateCommand({
        TableName: TABLE_NAME,
        Key: {
            PK: `SESSION#${sessionId}`,
            SK: `TASK#${taskId}`,
        },
        UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, completedAt = :completedAt',
        ExpressionAttributeNames: {
            '#status': 'status',
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':updatedAt': new Date().toISOString(),
            ':completedAt': (status === 'completed' || status === 'failed') ? new Date().toISOString() : null,
        },
    }));
}
//# sourceMappingURL=data:application/json;base64,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