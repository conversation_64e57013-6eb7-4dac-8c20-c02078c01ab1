"use strict";
/**
 * Code Creator Agent
 * Generates code files based on specifications and individual tasks
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const schemas_1 = require("../../database/schemas");
const llm_client_1 = require("../../utils/llm-client");
const logger_1 = require("../../utils/logger");
const code_validator_1 = require("../../utils/code-validator");
const dynamoClient = lib_dynamodb_1.DynamoDBDocumentClient.from(new client_dynamodb_1.DynamoDBClient({}));
const s3Client = new client_s3_1.S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME;
const handler = async (event, context) => {
    const requestId = context.awsRequestId;
    logger_1.logger.info('Code Creator Agent started', { requestId, event });
    try {
        const { sessionId, specification, task, iteration } = event;
        // Update task status to in_progress
        await updateTaskStatus(sessionId, task.taskId, 'in_progress');
        // Generate code for the specific task
        const codeArtifact = await generateCodeForTask(specification, task, iteration, requestId);
        // Validate generated code
        const validationResult = await (0, code_validator_1.validateGeneratedCode)(codeArtifact.files);
        codeArtifact.errors.push(...validationResult.errors);
        codeArtifact.warnings.push(...validationResult.warnings);
        // Store artifacts in S3
        const s3Key = await storeArtifactsInS3(sessionId, task.taskId, codeArtifact);
        // Save artifact record to DynamoDB
        const artifactRecord = (0, schemas_1.createArtifactRecord)(sessionId, task.taskId, codeArtifact, s3Key);
        await dynamoClient.send(new lib_dynamodb_1.PutCommand({
            TableName: TABLE_NAME,
            Item: artifactRecord,
        }));
        // Update task status based on errors
        const finalStatus = codeArtifact.errors.length > 0 ? 'failed' : 'completed';
        await updateTaskStatus(sessionId, task.taskId, finalStatus);
        logger_1.logger.info('Code generation completed', {
            requestId,
            sessionId,
            taskId: task.taskId,
            filesGenerated: codeArtifact.files.length,
            errorsCount: codeArtifact.errors.length,
            warningsCount: codeArtifact.warnings.length
        });
        return codeArtifact;
    }
    catch (error) {
        logger_1.logger.error('Code Creator Agent failed', { requestId, error });
        // Update task status to failed
        if (event.task?.taskId) {
            await updateTaskStatus(event.sessionId, event.task.taskId, 'failed');
        }
        throw error;
    }
};
exports.handler = handler;
async function generateCodeForTask(specification, task, iteration, requestId) {
    const codeGenerationPrompt = `
You are an expert software developer tasked with generating production-ready code for a specific task.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

CURRENT TASK:
${JSON.stringify(task, null, 2)}

ITERATION: ${iteration}

Your task is to generate complete, production-ready code files for this specific task. Consider:

1. QUALITY REQUIREMENTS:
   - Write clean, maintainable, and well-documented code
   - Follow best practices and design patterns
   - Include proper error handling and validation
   - Add comprehensive comments and documentation
   - Ensure type safety (TypeScript where applicable)

2. SECURITY CONSIDERATIONS:
   - Implement proper input validation
   - Use secure authentication and authorization
   - Protect against common vulnerabilities (XSS, SQL injection, etc.)
   - Follow OWASP guidelines

3. PERFORMANCE & SCALABILITY:
   - Optimize for performance
   - Consider caching strategies
   - Implement proper database indexing
   - Use efficient algorithms and data structures

4. TESTING:
   - Include unit tests for critical functions
   - Add integration tests where appropriate
   - Ensure good test coverage

5. DEPLOYMENT & INFRASTRUCTURE:
   - Include necessary configuration files
   - Add Docker files if needed
   - Include CI/CD pipeline configurations
   - Add monitoring and logging

Generate the following for this task:
- All necessary source code files
- Configuration files
- Test files
- Documentation files
- Any other required files

Return your response as a JSON object with the following structure:
{
  "taskId": "string",
  "files": [
    {
      "path": "string",
      "content": "string",
      "type": "source|config|documentation|test",
      "language": "string"
    }
  ],
  "errors": [],
  "warnings": [],
  "metadata": {
    "generatedAt": "string",
    "agent": "CodeCreator",
    "version": "string"
  }
}

Focus specifically on the requirements of the current task while ensuring compatibility with the overall project specification.
`;
    try {
        const response = await (0, llm_client_1.invokeLLM)(codeGenerationPrompt, {
            temperature: 0.3,
            maxTokens: 8000,
            requestId,
        });
        const result = JSON.parse(response);
        // Validate and enhance the generated code
        const codeArtifact = {
            taskId: task.taskId,
            files: result.files || [],
            errors: result.errors || [],
            warnings: result.warnings || [],
            metadata: {
                generatedAt: new Date().toISOString(),
                agent: 'CodeCreator',
                version: '1.0.0',
                ...result.metadata,
            },
        };
        // Post-process generated files
        codeArtifact.files = await postProcessGeneratedFiles(codeArtifact.files, specification, task);
        return codeArtifact;
    }
    catch (error) {
        logger_1.logger.error('Code generation failed', { requestId, taskId: task.taskId, error });
        return {
            taskId: task.taskId,
            files: [],
            errors: [{
                    severity: 'critical',
                    message: `Code generation failed: ${error instanceof Error ? error.message : String(error)}`,
                    rule: 'generation_error',
                }],
            warnings: [],
            metadata: {
                generatedAt: new Date().toISOString(),
                agent: 'CodeCreator',
                version: '1.0.0',
            },
        };
    }
}
async function postProcessGeneratedFiles(files, specification, task) {
    return files.map(file => {
        // Add file headers and metadata
        let content = file.content;
        if (file.type === 'source' && (file.language === 'typescript' || file.language === 'javascript')) {
            const header = `/**
 * ${file.path}
 * Generated by AI Agent Workflow System
 * Task: ${task.taskName}
 * Project: ${specification.projectName}
 * Generated: ${new Date().toISOString()}
 */

`;
            content = header + content;
        }
        // Add proper imports and dependencies
        if (file.language === 'typescript' && file.type === 'source') {
            content = addTypeScriptImports(content, specification);
        }
        return {
            ...file,
            content,
        };
    });
}
function addTypeScriptImports(content, specification) {
    // Add common imports based on the tech stack
    const imports = [];
    if (specification.techStack.frontend.framework === 'React') {
        if (content.includes('React') && !content.includes("import React")) {
            imports.push("import React from 'react';");
        }
    }
    if (specification.techStack.backend.framework === 'Express.js') {
        if (content.includes('express') && !content.includes("import express")) {
            imports.push("import express from 'express';");
        }
    }
    if (imports.length > 0) {
        return imports.join('\n') + '\n\n' + content;
    }
    return content;
}
async function storeArtifactsInS3(sessionId, taskId, artifact) {
    const s3Key = `sessions/${sessionId}/tasks/${taskId}/artifacts/${Date.now()}.json`;
    const artifactData = {
        ...artifact,
        storedAt: new Date().toISOString(),
    };
    await s3Client.send(new client_s3_1.PutObjectCommand({
        Bucket: ARTIFACTS_BUCKET,
        Key: s3Key,
        Body: JSON.stringify(artifactData, null, 2),
        ContentType: 'application/json',
        Metadata: {
            sessionId,
            taskId,
            agent: 'CodeCreator',
        },
    }));
    // Store individual files as well
    for (const file of artifact.files) {
        const fileKey = `sessions/${sessionId}/tasks/${taskId}/files/${file.path}`;
        await s3Client.send(new client_s3_1.PutObjectCommand({
            Bucket: ARTIFACTS_BUCKET,
            Key: fileKey,
            Body: file.content,
            ContentType: getContentType(file.language),
            Metadata: {
                sessionId,
                taskId,
                fileType: file.type,
                language: file.language,
            },
        }));
    }
    return s3Key;
}
function getContentType(language) {
    const contentTypes = {
        'typescript': 'text/typescript',
        'javascript': 'text/javascript',
        'html': 'text/html',
        'css': 'text/css',
        'json': 'application/json',
        'yaml': 'text/yaml',
        'markdown': 'text/markdown',
        'python': 'text/x-python',
        'sql': 'text/sql',
    };
    return contentTypes[language] || 'text/plain';
}
async function updateTaskStatus(sessionId, taskId, status) {
    await dynamoClient.send(new lib_dynamodb_1.UpdateCommand({
        TableName: TABLE_NAME,
        Key: {
            PK: `SESSION#${sessionId}`,
            SK: `TASK#${taskId}`,
        },
        UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, completedAt = :completedAt',
        ExpressionAttributeNames: {
            '#status': 'status',
        },
        ExpressionAttributeValues: {
            ':status': status,
            ':updatedAt': new Date().toISOString(),
            ':completedAt': (status === 'completed' || status === 'failed') ? new Date().toISOString() : null,
        },
    }));
}
//# sourceMappingURL=data:application/json;base64,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