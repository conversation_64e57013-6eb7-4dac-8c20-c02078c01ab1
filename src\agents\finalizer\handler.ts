/**
 * Finalizer Agent
 * Generates README, deployment scripts, and final artifacts
 */

import { Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import archiver from 'archiver';
import { 
  FinalizerEvent, 
  FinalOutput, 
  CodeArtifact, 
  ReviewResult,
  DetailedSpecification,
  GeneratedFile
} from '../../types';
import { invokeLLM } from '../../utils/llm-client';
import { logger } from '../../utils/logger';
import { generateMermaidDiagram } from '../../utils/diagram-generator';

const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));
const s3Client = new S3Client({});
const TABLE_NAME = process.env.WORKFLOW_TABLE_NAME!;
const ARTIFACTS_BUCKET = process.env.ARTIFACTS_BUCKET_NAME!;

export const handler = async (
  event: FinalizerEvent,
  context: Context
): Promise<FinalOutput> => {
  const requestId = context.awsRequestId;
  logger.info('Finalizer Agent started', { requestId, event });

  try {
    const { sessionId, specification, artifacts, reviews } = event;

    // Generate comprehensive README
    const readme = await generateComprehensiveReadme(
      specification, 
      artifacts, 
      reviews, 
      requestId
    );

    // Generate deployment scripts
    const deploymentScript = await generateDeploymentScript(
      specification, 
      artifacts, 
      requestId
    );

    // Generate API documentation
    const apiDocs = await generateAPIDocumentation(
      specification, 
      artifacts, 
      requestId
    );

    // Generate architecture diagram
    const architectureDiagram = await generateArchitectureDiagram(
      specification, 
      artifacts, 
      requestId
    );

    // Create project bundle
    const projectBundle = await createProjectBundle(
      sessionId,
      specification,
      artifacts,
      readme,
      deploymentScript,
      apiDocs,
      architectureDiagram
    );

    // Store final artifacts
    const finalOutput = await storeFinalArtifacts(
      sessionId,
      projectBundle,
      readme,
      deploymentScript,
      apiDocs,
      architectureDiagram
    );

    // Update workflow state to completed
    await updateWorkflowState(sessionId, 'completed', finalOutput);

    logger.info('Finalization completed', { 
      requestId, 
      sessionId,
      bundleSize: projectBundle.size,
      filesCount: artifacts.reduce((sum, a) => sum + a.files.length, 0)
    });

    return finalOutput;

  } catch (error) {
    logger.error('Finalizer Agent failed', { requestId, error });
    
    // Update workflow state to failed
    await updateWorkflowState(event.sessionId, 'failed');
    
    throw error;
  }
};

async function generateComprehensiveReadme(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[],
  reviews: ReviewResult[],
  requestId: string
): Promise<string> {
  
  const readmePrompt = `
You are a technical documentation expert. Generate a comprehensive, professional README.md file for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
  taskId: a.taskId,
  files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language })),
  errorsCount: a.errors.length
})), null, 2)}

REVIEW SUMMARY:
${JSON.stringify(reviews.map(r => ({
  taskId: r.taskId,
  quality: r.overallQuality,
  errorsCount: r.errors.length,
  suggestionsCount: r.suggestions.length
})), null, 2)}

Create a comprehensive README.md that includes:

1. PROJECT OVERVIEW
   - Clear project description
   - Key features and capabilities
   - Technology stack
   - Architecture overview

2. GETTING STARTED
   - Prerequisites
   - Installation instructions
   - Quick start guide
   - Configuration setup

3. ARCHITECTURE
   - System architecture description
   - Component relationships
   - Data flow
   - Integration points

4. API DOCUMENTATION
   - Available endpoints
   - Request/response examples
   - Authentication requirements

5. DEPLOYMENT
   - Deployment options
   - Environment setup
   - CI/CD pipeline
   - Monitoring and logging

6. DEVELOPMENT
   - Development setup
   - Testing instructions
   - Contributing guidelines
   - Code structure

7. TROUBLESHOOTING
   - Common issues and solutions
   - Debugging tips
   - Performance optimization

8. ADDITIONAL RESOURCES
   - Links to documentation
   - Support information
   - License information

Make the README professional, comprehensive, and easy to follow. Use proper Markdown formatting with clear sections, code blocks, and examples.
`;

  try {
    const response = await invokeLLM(readmePrompt, {
      temperature: 0.3,
      maxTokens: 6000,
      requestId,
    });

    return response;

  } catch (error) {
    logger.error('README generation failed', { requestId, error });
    return generateFallbackReadme(specification);
  }
}

async function generateDeploymentScript(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[],
  requestId: string
): Promise<{ content: string; type: 'cdk' | 'cloudformation' | 'terraform' }> {
  
  const deploymentPrompt = `
Generate a comprehensive deployment script for this project based on the specification and generated artifacts.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
  taskId: a.taskId,
  files: a.files.map(f => ({ path: f.path, type: f.type, language: f.language }))
})), null, 2)}

Create a deployment script that:

1. Sets up all necessary AWS resources
2. Configures proper IAM roles and policies
3. Sets up networking and security groups
4. Configures databases and storage
5. Sets up monitoring and logging
6. Includes environment variables and secrets management
7. Configures auto-scaling and load balancing
8. Sets up CI/CD pipeline

Choose the most appropriate deployment tool (CDK, CloudFormation, or Terraform) based on the project requirements.

Return the deployment script as a complete, production-ready configuration.
`;

  try {
    const response = await invokeLLM(deploymentPrompt, {
      temperature: 0.2,
      maxTokens: 8000,
      requestId,
    });

    // Determine deployment type based on content
    let type: 'cdk' | 'cloudformation' | 'terraform' = 'cdk';
    if (response.includes('terraform')) type = 'terraform';
    else if (response.includes('AWSTemplateFormatVersion')) type = 'cloudformation';

    return { content: response, type };

  } catch (error) {
    logger.error('Deployment script generation failed', { requestId, error });
    return {
      content: generateFallbackDeploymentScript(specification),
      type: 'cdk'
    };
  }
}

async function generateAPIDocumentation(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[],
  requestId: string
): Promise<string> {
  
  const apiDocPrompt = `
Generate comprehensive API documentation in OpenAPI 3.0 format for this project.

PROJECT SPECIFICATION:
${JSON.stringify(specification, null, 2)}

GENERATED CODE ARTIFACTS:
${JSON.stringify(artifacts.map(a => ({
  taskId: a.taskId,
  files: a.files.filter(f => f.type === 'source').map(f => ({ path: f.path, language: f.language }))
})), null, 2)}

Create a complete OpenAPI specification that includes:

1. API information and metadata
2. Server configurations
3. All endpoints with detailed descriptions
4. Request/response schemas
5. Authentication and security schemes
6. Error response formats
7. Examples for all endpoints
8. Data models and components

Make sure the documentation is comprehensive and follows OpenAPI 3.0 standards.
`;

  try {
    const response = await invokeLLM(apiDocPrompt, {
      temperature: 0.2,
      maxTokens: 6000,
      requestId,
    });

    return response;

  } catch (error) {
    logger.error('API documentation generation failed', { requestId, error });
    return generateFallbackAPIDoc(specification);
  }
}

async function generateArchitectureDiagram(
  specification: DetailedSpecification,
  artifacts: CodeArtifact[],
  requestId: string
): Promise<string> {
  
  try {
    return await generateMermaidDiagram(specification, artifacts);
  } catch (error) {
    logger.error('Architecture diagram generation failed', { requestId, error });
    return generateFallbackDiagram(specification);
  }
}

async function createProjectBundle(
  sessionId: string,
  specification: DetailedSpecification,
  artifacts: CodeArtifact[],
  readme: string,
  deploymentScript: { content: string; type: string },
  apiDocs: string,
  architectureDiagram: string
): Promise<{ buffer: Buffer; size: number }> {
  
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } });
    const chunks: Buffer[] = [];

    archive.on('data', (chunk: Buffer) => chunks.push(chunk));
    archive.on('end', () => {
      const buffer = Buffer.concat(chunks);
      resolve({ buffer, size: buffer.length });
    });
    archive.on('error', reject);

    // Add README
    archive.append(readme, { name: 'README.md' });

    // Add deployment script
    const deploymentFileName = `deployment.${deploymentScript.type === 'terraform' ? 'tf' : 'ts'}`;
    archive.append(deploymentScript.content, { name: deploymentFileName });

    // Add API documentation
    archive.append(apiDocs, { name: 'api-docs.yaml' });

    // Add architecture diagram
    archive.append(architectureDiagram, { name: 'architecture.mmd' });

    // Add all generated files
    for (const artifact of artifacts) {
      for (const file of artifact.files) {
        archive.append(file.content, { name: file.path });
      }
    }

    // Add package.json if not already present
    const hasPackageJson = artifacts.some(a => 
      a.files.some(f => f.path === 'package.json')
    );
    
    if (!hasPackageJson && specification.techStack.frontend.framework.includes('React')) {
      const packageJson = generatePackageJson(specification);
      archive.append(packageJson, { name: 'package.json' });
    }

    archive.finalize();
  });
}

async function storeFinalArtifacts(
  sessionId: string,
  projectBundle: { buffer: Buffer; size: number },
  readme: string,
  deploymentScript: { content: string; type: string },
  apiDocs: string,
  architectureDiagram: string
): Promise<FinalOutput> {
  
  const timestamp = Date.now();
  
  // Store project bundle
  const bundleKey = `sessions/${sessionId}/final/project-bundle-${timestamp}.zip`;
  await s3Client.send(new PutObjectCommand({
    Bucket: ARTIFACTS_BUCKET,
    Key: bundleKey,
    Body: projectBundle.buffer,
    ContentType: 'application/zip',
    Metadata: {
      sessionId,
      type: 'project-bundle',
      size: projectBundle.size.toString(),
    },
  }));

  // Store README
  const readmeKey = `sessions/${sessionId}/final/README.md`;
  await s3Client.send(new PutObjectCommand({
    Bucket: ARTIFACTS_BUCKET,
    Key: readmeKey,
    Body: readme,
    ContentType: 'text/markdown',
  }));

  // Store deployment script
  const deploymentKey = `sessions/${sessionId}/final/deployment.${deploymentScript.type}`;
  await s3Client.send(new PutObjectCommand({
    Bucket: ARTIFACTS_BUCKET,
    Key: deploymentKey,
    Body: deploymentScript.content,
    ContentType: 'text/plain',
  }));

  // Generate download URL
  const downloadUrl = await getSignedUrl(
    s3Client,
    new GetObjectCommand({
      Bucket: ARTIFACTS_BUCKET,
      Key: bundleKey,
    }),
    { expiresIn: 3600 * 24 * 7 } // 7 days
  );

  return {
    projectBundle: {
      s3Key: bundleKey,
      downloadUrl,
    },
    readme: {
      s3Key: readmeKey,
      content: readme,
    },
    deploymentScript: {
      s3Key: deploymentKey,
      type: deploymentScript.type as 'cdk' | 'cloudformation' | 'terraform',
    },
    documentation: {
      apiDocs,
      architectureDiagram,
      userGuide: readme,
    },
  };
}

async function updateWorkflowState(
  sessionId: string, 
  status: 'completed' | 'failed',
  finalOutput?: FinalOutput
): Promise<void> {
  
  await dynamoClient.send(new UpdateCommand({
    TableName: TABLE_NAME,
    Key: {
      PK: `SESSION#${sessionId}`,
      SK: `WORKFLOW#${Date.now()}`,
    },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, finalOutput = :finalOutput',
    ExpressionAttributeNames: {
      '#status': 'status',
    },
    ExpressionAttributeValues: {
      ':status': status,
      ':updatedAt': new Date().toISOString(),
      ':finalOutput': finalOutput ? JSON.stringify(finalOutput) : null,
    },
  }));
}

// Fallback functions
function generateFallbackReadme(specification: DetailedSpecification): string {
  return `# ${specification.projectName}

${specification.description}

## Features

${specification.features.map(f => `- ${f.name}: ${f.description}`).join('\n')}

## Tech Stack

- Frontend: ${specification.techStack.frontend.framework}
- Backend: ${specification.techStack.backend.framework}
- Database: ${specification.techStack.backend.database}

## Getting Started

1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start the development server: \`npm run dev\`

## Deployment

Follow the deployment script instructions in the deployment files.
`;
}

function generateFallbackDeploymentScript(specification: DetailedSpecification): string {
  return `// CDK Deployment Script for ${specification.projectName}
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';

export class ${specification.projectName.replace(/[^a-zA-Z0-9]/g, '')}Stack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);
    
    // Add your AWS resources here
  }
}`;
}

function generateFallbackAPIDoc(specification: DetailedSpecification): string {
  return `openapi: 3.0.0
info:
  title: ${specification.projectName} API
  description: ${specification.description}
  version: 1.0.0
paths: {}`;
}

function generateFallbackDiagram(specification: DetailedSpecification): string {
  return `graph TD
    A[Frontend] --> B[Backend]
    B --> C[Database]`;
}

function generatePackageJson(specification: DetailedSpecification): string {
  return JSON.stringify({
    name: specification.projectName,
    version: "1.0.0",
    description: specification.description,
    scripts: {
      dev: "npm run dev",
      build: "npm run build",
      start: "npm start"
    },
    dependencies: {},
    devDependencies: {}
  }, null, 2);
}
