/**
 * CloudWatch Dashboard for AI Agent Workflow System
 */

import * as cdk from 'aws-cdk-lib';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';

export interface DashboardProps {
  lambdaFunctions: {
    descriptionEnhancer: lambda.Function;
    codeCreator: lambda.Function;
    reviewRefine: lambda.Function;
    finalizer: lambda.Function;
  };
  stateMachine: stepfunctions.StateMachine;
  workflowTable: dynamodb.Table;
  api: apigateway.RestApi;
}

export class WorkflowDashboard extends Construct {
  public readonly dashboard: cloudwatch.Dashboard;

  constructor(scope: Construct, id: string, props: DashboardProps) {
    super(scope, id);

    this.dashboard = new cloudwatch.Dashboard(this, 'AIAgentWorkflowDashboard', {
      dashboardName: 'AI-Agent-Workflow-System',
      defaultInterval: cdk.Duration.hours(1),
    });

    this.addApiGatewayWidgets(props.api);
    this.addLambdaWidgets(props.lambdaFunctions);
    this.addStepFunctionsWidgets(props.stateMachine);
    this.addDynamoDBWidgets(props.workflowTable);
    this.addSystemOverviewWidgets(props);
  }

  private addApiGatewayWidgets(api: apigateway.RestApi): void {
    // API Gateway metrics
    const apiRequestsWidget = new cloudwatch.GraphWidget({
      title: 'API Gateway Requests',
      left: [
        api.metricCount({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      ],
      right: [
        api.metricLatency({
          period: cdk.Duration.minutes(5),
          statistic: 'Average',
        }),
      ],
      width: 12,
      height: 6,
    });

    const apiErrorsWidget = new cloudwatch.GraphWidget({
      title: 'API Gateway Errors',
      left: [
        api.metricClientError({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        api.metricServerError({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      ],
      width: 12,
      height: 6,
    });

    this.dashboard.addWidgets(apiRequestsWidget, apiErrorsWidget);
  }

  private addLambdaWidgets(lambdaFunctions: {
    descriptionEnhancer: lambda.Function;
    codeCreator: lambda.Function;
    reviewRefine: lambda.Function;
    finalizer: lambda.Function;
  }): void {
    
    // Lambda invocations
    const lambdaInvocationsWidget = new cloudwatch.GraphWidget({
      title: 'Lambda Function Invocations',
      left: Object.values(lambdaFunctions).map(func =>
        func.metricInvocations({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        })
      ),
      width: 12,
      height: 6,
    });

    // Lambda errors
    const lambdaErrorsWidget = new cloudwatch.GraphWidget({
      title: 'Lambda Function Errors',
      left: Object.values(lambdaFunctions).map(func =>
        func.metricErrors({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        })
      ),
      width: 12,
      height: 6,
    });

    // Lambda duration
    const lambdaDurationWidget = new cloudwatch.GraphWidget({
      title: 'Lambda Function Duration',
      left: Object.values(lambdaFunctions).map(func =>
        func.metricDuration({
          period: cdk.Duration.minutes(5),
          statistic: 'Average',
        })
      ),
      width: 12,
      height: 6,
    });

    // Lambda throttles
    const lambdaThrottlesWidget = new cloudwatch.GraphWidget({
      title: 'Lambda Function Throttles',
      left: Object.values(lambdaFunctions).map(func =>
        func.metricThrottles({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        })
      ),
      width: 12,
      height: 6,
    });

    this.dashboard.addWidgets(
      lambdaInvocationsWidget,
      lambdaErrorsWidget,
      lambdaDurationWidget,
      lambdaThrottlesWidget
    );
  }

  private addStepFunctionsWidgets(stateMachine: stepfunctions.StateMachine): void {
    // Step Functions executions
    const sfnExecutionsWidget = new cloudwatch.GraphWidget({
      title: 'Step Functions Executions',
      left: [
        stateMachine.metricStarted({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        stateMachine.metricSucceeded({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        stateMachine.metricFailed({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      ],
      width: 12,
      height: 6,
    });

    // Step Functions duration
    const sfnDurationWidget = new cloudwatch.GraphWidget({
      title: 'Step Functions Execution Duration',
      left: [
        new cloudwatch.Metric({
          namespace: 'AWS/States',
          metricName: 'ExecutionTime',
          dimensionsMap: {
            StateMachineArn: stateMachine.stateMachineArn,
          },
          period: cdk.Duration.minutes(5),
          statistic: 'Average',
        }),
      ],
      width: 12,
      height: 6,
    });

    this.dashboard.addWidgets(sfnExecutionsWidget, sfnDurationWidget);
  }

  private addDynamoDBWidgets(table: dynamodb.Table): void {
    // DynamoDB read/write capacity
    const dynamoCapacityWidget = new cloudwatch.GraphWidget({
      title: 'DynamoDB Read/Write Capacity',
      left: [
        table.metricConsumedReadCapacityUnits({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        table.metricConsumedWriteCapacityUnits({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      ],
      width: 12,
      height: 6,
    });

    // DynamoDB throttles
    const dynamoThrottlesWidget = new cloudwatch.GraphWidget({
      title: 'DynamoDB Throttles',
      left: [
        table.metricThrottledRequests({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      ],
      width: 12,
      height: 6,
    });

    this.dashboard.addWidgets(dynamoCapacityWidget, dynamoThrottlesWidget);
  }

  private addSystemOverviewWidgets(props: DashboardProps): void {
    // System health overview
    const systemHealthWidget = new cloudwatch.SingleValueWidget({
      title: 'System Health Overview',
      metrics: [
        props.api.metricCount({
          period: cdk.Duration.hours(1),
          statistic: 'Sum',
        }),
        props.stateMachine.metricSucceeded({
          period: cdk.Duration.hours(1),
          statistic: 'Sum',
        }),
      ],
      width: 12,
      height: 6,
    });

    // Error rate overview
    const errorRateWidget = new cloudwatch.GraphWidget({
      title: 'Overall Error Rate',
      left: [
        new cloudwatch.MathExpression({
          expression: '(api_errors + lambda_errors + sfn_errors) / (api_requests + lambda_invocations + sfn_executions) * 100',
          usingMetrics: {
            api_errors: props.api.metricServerError({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
            api_requests: props.api.metricCount({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
            lambda_errors: props.lambdaFunctions.descriptionEnhancer.metricErrors({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
            lambda_invocations: props.lambdaFunctions.descriptionEnhancer.metricInvocations({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
            sfn_errors: props.stateMachine.metricFailed({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
            sfn_executions: props.stateMachine.metricStarted({
              period: cdk.Duration.minutes(5),
              statistic: 'Sum',
            }),
          },
          label: 'Error Rate (%)',
        }),
      ],
      width: 12,
      height: 6,
    });

    this.dashboard.addWidgets(systemHealthWidget, errorRateWidget);
  }
}

export function createCustomMetrics(scope: Construct): {
  workflowCompletionRate: cloudwatch.Metric;
  averageProcessingTime: cloudwatch.Metric;
  codeQualityScore: cloudwatch.Metric;
} {
  
  const workflowCompletionRate = new cloudwatch.Metric({
    namespace: 'AIAgentWorkflow/Custom',
    metricName: 'WorkflowCompletionRate',
    period: cdk.Duration.minutes(5),
    statistic: 'Average',
  });

  const averageProcessingTime = new cloudwatch.Metric({
    namespace: 'AIAgentWorkflow/Custom',
    metricName: 'AverageProcessingTime',
    period: cdk.Duration.minutes(5),
    statistic: 'Average',
  });

  const codeQualityScore = new cloudwatch.Metric({
    namespace: 'AIAgentWorkflow/Custom',
    metricName: 'CodeQualityScore',
    period: cdk.Duration.minutes(5),
    statistic: 'Average',
  });

  return {
    workflowCompletionRate,
    averageProcessingTime,
    codeQualityScore,
  };
}

export function createAlerts(
  scope: Construct,
  props: DashboardProps,
  notificationTopic: string
): void {
  
  // High error rate alarm
  const highErrorRateAlarm = new cloudwatch.Alarm(scope, 'HighErrorRateAlarm', {
    metric: new cloudwatch.MathExpression({
      expression: '(errors / requests) * 100',
      usingMetrics: {
        errors: props.api.metricServerError({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        requests: props.api.metricCount({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
      },
    }),
    threshold: 5, // 5% error rate
    evaluationPeriods: 2,
    treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
  });

  // Long execution time alarm
  const longExecutionAlarm = new cloudwatch.Alarm(scope, 'LongExecutionAlarm', {
    metric: new cloudwatch.Metric({
      namespace: 'AWS/States',
      metricName: 'ExecutionTime',
      dimensionsMap: {
        StateMachineArn: props.stateMachine.stateMachineArn,
      },
      period: cdk.Duration.minutes(5),
      statistic: 'Average',
    }),
    threshold: 3600000, // 1 hour in milliseconds
    evaluationPeriods: 1,
    treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
  });

  // DynamoDB throttle alarm
  const dynamoThrottleAlarm = new cloudwatch.Alarm(scope, 'DynamoThrottleAlarm', {
    metric: props.workflowTable.metricThrottledRequests({
      period: cdk.Duration.minutes(5),
      statistic: 'Sum',
    }),
    threshold: 1,
    evaluationPeriods: 1,
    treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
  });
}
