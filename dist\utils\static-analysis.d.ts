/**
 * Static analysis utilities
 */
import { GeneratedFile, CodeError, Suggestion } from '../types';
export interface StaticAnalysisResult {
    errors: CodeError[];
    suggestions: Suggestion[];
    metrics: {
        complexity: number;
        maintainability: number;
        testCoverage: number;
    };
}
export declare function runStaticAnalysis(files: GeneratedFile[]): Promise<StaticAnalysisResult>;
