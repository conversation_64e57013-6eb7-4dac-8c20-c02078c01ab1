/**
 * DynamoDB table schemas and data access patterns
 */
import { AttributeType, BillingMode, ProjectionType } from 'aws-cdk-lib/aws-dynamodb';
export interface WorkflowStateRecord {
    PK: string;
    SK: string;
    GSI1PK: string;
    GSI1SK: string;
    sessionId: string;
    currentIteration: number;
    maxIterations: number;
    status: 'initializing' | 'enhancing' | 'creating' | 'reviewing' | 'finalizing' | 'completed' | 'failed';
    specification?: string;
    taskPlan?: string;
    finalOutput?: string;
    createdAt: string;
    updatedAt: string;
    ttl?: number;
}
export interface TaskRecord {
    PK: string;
    SK: string;
    GSI1PK: string;
    GSI1SK: string;
    GSI2PK: string;
    GSI2SK: string;
    sessionId: string;
    taskId: string;
    taskName: string;
    description: string;
    agent: 'CodeCreator' | 'ReviewRefine' | 'Finalizer';
    dependencies: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    priority: number;
    estimatedDuration?: number;
    metadata: string;
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
}
export interface ArtifactRecord {
    PK: string;
    SK: string;
    GSI1PK: string;
    GSI1SK: string;
    sessionId: string;
    taskId: string;
    s3Key: string;
    files: string;
    errors: string;
    warnings: string;
    metadata: string;
    createdAt: string;
    version: string;
}
export interface ReviewRecord {
    PK: string;
    SK: string;
    GSI1PK: string;
    GSI1SK: string;
    sessionId: string;
    taskId: string;
    overallQuality: 'excellent' | 'good' | 'fair' | 'poor';
    errors: string;
    suggestions: string;
    securityIssues: string;
    performanceIssues: string;
    updatedSpecDelta?: string;
    updatedTasks?: string;
    createdAt: string;
    reviewedBy: string;
}
export interface UserSessionRecord {
    PK: string;
    SK: string;
    GSI1PK: string;
    GSI1SK: string;
    userId: string;
    sessionId: string;
    userRequest: string;
    status: string;
    createdAt: string;
    lastAccessedAt: string;
    ttl?: number;
}
export declare const WORKFLOW_TABLE_CONFIG: {
    tableName: string;
    partitionKey: {
        name: string;
        type: AttributeType;
    };
    sortKey: {
        name: string;
        type: AttributeType;
    };
    billingMode: BillingMode;
    timeToLiveAttribute: string;
    globalSecondaryIndexes: {
        indexName: string;
        partitionKey: {
            name: string;
            type: AttributeType;
        };
        sortKey: {
            name: string;
            type: AttributeType;
        };
        projectionType: ProjectionType;
    }[];
    pointInTimeRecovery: boolean;
    encryption: string;
};
export declare const ACCESS_PATTERNS: {
    getWorkflowState: (sessionId: string) => {
        PK: string;
        SK: {
            beginsWith: string;
        };
    };
    getTasksBySession: (sessionId: string) => {
        PK: string;
        SK: {
            beginsWith: string;
        };
    };
    getTasksByStatus: (status: string) => {
        IndexName: string;
        GSI1PK: string;
    };
    getTasksByAgent: (agent: string, status?: string) => {
        IndexName: string;
        GSI2PK: string;
        GSI2SK: {
            beginsWith: string;
        } | undefined;
    };
    getArtifactsBySession: (sessionId: string) => {
        PK: string;
        SK: {
            beginsWith: string;
        };
    };
    getArtifactsByTask: (taskId: string) => {
        IndexName: string;
        GSI1PK: string;
    };
    getReviewsBySession: (sessionId: string) => {
        PK: string;
        SK: {
            beginsWith: string;
        };
    };
    getReviewsByTask: (taskId: string) => {
        IndexName: string;
        GSI1PK: string;
    };
    getUserSessions: (userId: string) => {
        PK: string;
        SK: {
            beginsWith: string;
        };
    };
    getSessionsByDate: (date: string) => {
        IndexName: string;
        GSI1PK: string;
    };
};
export declare const createWorkflowStateRecord: (sessionId: string, workflowState: any, ttlHours?: number) => WorkflowStateRecord;
export declare const createTaskRecord: (sessionId: string, task: any) => TaskRecord;
export declare const createArtifactRecord: (sessionId: string, taskId: string, artifact: any, s3Key: string) => ArtifactRecord;
export declare const createReviewRecord: (sessionId: string, taskId: string, review: any, reviewedBy: string) => ReviewRecord;
export declare const createUserSessionRecord: (userId: string, sessionId: string, userRequest: any, ttlHours?: number) => UserSessionRecord;
