/**
 * Main CDK Stack for AI Agent Workflow System
 */

import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
// import * as nodejs from 'aws-cdk-lib/aws-lambda-nodejs'; // Not needed for regular Lambda functions
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as sfnTasks from 'aws-cdk-lib/aws-stepfunctions-tasks';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as xray from 'aws-cdk-lib/aws-xray';
import { Construct } from 'constructs';
import { WORKFLOW_TABLE_CONFIG } from '../database/schemas';

export class AIAgentWorkflowStack extends cdk.Stack {
  public readonly workflowTable: dynamodb.Table;
  public readonly artifactsBucket: s3.Bucket;
  public readonly api: apigateway.RestApi;
  public readonly stateMachine: stepfunctions.StateMachine;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create KMS key for encryption
    const encryptionKey = new kms.Key(this, 'EncryptionKey', {
      description: 'KMS key for AI Agent Workflow System',
      enableKeyRotation: true,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
    });

    // Create DynamoDB table
    this.workflowTable = this.createDynamoDBTable(encryptionKey);

    // Create S3 bucket for artifacts
    this.artifactsBucket = this.createS3Bucket(encryptionKey);

    // Create Lambda functions
    const lambdaFunctions = this.createLambdaFunctions();

    // Create Step Functions state machine
    this.stateMachine = this.createStateMachine(lambdaFunctions);

    // Create API Gateway
    this.api = this.createApiGateway(lambdaFunctions.descriptionEnhancer, this.stateMachine);

    // Create SNS topic for error notifications
    const errorTopic = this.createErrorNotificationTopic(encryptionKey);

    // Create CloudWatch dashboards and alarms
    this.createMonitoring(lambdaFunctions, this.stateMachine);

    // Output important values
    this.createOutputs();
  }

  private createDynamoDBTable(encryptionKey: kms.Key): dynamodb.Table {
    const table = new dynamodb.Table(this, 'WorkflowTable', {
      tableName: WORKFLOW_TABLE_CONFIG.tableName,
      partitionKey: WORKFLOW_TABLE_CONFIG.partitionKey,
      sortKey: WORKFLOW_TABLE_CONFIG.sortKey,
      billingMode: WORKFLOW_TABLE_CONFIG.billingMode,
      timeToLiveAttribute: WORKFLOW_TABLE_CONFIG.timeToLiveAttribute,
      encryption: dynamodb.TableEncryption.CUSTOMER_MANAGED,
      encryptionKey,
      pointInTimeRecoverySpecification: {
        pointInTimeRecoveryEnabled: WORKFLOW_TABLE_CONFIG.pointInTimeRecovery,
      },
      removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
    });

    // Add Global Secondary Indexes
    WORKFLOW_TABLE_CONFIG.globalSecondaryIndexes.forEach((gsi, index) => {
      table.addGlobalSecondaryIndex({
        indexName: gsi.indexName,
        partitionKey: gsi.partitionKey,
        sortKey: gsi.sortKey,
        projectionType: gsi.projectionType,
      });
    });

    return table;
  }

  private createS3Bucket(encryptionKey: kms.Key): s3.Bucket {
    const bucket = new s3.Bucket(this, 'ArtifactsBucket', {
      bucketName: `ai-agent-artifacts-${this.account}-${this.region}`,
      encryption: s3.BucketEncryption.KMS,
      encryptionKey,
      versioned: true,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // Change to RETAIN for production
      autoDeleteObjects: true, // Remove for production
      lifecycleRules: [
        {
          id: 'DeleteOldVersions',
          enabled: true,
          noncurrentVersionExpiration: cdk.Duration.days(30),
        },
        {
          id: 'TransitionToIA',
          enabled: true,
          transitions: [
            {
              storageClass: s3.StorageClass.INFREQUENT_ACCESS,
              transitionAfter: cdk.Duration.days(30),
            },
            {
              storageClass: s3.StorageClass.GLACIER,
              transitionAfter: cdk.Duration.days(90),
            },
          ],
        },
      ],
    });

    // Add CORS configuration
    bucket.addCorsRule({
      allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.PUT, s3.HttpMethods.POST],
      allowedOrigins: ['*'], // Restrict this in production
      allowedHeaders: ['*'],
      maxAge: 3000,
    });

    return bucket;
  }

  private createLambdaFunctions(): {
    descriptionEnhancer: lambda.Function;
    codeCreator: lambda.Function;
    reviewRefine: lambda.Function;
    finalizer: lambda.Function;
  } {
    // Common Lambda configuration (not used with inline functions)
    const commonLambdaProps = {
      runtime: lambda.Runtime.NODEJS_20_X,
      timeout: cdk.Duration.minutes(15),
      memorySize: 1024,
      environment: {
        WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
        ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
        LOG_LEVEL: 'info',
      },
      bundling: {
        minify: false,
        sourceMap: false,
        target: 'es2020',
        externalModules: ['aws-sdk', '@aws-sdk/*'],
        nodeModules: ['@google/generative-ai', 'uuid', 'archiver'],
        platform: 'node',
        format: 'cjs',
        mainFields: ['main', 'module'],
        conditions: ['node'],
        forceDockerBundling: false,
      },
      tracing: lambda.Tracing.ACTIVE,
      logRetention: logs.RetentionDays.ONE_WEEK,
    };

    // Description Enhancer Lambda
    const descriptionEnhancer = new lambda.Function(this, 'DescriptionEnhancerFunction', {
      runtime: lambda.Runtime.NODEJS_20_X,
      timeout: cdk.Duration.minutes(15),
      memorySize: 1024,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Description Enhancer called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              sessionId: 'test-session-' + Date.now(),
              specification: {
                projectName: 'Generated Project',
                description: 'A test project generated by AI Agent Workflow',
                features: ['Authentication', 'API', 'Database'],
                techStack: {
                  frontend: { framework: 'React', language: 'TypeScript' },
                  backend: { framework: 'Express', language: 'Node.js' }
                }
              },
              taskPlan: {
                tasks: [{
                  taskId: 'task-1',
                  taskName: 'Setup Project Structure',
                  description: 'Create basic project structure',
                  status: 'pending'
                }]
              },
              status: 'enhancement_completed'
            })
          };
        };
      `),
      description: 'Enhances user descriptions and creates detailed specifications',
      environment: {
        WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
        ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
        LOG_LEVEL: 'info',
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
        DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
      },
      tracing: lambda.Tracing.ACTIVE,
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // Code Creator Lambda
    const codeCreator = new lambda.Function(this, 'CodeCreatorFunction', {
      runtime: lambda.Runtime.NODEJS_20_X,
      timeout: cdk.Duration.minutes(15),
      memorySize: 2048,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Code Creator called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              artifacts: [{
                path: 'src/index.js',
                content: 'console.log("Hello World!");',
                type: 'source',
                language: 'javascript'
              }],
              status: 'code_generation_completed'
            })
          };
        };
      `),
      description: 'Generates code files based on specifications and tasks',
      environment: {
        WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
        ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
        LOG_LEVEL: 'info',
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
        DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
      },
      tracing: lambda.Tracing.ACTIVE,
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // Review & Refine Lambda
    const reviewRefine = new lambda.Function(this, 'ReviewRefineFunction', {
      runtime: lambda.Runtime.NODEJS_20_X,
      timeout: cdk.Duration.minutes(10),
      memorySize: 1536,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Review & Refine called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              reviewResults: {
                errors: [],
                warnings: [],
                suggestions: ['Code looks good!'],
                qualityScore: 85
              },
              status: 'review_completed'
            })
          };
        };
      `),
      description: 'Reviews and refines generated code',
      environment: {
        WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
        ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
        LOG_LEVEL: 'info',
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
        DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
      },
      tracing: lambda.Tracing.ACTIVE,
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // Finalizer Lambda
    const finalizer = new lambda.Function(this, 'FinalizerFunction', {
      runtime: lambda.Runtime.NODEJS_20_X,
      timeout: cdk.Duration.minutes(10),
      memorySize: 1536,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
        exports.handler = async (event) => {
          console.log('Finalizer called with:', JSON.stringify(event, null, 2));
          return {
            statusCode: 200,
            body: JSON.stringify({
              finalOutput: {
                downloadUrl: 'https://example.com/download/project.zip',
                documentation: 'Project documentation generated successfully',
                deploymentInstructions: 'Run npm install && npm start'
              },
              status: 'finalization_completed'
            })
          };
        };
      `),
      description: 'Generates final artifacts and documentation',
      environment: {
        WORKFLOW_TABLE_NAME: this.workflowTable.tableName,
        ARTIFACTS_BUCKET_NAME: this.artifactsBucket.bucketName,
        LOG_LEVEL: 'info',
        GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
        OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
        DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY || '',
      },
      tracing: lambda.Tracing.ACTIVE,
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // Grant permissions
    this.workflowTable.grantReadWriteData(descriptionEnhancer);
    this.workflowTable.grantReadWriteData(codeCreator);
    this.workflowTable.grantReadWriteData(reviewRefine);
    this.workflowTable.grantReadWriteData(finalizer);

    this.artifactsBucket.grantReadWrite(codeCreator);
    this.artifactsBucket.grantReadWrite(reviewRefine);
    this.artifactsBucket.grantReadWrite(finalizer);

    return {
      descriptionEnhancer,
      codeCreator,
      reviewRefine,
      finalizer,
    };
  }

  private createStateMachine(lambdaFunctions: {
    descriptionEnhancer: lambda.Function;
    codeCreator: lambda.Function;
    reviewRefine: lambda.Function;
    finalizer: lambda.Function;
  }): stepfunctions.StateMachine {
    
    // Create Step Functions tasks
    const enhanceDescriptionTask = new sfnTasks.LambdaInvoke(this, 'EnhanceDescriptionTask', {
      lambdaFunction: lambdaFunctions.descriptionEnhancer,
      outputPath: '$.Payload',
    });

    const createCodeTask = new sfnTasks.LambdaInvoke(this, 'CreateCodeTask', {
      lambdaFunction: lambdaFunctions.codeCreator,
      outputPath: '$.Payload',
    });

    const reviewRefineTask = new sfnTasks.LambdaInvoke(this, 'ReviewRefineTask', {
      lambdaFunction: lambdaFunctions.reviewRefine,
      outputPath: '$.Payload',
    });

    const finalizeTask = new sfnTasks.LambdaInvoke(this, 'FinalizeTask', {
      lambdaFunction: lambdaFunctions.finalizer,
      outputPath: '$.Payload',
    });

    // Create the state machine definition
    const definition = this.createStateMachineDefinition(
      enhanceDescriptionTask,
      createCodeTask,
      reviewRefineTask,
      finalizeTask,
      lambdaFunctions
    );

    // Create the state machine
    const stateMachine = new stepfunctions.StateMachine(this, 'WorkflowStateMachine', {
      definition,
      timeout: cdk.Duration.hours(2),
      tracingEnabled: true,
      logs: {
        destination: new logs.LogGroup(this, 'StateMachineLogGroup', {
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
        level: stepfunctions.LogLevel.ALL,
      },
    });

    // Grant permissions to invoke Lambda functions
    lambdaFunctions.descriptionEnhancer.grantInvoke(stateMachine);
    lambdaFunctions.codeCreator.grantInvoke(stateMachine);
    lambdaFunctions.reviewRefine.grantInvoke(stateMachine);
    lambdaFunctions.finalizer.grantInvoke(stateMachine);

    return stateMachine;
  }

  private createStateMachineDefinition(
    enhanceTask: sfnTasks.LambdaInvoke,
    createTask: sfnTasks.LambdaInvoke,
    reviewTask: sfnTasks.LambdaInvoke,
    finalizeTask: sfnTasks.LambdaInvoke,
    lambdaFunctions: {
      descriptionEnhancer: lambda.Function;
      codeCreator: lambda.Function;
      reviewRefine: lambda.Function;
      finalizer: lambda.Function;
    }
  ): stepfunctions.IChainable {
    
    // Initialize workflow
    const initializeWorkflow = new stepfunctions.Pass(this, 'InitializeWorkflow', {
      parameters: {
        'sessionId.$': '$.sessionId',
        'userRequest.$': '$.userRequest',
        'currentIteration': 0,
        'maxIterations': 5,
        'status': 'initializing',
      },
    });

    // Prepare task execution
    const prepareTaskExecution = new stepfunctions.Pass(this, 'PrepareTaskExecution', {
      parameters: {
        'sessionId.$': '$.sessionId',
        'specification.$': '$.specification',
        'taskPlan.$': '$.taskPlan',
        'currentIteration.$': '$.currentIteration',
        'maxIterations.$': '$.maxIterations',
        'artifacts': [],
        'reviews': [],
      },
    });

    // Execute tasks in parallel (simplified for this example)
    const executeTasksMap = new stepfunctions.Map(this, 'ExecuteTasksMap', {
      itemsPath: '$.taskPlan.tasks',
      maxConcurrency: 3,
      parameters: {
        'sessionId.$': '$.sessionId',
        'specification.$': '$.specification',
        'task.$': '$$.Map.Item.Value',
        'iteration.$': '$.currentIteration',
      },
    });

    executeTasksMap.iterator(createTask);

    // Collect artifacts
    const collectArtifacts = new stepfunctions.Pass(this, 'CollectArtifacts', {
      parameters: {
        'sessionId.$': '$.sessionId',
        'specification.$': '$.specification',
        'taskPlan.$': '$.taskPlan',
        'artifacts.$': '$[*].Payload',
        'currentIteration.$': '$.currentIteration',
        'maxIterations.$': '$.maxIterations',
      },
    });

    // Workflow completed
    const workflowCompleted = new stepfunctions.Pass(this, 'WorkflowCompleted', {
      parameters: {
        'sessionId.$': '$.sessionId',
        'status': 'completed',
        'finalOutput.$': '$.finalOutput',
        'message': 'Workflow completed successfully',
        'completedAt.$': '$$.State.EnteredTime',
      },
    });

    // Create separate finalize tasks to avoid chaining conflicts
    const finalizeTaskForMaxIterations = new sfnTasks.LambdaInvoke(this, 'FinalizeTaskMaxIterations', {
      lambdaFunction: lambdaFunctions.finalizer,
      outputPath: '$.Payload',
    });

    const finalizeTaskForNormal = new sfnTasks.LambdaInvoke(this, 'FinalizeTaskNormal', {
      lambdaFunction: lambdaFunctions.finalizer,
      outputPath: '$.Payload',
    });

    // Evaluate review results
    const evaluateReviewResults = new stepfunctions.Choice(this, 'EvaluateReviewResults')
      .when(
        stepfunctions.Condition.and(
          stepfunctions.Condition.isPresent('$.reviewResults[0].errors'),
          stepfunctions.Condition.numberLessThan('$.currentIteration', 5)
        ),
        new stepfunctions.Pass(this, 'PrepareNextIteration', {
          parameters: {
            'sessionId.$': '$.sessionId',
            'specification.$': '$.specification',
            'taskPlan.$': '$.taskPlan',
            'currentIteration.$': 'States.MathAdd($.currentIteration, 1)',
            'maxIterations.$': '$.maxIterations',
            'artifacts.$': '$.artifacts',
            'reviews.$': '$.reviewResults',
          },
        }).next(prepareTaskExecution)
      )
      .when(
        stepfunctions.Condition.numberGreaterThanEquals('$.currentIteration', 5),
        new stepfunctions.Pass(this, 'MaxIterationsReached', {
          parameters: {
            'sessionId.$': '$.sessionId',
            'status': 'max_iterations_reached',
            'message': 'Maximum iterations reached. Proceeding with current artifacts.',
            'artifacts.$': '$.artifacts',
            'reviews.$': '$.reviewResults',
          },
        }).next(finalizeTaskForMaxIterations).next(workflowCompleted)
      )
      .otherwise(finalizeTaskForNormal.next(workflowCompleted));



    // Chain the states together
    return initializeWorkflow
      .next(enhanceTask)
      .next(prepareTaskExecution)
      .next(executeTasksMap)
      .next(collectArtifacts)
      .next(reviewTask)
      .next(evaluateReviewResults);
  }

  private createApiGateway(
    descriptionEnhancerFunction: lambda.Function,
    stateMachine: stepfunctions.StateMachine
  ): apigateway.RestApi {

    // Create API Gateway
    const api = new apigateway.RestApi(this, 'WorkflowApi', {
      restApiName: 'AI Agent Workflow API',
      description: 'API for AI Agent Workflow System',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS, // Restrict in production
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: ['Content-Type', 'X-Amz-Date', 'Authorization', 'X-Api-Key'],
      },
      deployOptions: {
        stageName: 'prod',
        tracingEnabled: true,
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        dataTraceEnabled: true,
        metricsEnabled: true,
      },
      cloudWatchRole: true,
    });

    // Create API Key for authentication
    const apiKey = new apigateway.ApiKey(this, 'WorkflowApiKey', {
      description: 'API Key for AI Agent Workflow System',
    });

    // Create usage plan
    const usagePlan = new apigateway.UsagePlan(this, 'WorkflowUsagePlan', {
      name: 'AI Agent Workflow Usage Plan',
      description: 'Usage plan for AI Agent Workflow API',
      throttle: {
        rateLimit: 100,
        burstLimit: 200,
      },
      quota: {
        limit: 10000,
        period: apigateway.Period.MONTH,
      },
    });

    usagePlan.addApiKey(apiKey);
    usagePlan.addApiStage({
      stage: api.deploymentStage,
    });

    // Create Lambda integration for description enhancement
    const descriptionEnhancerIntegration = new apigateway.LambdaIntegration(
      descriptionEnhancerFunction,
      {
        requestTemplates: {
          'application/json': JSON.stringify({
            userRequest: '$input.json("$")',
            sessionId: '$context.requestId',
            userId: '$context.identity.apiKey',
          }),
        },
        integrationResponses: [
          {
            statusCode: '200',
            responseParameters: {
              'method.response.header.Access-Control-Allow-Origin': "'*'",
            },
          },
          {
            statusCode: '400',
            selectionPattern: '.*"statusCode":400.*',
            responseParameters: {
              'method.response.header.Access-Control-Allow-Origin': "'*'",
            },
          },
          {
            statusCode: '500',
            selectionPattern: '.*"statusCode":500.*',
            responseParameters: {
              'method.response.header.Access-Control-Allow-Origin': "'*'",
            },
          },
        ],
      }
    );

    // Create Step Functions integration for workflow execution
    const stepFunctionsRole = new iam.Role(this, 'StepFunctionsApiRole', {
      assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      inlinePolicies: {
        StepFunctionsExecutionPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: ['states:StartExecution'],
              resources: [stateMachine.stateMachineArn],
            }),
          ],
        }),
      },
    });

    const stepFunctionsIntegration = new apigateway.AwsIntegration({
      service: 'states',
      action: 'StartExecution',
      integrationHttpMethod: 'POST',
      options: {
        credentialsRole: stepFunctionsRole,
        requestTemplates: {
          'application/json': JSON.stringify({
            stateMachineArn: stateMachine.stateMachineArn,
            input: JSON.stringify({
              sessionId: '$context.requestId',
              userRequest: '$input.json("$")',
            }),
          }),
        },
        integrationResponses: [
          {
            statusCode: '200',
            responseParameters: {
              'method.response.header.Access-Control-Allow-Origin': "'*'",
            },
            responseTemplates: {
              'application/json': JSON.stringify({
                executionArn: '$input.json("$.executionArn")',
                startDate: '$input.json("$.startDate")',
                message: 'Workflow started successfully',
              }),
            },
          },
        ],
      },
    });

    // Create API resources and methods
    const enhanceResource = api.root.addResource('enhance');
    enhanceResource.addMethod('POST', descriptionEnhancerIntegration, {
      apiKeyRequired: true,
      methodResponses: [
        { statusCode: '200' },
        { statusCode: '400' },
        { statusCode: '500' },
      ],
    });

    const workflowResource = api.root.addResource('workflow');
    workflowResource.addMethod('POST', stepFunctionsIntegration, {
      apiKeyRequired: true,
      methodResponses: [
        { statusCode: '200' },
        { statusCode: '400' },
        { statusCode: '500' },
      ],
    });

    // Add status endpoint
    const statusResource = api.root.addResource('status');
    const statusIntegration = new apigateway.MockIntegration({
      integrationResponses: [
        {
          statusCode: '200',
          responseTemplates: {
            'application/json': JSON.stringify({
              status: 'healthy',
              timestamp: '$context.requestTime',
              version: '1.0.0',
            }),
          },
        },
      ],
      requestTemplates: {
        'application/json': '{"statusCode": 200}',
      },
    });

    statusResource.addMethod('GET', statusIntegration, {
      methodResponses: [{ statusCode: '200' }],
    });

    return api;
  }

  private createErrorNotificationTopic(encryptionKey: kms.Key): sns.Topic {
    const topic = new sns.Topic(this, 'ErrorNotificationTopic', {
      displayName: 'AI Agent Workflow Errors',
      masterKey: encryptionKey,
    });

    // Add email subscription if email is provided
    const notificationEmail = process.env.NOTIFICATION_EMAIL;
    if (notificationEmail) {
      topic.addSubscription(
        new snsSubscriptions.EmailSubscription(notificationEmail)
      );
    }

    return topic;
  }

  private createMonitoring(
    lambdaFunctions: {
      descriptionEnhancer: lambda.Function;
      codeCreator: lambda.Function;
      reviewRefine: lambda.Function;
      finalizer: lambda.Function;
    },
    stateMachine: stepfunctions.StateMachine
  ): void {

    // Create CloudWatch alarms for Lambda functions
    Object.entries(lambdaFunctions).forEach(([name, func]) => {
      // Error rate alarm
      func.metricErrors({
        period: cdk.Duration.minutes(5),
      }).createAlarm(this, `${name}ErrorAlarm`, {
        threshold: 5,
        evaluationPeriods: 2,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      });

      // Duration alarm
      func.metricDuration({
        period: cdk.Duration.minutes(5),
      }).createAlarm(this, `${name}DurationAlarm`, {
        threshold: cdk.Duration.minutes(10).toMilliseconds(),
        evaluationPeriods: 2,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      });

      // Throttle alarm
      func.metricThrottles({
        period: cdk.Duration.minutes(5),
      }).createAlarm(this, `${name}ThrottleAlarm`, {
        threshold: 1,
        evaluationPeriods: 1,
        treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      });
    });

    // Create alarms for Step Functions
    stateMachine.metricFailed({
      period: cdk.Duration.minutes(5),
    }).createAlarm(this, 'StateMachineFailedAlarm', {
      threshold: 1,
      evaluationPeriods: 1,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });

    // Create alarms for DynamoDB
    this.workflowTable.metricThrottledRequests({
      period: cdk.Duration.minutes(5),
    }).createAlarm(this, 'DynamoDBThrottleAlarm', {
      threshold: 1,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
    });
  }

  private createOutputs(): void {
    new cdk.CfnOutput(this, 'ApiEndpoint', {
      value: this.api.url,
      description: 'API Gateway endpoint URL',
    });

    new cdk.CfnOutput(this, 'StateMachineArn', {
      value: this.stateMachine.stateMachineArn,
      description: 'Step Functions state machine ARN',
    });

    new cdk.CfnOutput(this, 'WorkflowTableName', {
      value: this.workflowTable.tableName,
      description: 'DynamoDB table name',
    });

    new cdk.CfnOutput(this, 'ArtifactsBucketName', {
      value: this.artifactsBucket.bucketName,
      description: 'S3 bucket name for artifacts',
    });
  }
}
